/* eslint-disable @typescript-eslint/explicit-function-return-type */
/** @type {import('next').NextConfig} */

/**
 * Helper function to get required environment variables
 * @param {string} envVariable - The name of the environment variable
 * @returns {string} The value of the environment variable
 * @throws {Error} If the variable is not defined in production
 */
function required(envVariable) {
  const value = process.env[envVariable];
  
  // During development, provide a fallback value
  if (process.env.NODE_ENV === "development") {
    return value || "development-placeholder";
  }
  
  if (value === undefined || value === "") {
    throw new Error(`Environment variable ${envVariable} is not defined`);
  }
  
  return value;
}

/**
 * Helper function to get optional environment variables
 * @param {string} envVariable - The name of the environment variable
 * @returns {string|undefined} The value of the environment variable or undefined
 */
function optional(envVariable) {
  return process.env[envVariable];
}

/**
 * Next.js 15 configuration with Turbopack
 */
const nextConfig = {
  // Core settings
  reactStrictMode: true,
  output: "standalone", // Optimized for production builds
  basePath: process.env.RUBICON_ADMIN_BASE_URL || "",
  
  // Turbopack configuration (Next.js 15)
  turbopack: {
    // Memory limit for development (4GB - adjust based on your system)
    memoryLimit: 4000000000, // 4GB in bytes
    
    // Module resolution aliases (similar to webpack resolve.alias)
    // resolveAlias: {
    //   // Add any custom aliases you might need
    //   '@': './src',
    //   '@/components': './src/components',
    //   '@/lib': './src/lib',
    //   '@/utils': './src/utils',
    //   // Add any specific aliases your project needs
    // },
    
    // File extensions resolution order
    resolveExtensions: [
      '.tsx',
      '.ts', 
      '.jsx', 
      '.js',
      '.mjs',
      '.json'
    ],
    
    // Custom loader rules (if needed)
    rules: {},
    
    // Module ID generation strategy
    moduleIds: 'deterministic', // Better for production consistency
    
    // Tree shaking configuration
    treeShaking: true,
  },
  
  // Cleaned up experimental settings for Next.js 15
  experimental: {},
  
  // Image optimization configuration
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*.s3.amazonaws.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "*s3.*.amazonaws.com",
        pathname: "**",
      },
    ],
  },
  
  // Package transpilation for specific dependencies
  transpilePackages: ["echarts/core.js"],
  
  // Environment variables
  env: {
    // Public client-side variables
    NEXT_PUBLIC_BASE_URL: optional("RUBICON_ADMIN_BASE_URL") ?? "/",
    NEXT_PUBLIC_API_BASE_URL: required("RUBICON_ADMIN_API_BASE_URL"),
    NEXT_PUBLIC_AUTH_URL: required("RUBICON_ADMIN_AUTH_URL"),
    NEXT_PUBLIC_BLOB_STORAGE: required("RUBICON_ADMIN_STORAGE_BASE_URL"),
    NEXT_PUBLIC_GOOGLE_CLIENT_ID: required("RUBICON_ADMIN_GOOGLE_CLIENT_ID"),
    NEXT_PUBLIC_APP_NAME: required("RUBICON_ADMIN_APP_NAME"),
    NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT: required("RUBICON_ADMIN_DEPLOYMENT_ENVIRONMENT"),
    
    // Default configurations with fallbacks
    NEXT_PUBLIC_ADMIN_ORGANIZATION: 
      optional("RUBICON_ALL_RUBICON_MASTER_ORGANIZATION") ?? "892ad7c3-d962-4a03-848d-b2ea4a97e4c8",
    NEXT_PUBLIC_ADMIN_INACTIVITY_TIMEOUT: 
      optional("RUBICON_ADMIN_INACTIVITY_TIMEOUT") ?? "1800000",
    NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT:
      optional("RUBICON_ALL_PORTFOLIO_DEFAULT") ?? "e2031c05-98f0-4c63-a6e0-196c70647d3a",
    NEXT_PUBLIC_ADMIN_SERVER_PAGINATION_LIMIT: 
      optional("RUBICON_FE_LIMIT_DEFAULT") ?? "1000",
    
    // Build information
    NEXT_PUBLIC_GIT_COMMIT_HASH: optional("GIT_COMMIT_HASH") ?? "",
    NEXT_PUBLIC_GIT_COMMIT_BRANCH: optional("GIT_COMMIT_BRANCH") ?? "",
    
    // Server-side only variables
    RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_SCIENCE: 
      required("RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_SCIENCE"),
    RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_TRADING: 
      required("RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_TRADING"),
  },
  
  // Webpack configuration (still used for production builds)
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Your existing webpack customizations go here
    // This will only apply to production builds (when not using --turbo flag)
    
    return config;
  },
};

module.exports = nextConfig;