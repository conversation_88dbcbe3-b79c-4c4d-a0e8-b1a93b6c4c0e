# TypeScript Errors in App Folder

## Summary
Total: 16 errors in 7 files

### Error Categories:

1. **Component-level Issues (8 errors)** - Same as components folder
   - Generic table hook issues (4 errors)
   - Form hook type mismatch (1 error) 
   - Navigation interceptor issues (3 errors)

2. **App-specific Issues (8 errors)** - New issues in app folder
   - Type inference problems (5 errors)
   - Missing hook properties (3 errors)

## App-Specific Errors:

### src/app/[level-one-nav-page]/forward-delivery/components/line-tems.tsx (5 errors)
**Lines 237-259:** Type inference and property access issues
```typescript
// Error: Type 'string' is not assignable to type 'never'
keys: ["id", "name", "ownerAllocationsByAssetType"],

// Error: Property 'name' does not exist on type 'Partial<unknown>'
label: (entry) => entry.name,
value: (entry) => entry.id,

// Error: Property 'ownerAllocationsByAssetType' does not exist on type 'Partial<unknown>'
const allocsByAssetType: AssetTypeGroupedAllocationResponse[] = entry?.ownerAllocationsByAssetType || [];
```

**Issue:** Generic type inference failing, resulting in `Partial<unknown>` instead of proper type
**Solution:** Add explicit type annotations or improve type inference

### src/app/[level-one-nav-page]/transactions/components/trade/form.tsx (1 error)
**Line 1009:** Missing property in component props
```typescript
// Error: Property 'smartSetValue' does not exist on type 'IntrinsicAttributes & CounterpartiesProps'
smartSetValue={smartSetValue}
```

**Issue:** Component interface doesn't include `smartSetValue` property
**Solution:** Update component interface or remove unused prop

### src/app/market-intelligence/components/market-intelligence-item.tsx (1 error)
### src/app/market-intelligence/components/reports-list.tsx (1 error)
**Lines 30 & 59:** Missing hook property
```typescript
// Error: Property 'pushOnToPath' does not exist on type 'UseNavigationReturn'
const { pushOnToPath } = useNavigation();
```

**Issue:** Hook interface doesn't include `pushOnToPath` method
**Solution:** Check hook implementation or use correct navigation method

## Component Issues (Inherited from components folder):
- Generic table row actions type guards (4 errors)
- Form hook type mismatch (1 error)
- Navigation interceptor interface (3 errors)

## Priority Fix Order:
1. **High Priority - App-specific issues (8 errors):**
   - Fix type inference in forward-delivery component (5 errors)
   - Fix navigation hook interface (2 errors)
   - Fix component prop interface (1 error)

2. **Medium Priority - Component issues (8 errors):**
   - These are shared with components folder and should be fixed there first

## Next Steps:
1. Focus on app-specific issues first
2. Fix type inference problems with explicit typing
3. Update hook interfaces to match implementations
4. Fix component prop interfaces
5. Re-run type check to verify fixes

## Notes:
- App folder includes components folder, so fixing components will reduce app errors
- Some errors may be related to recent Next.js 15 migration
- Type inference issues suggest generic constraints may need adjustment
