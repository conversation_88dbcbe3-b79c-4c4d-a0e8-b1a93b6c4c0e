{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // Update to match Next.js 15 internal settings
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext",
      "ES2022"
    ],
    
    // Core settings for type checking
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    
    // Performance optimizations for type checking
    "incremental": true,
    "tsBuildInfoFile": "./.tsbuildinfo-typecheck",
    
    // Decorators (from your original config)
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "strictPropertyInitialization": false,
    "downlevelIteration": true,
    
    // Path resolution - keep all your existing aliases
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@app/*": ["./src/app/*"],
      "@components/*": ["./src/components/*"],
      "@constants/*": ["./src/constants/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@models/*": ["./src/models/*"],
      "@providers/*": ["./src/providers/*"],
      "@utils/*": ["./src/utils/*"],
      "@assets/*": ["./public/*"]
    },
    
    // Additional type checking optimizations
    "verbatimModuleSyntax": true,
  },
  
  // Optimized includes for your project structure
  "include": [
    "next-env.d.ts",
    "src/**/*.ts",
    "src/**/*.tsx",
    ".next/types/**/*.ts",
    "types/**/*.ts"
  ],
  
  // Exclude unnecessary files for faster type checking
  "exclude": [
    "node_modules",
    ".next",
    "out",
    "dist",
    "build",
    ".tsbuildinfo*",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx",
    "cypress",
    "playwright",
    "__tests__"
  ],
  
  // Remove plugins for direct tsc usage
  "plugins": []
}