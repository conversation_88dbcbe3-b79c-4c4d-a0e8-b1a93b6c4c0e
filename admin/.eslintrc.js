module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true,
  },
  ignorePatterns: [".eslintrc.js", ".next", "node_modules", "out", "*.d.ts"],
  // Only include next/core-web-vitals and essential typescript config
  extends: [
    "next/core-web-vitals", // This already includes eslint:recommended, React, and react-hooks
    "plugin:@typescript-eslint/recommended",
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  settings: {
    next: {
      rootDir: ".",
    },
    "import/resolver": {
      typescript: {},
      node: {
        extensions: [".js", ".jsx", ".ts", ".tsx"],
      },
    },
  },
  rules: {
    "@typescript-eslint/interface-name-prefix": "off",
    "@typescript-eslint/explicit-function-return-type": "error",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "import/no-unresolved": 2,
    "import/no-unused-modules": 2,
    "import/no-commonjs": 2,
    "import/extensions": [2, { js: "always", ts: "never" }],
    "import/no-namespace": 2,
    "import/no-useless-path-segments": 2,
    "comma-dangle": [2, "always-multiline"],
    "no-console": ["error", { allow: ["warn", "error"] }],
    "@next/next/no-html-link-for-pages": "off",
    "@next/next/no-img-element": "off",
    "@next/next/no-page-custom-font": "off",
    "no-unused-expressions": [
      "error",
      {
        "allowShortCircuit": true,
        "allowTernary": true,
        "allowTaggedTemplates": true
      }
    ],
  },
};