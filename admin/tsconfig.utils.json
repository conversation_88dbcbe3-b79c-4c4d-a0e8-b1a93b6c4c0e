{"extends": "./tsconfig.minimal.json", "compilerOptions": {"paths": {"@/*": ["./src/*"], "@app/*": ["./src/app/*"], "@components/*": ["./src/components/*"], "@constants/*": ["./src/constants/*"], "@hooks/*": ["./src/hooks/*"], "@models/*": ["./src/models/*"], "@providers/*": ["./src/providers/*"], "@utils/*": ["./src/utils/*"], "@assets/*": ["./public/*"]}}, "include": ["next-env.d.ts", "src/constants/**/*.ts", "src/utils/**/*.ts", "src/utils/**/*.tsx"]}