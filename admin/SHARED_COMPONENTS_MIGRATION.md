# Rubicon Admin Shared Components Migration Plan

This document outlines the duplicated functionality between the frontend-shared library and the rubicon-admin module, and provides a plan for migrating to the shared implementations.

## Table of Contents

1. [Overview](#1-overview)
2. [Already Migrated Components](#2-already-migrated-components)
3. [Components to Migrate](#3-components-to-migrate)
4. [Formatters to Migrate](#4-formatters-to-migrate)
5. [Validators to Migrate](#5-validators-to-migrate)
6. [Utility Functions to Migrate](#6-utility-functions-to-migrate)
7. [Hooks to Migrate](#7-hooks-to-migrate)
8. [Migration Strategy](#8-migration-strategy)
9. [Testing Strategy](#9-testing-strategy)
10. [Rollback Plan](#10-rollback-plan)

## 1. Overview

As part of the Next.js 15 migration, we're also working to reduce duplication between the rubicon-admin module and the frontend-shared library. This will:

- Reduce maintenance burden
- Ensure consistent behavior across applications
- Make future upgrades easier
- Improve code quality and reusability

This document identifies duplicated functionality and outlines a plan for migrating to the shared implementations.

## 2. Already Migrated Components

The following components have been fully migrated to use the frontend-shared library directly:

### Components
- **Maybe Component** ✅
  - Frontend-shared: `packages/core/src/components/maybe.tsx`
  - Admin: Completely removed wrapper and directly using the shared version

### Hooks
- **useRequest** ✅
  - Frontend-shared: `packages/core/src/hooks/request/use-request.ts`
  - Admin: Completely removed wrapper and directly using the shared version

- **useTriggerRequest** ✅
  - Frontend-shared: `packages/core/src/hooks/request/use-trigger-request.ts`
  - Admin: Completely removed wrapper and directly using the shared version

- **useDataPolling** ✅
  - Frontend-shared: `packages/core/src/hooks/use-data-polling.ts`
  - Admin: Completely removed wrapper and directly using the shared version

- **useBatchRequest** ✅
  - Frontend-shared: `packages/core/src/hooks/request/use-batch-request.ts`
  - Admin: Completely removed wrapper (was unused in the codebase)

- **useBatchTriggerRequest** ✅
  - Frontend-shared: `packages/core/src/hooks/request/use-trigger-batch-request.ts`
  - Admin: Completely removed wrapper (was unused in the codebase)

## 3. Components to Migrate

The following UI components in the admin module have potential equivalents in the frontend-shared library or should be migrated to it:

### UI Components

- **Custom Buttons**
  - Admin: `src/components/ui/custom-button/custom-button.tsx`
  - Admin: `src/components/ui/back-button/back-button.tsx`
  - Admin: `src/components/ui/action-button/action-button-enhanced.tsx`

- **Status Indicators**
  - Admin: `src/components/ui/is-admin/is-admin.tsx`

- **Dialog Components**
  - Admin: `src/components/ui/generic-dialog/generic-dialog.tsx`

- **Table Components**
  - Admin: `src/components/ui/generic-table/*`

## 4. Formatters to Migrate

The following formatters in the admin module are duplicated in the frontend-shared library or should be migrated to it:

### Currency and Number Formatters

- **currencyFormat**
  - Frontend-shared: `packages/core/src/utilities/currency-format.ts`
  - Admin: `src/utils/formatters/currencyFormat.ts`
  - Web: `src/utilities/currency.ts`

- **numberFormat / integerFormat**
  - Frontend-shared: `packages/core/src/utilities/number-format.ts`
  - Admin: `src/utils/formatters/integerFormat.ts`
  - Admin: `src/utils/formatters/decimalFormat.ts`

### Date Formatters

- **dateFormatters**
  - Admin has multiple date formatters that could be consolidated:
    - `src/utils/formatters/dateFormatter.ts`
    - `src/utils/formatters/utcDateFormatter.ts`
    - `src/utils/formatters/estDateFormatter.ts`
    - `src/utils/formatters/localTimeDateFormatter.ts`
    - `src/utils/formatters/dateRangeFormatter.ts`

## 5. Validators to Migrate

The following validators in the admin module are duplicated in the frontend-shared library or should be migrated to it:

### Number Validators

- **Number Validators**
  - Frontend-shared: `packages/core/src/utilities/number-validators.ts`
  - Admin: `src/components/forward-delivery/utilities/number-validators.ts` (has a comment "should move this to shared fe lib")

### String Validators

- **String Validators**
  - Frontend-shared: `packages/core/src/utilities/string-validators.ts`
  - Admin: Various custom validators scattered throughout the codebase

### Form Validators

- **Form Validation Decorators**
  - Frontend-shared has validation decorators
  - Admin: `src/components/marketing-agreements/utilities/fee-structure-validator.ts`
  - Admin: Various other custom validators

## 6. Utility Functions to Migrate

The following utility functions in the admin module are duplicated in the frontend-shared library or should be migrated to it:

### URL and Query Utilities

- **generateQueryParams**
  - Frontend-shared: Likely has this functionality
  - Admin: `src/app/lib/server.ts`
  - Web: `src/utilities/fetch.ts`
  - Mobile: `src/utilities/fetch.ts`

### Date Utilities

- **compareDateTime**
  - Frontend-shared: `packages/core/src/utilities/compare-date-time.ts`
  - Admin: Likely has similar functionality in date utilities

### Number Utilities

- **NoNumberFormat / toNumber**
  - Frontend-shared: Has `toNumber` utility
  - Admin: `src/utils/formatters/no-number-format.ts`

### Storage Utilities

- **LocalStorage Utilities**
  - Frontend-shared: `packages/core/src/utilities/local-storage.ts`
  - Web: `src/app/guided-tour/utilities.ts` (has custom localStorage functions)

## 7. Hooks to Migrate

The following hooks in the admin module are duplicated in the frontend-shared library or should be migrated to it:

### Router Hooks

- **Router Hooks**
  - Admin: `src/utils/hooks/router-hooks.tsx`
  - These could potentially be moved to frontend-shared for reuse across projects

### Performance Hooks

- **Performance Hooks**
  - Admin: `src/utils/hooks/usePerformantEffect.ts`
  - Admin: `src/utils/hooks/usePerfomantState.ts`
  - These could be moved to frontend-shared

### File Upload Hooks

- **File Upload Utilities**
  - Frontend-shared: `packages/core/src/hooks/use-file-upload.ts`
  - Admin: `src/components/ui/uploader/utils/hook.ts`
  - Admin: `src/utils/hooks/useHeadlessDownloader.ts`

### Network Hooks

- **Network Status Utilities**
  - Frontend-shared: `packages/core/src/hooks/use-network-status.ts`
  - Potentially duplicated in admin

## 8. Migration Strategy

For each duplicated component or utility, follow these steps:

1. **Identify Usage**: Find all places in the codebase where the custom implementation is used
2. **Create Wrapper**: Create a wrapper around the frontend-shared implementation that maintains the same API
3. **Update Imports**: Update imports to use the wrapper
4. **Test**: Ensure functionality remains the same
5. **Remove Custom Implementation**: Once all usages have been migrated, remove the custom implementation

### Example Migration Process for a Formatter

```tsx
// Step 1: Create a wrapper around the frontend-shared implementation
// admin/src/utils/formatters/currencyFormat.ts

import { currencyFormat as sharedCurrencyFormat } from "@rubiconcarbon/frontend-shared";

const currencyFormat = (value: number | null | undefined, minimumFractionDigits: number = 2): string | null => {
  if (value === null || value === undefined) {
    return null;
  }

  return sharedCurrencyFormat(value, {
    decimalPlaces: minimumFractionDigits,
  });
};

export default currencyFormat;
```

### Prioritization

Migrate components in this order:

1. Formatters (high usage, low complexity)
2. Utility Functions (high usage, low complexity)
3. Validators (medium usage, medium complexity)
4. Hooks (medium usage, high complexity)
5. UI Components (high usage, high complexity)

## 9. Testing Strategy

For each migrated component or utility:

1. **Unit Tests**: Ensure the wrapper behaves identically to the original implementation
2. **Integration Tests**: Test the component in context to ensure it works with other components
3. **Visual Regression Tests**: For UI components, ensure they look the same
4. **Performance Tests**: Ensure performance is not degraded

## 10. Rollback Plan

If issues arise during migration:

1. **Revert to Custom Implementation**: Temporarily revert to the custom implementation
2. **Fix Issues in Wrapper**: Address any issues in the wrapper
3. **Try Again**: Once issues are fixed, try the migration again

## Migration Progress Tracking

| Category | Item | Status | Notes |
|----------|------|--------|-------|
| **Components** | Maybe | ✅ | Completed - Direct import from frontend-shared |
| **Hooks** | useRequest | ✅ | Completed - Direct import from frontend-shared |
| **Hooks** | useTriggerRequest | ✅ | Completed - Direct import from frontend-shared |
| **Hooks** | useDataPolling | ✅ | Completed - Direct import from frontend-shared |
| **Hooks** | useBatchRequest | ✅ | Completed - Removed (unused in codebase) |
| **Hooks** | useBatchTriggerRequest | ✅ | Completed - Removed (unused in codebase) |
| **Formatters** | currencyFormat | ⏸️ | Postponed - Requires significant analysis and potential enhancements to frontend-shared |
| **Formatters** | numberFormat | ⏸️ | Postponed - Requires significant analysis and potential enhancements to frontend-shared |
| **Formatters** | dateFormatters | ⏸️ | Postponed - May need to be implemented in frontend-shared first |
| **Validators** | Number Validators | 📝 | Planned |
| **Validators** | String Validators | 📝 | Planned |
| **Utilities** | generateQueryParams | ⏸️ | Postponed - Server component compatibility issues require separate implementations |
| **Utilities** | compareDateTime | ✅ | Completed - Already using frontend-shared directly |
| **Hooks** | Router Hooks | 📝 | Planned |
| **Hooks** | Performance Hooks | 📝 | Planned |
| **Components** | Custom Buttons | 📝 | Planned |
| **Components** | Status Indicators | 📝 | Planned |

## Conclusion

Migrating to the frontend-shared library will reduce duplication, improve maintainability, and ensure consistent behavior across applications. By following this plan, we can systematically replace custom implementations with shared ones while maintaining backward compatibility.
