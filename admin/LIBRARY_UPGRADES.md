# Rubicon Admin Library Upgrades

This document outlines the plan for upgrading key libraries in the rubicon-admin module to work better with Next.js 15 and React 19. These upgrades are separate from the overall Next.js migration and can be performed independently.

## Table of Contents

1. [Overview](#1-overview)
2. [Material UI Upgrade (v5 to v6)](#2-material-ui-upgrade-v5-to-v6)
3. [React Hook Form Upgrade (v7 to v8)](#3-react-hook-form-upgrade-v7-to-v8)
4. [Other Library Upgrades](#4-other-library-upgrades)
5. [Implementation Strategy](#5-implementation-strategy)
6. [Testing Strategy](#6-testing-strategy)
7. [Rollback Plan](#7-rollback-plan)

## 1. Overview

### Current Library Versions

| Library | Current Version | Target Version |
|---------|----------------|---------------|
| Material UI | 5.11.8 | 6.x (latest) |
| MUI Lab | 5.0.0-alpha.137 | 6.x (latest) |
| MUI X Date Pickers | 6.5.0 | 7.x (latest) |
| React Hook Form | 7.43.5 | 8.x (latest) |
| @hookform/resolvers | 2.9.11 | 3.x (latest) |
| SWR | 2.0.3 | 2.x (latest) |
| Notistack | 3.0.1 | 3.x (latest) |

### Benefits of Upgrading

- **Better compatibility** with Next.js 15 and React 19
- **Performance improvements** from newer library versions
- **Bug fixes** and security patches
- **New features** and improved developer experience
- **Future-proofing** the application

## 2. Material UI Upgrade (v5 to v6)

Material UI v6 introduces Pigment CSS, a zero-runtime CSS-in-JS styling engine to replace Emotion and styled-components.

### Key Changes

- New styling engine (Pigment CSS)
- Removal of styled-components support
- New theme structure
- Updated component APIs
- Better server-side rendering support

### Upgrade Steps

#### 2.1 Preparation

1. Create a new branch for the Material UI upgrade
   ```bash
   git checkout -b feature/material-ui-v6-upgrade
   ```

2. Update Material UI dependencies
   ```bash
   cd admin
   npm install @mui/material@latest @mui/icons-material@latest @mui/lab@latest @mui/x-date-pickers@latest @emotion/react@latest @emotion/styled@latest
   ```

3. Install the Next.js integration package for Material UI v6
   ```bash
   npm install @mui/material-nextjs
   ```

#### 2.2 Theme Provider Migration

1. Update the theme provider to use the new Pigment CSS engine and Next.js integration:

   ```tsx
   // admin/src/providers/theme-provider.tsx
   import { createTheme, ThemeProvider as MuiThemeProvider } from "@mui/material/styles";
   import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
   import palette from "@/components/ui/theme/palette";
   import components from "@/components/ui/theme/components";

   type ThemeProviderProps = {
     children: React.ReactNode;
   };

   function ThemeProvider(props: ThemeProviderProps): JSX.Element {
     const { children } = props;
     const theme = createTheme({
       palette,
       components,
     });

     return (
       <AppRouterCacheProvider>
         <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>
       </AppRouterCacheProvider>
     );
   }

   export default ThemeProvider;
   ```

2. Update the providers.tsx file to remove StyledEngineProvider (no longer needed in v6)

   ```tsx
   // admin/src/app/providers.tsx
   // Remove this import:
   // import { StyledEngineProvider } from "@mui/material/styles";

   // And replace this:
   // <StyledEngineProvider injectFirst>
   //   <ThemeProvider>
   //     ...
   //   </ThemeProvider>
   // </StyledEngineProvider>

   // With just:
   <ThemeProvider>
     ...
   </ThemeProvider>
   ```

#### 2.3 Component Migration

1. Update custom components that use Material UI styling:
   - `src/components/ui/custom-button/custom-button.tsx`
   - `src/components/ui/mat-icon/mat-icon.tsx`
   - `src/components/ui/generic-dialog/generic-dialog.tsx`
   - Any components using styled components or makeStyles

2. Update theme components.ts to use the new styling approach:
   ```tsx
   // admin/src/components/ui/theme/components.ts
   // Update styleOverrides to use the new format if needed
   ```

3. Check for deprecated APIs and update accordingly:
   - Some component props may have changed
   - Some styling approaches may need updates

#### 2.4 Next.js Configuration

Update next.config.js for better Material UI integration:

```js
// admin/next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // existing config...

  // Optimize Material UI imports
  modularizeImports: {
    "@mui/material": {
      transform: "@mui/material/{{member}}",
    },
    "@mui/icons-material": {
      transform: "@mui/icons-material/{{member}}",
    },
    "@mui/lab": {
      transform: "@mui/lab/{{member}}",
    },
  },

  // other config...
};

module.exports = nextConfig;
```

## 3. React Hook Form Upgrade (v7 to v8)

React Hook Form v8 introduces improved TypeScript support and performance optimizations.

### Key Changes

- Improved TypeScript types
- Performance optimizations
- Some API changes
- Better integration with React 18+ features

### Upgrade Steps

#### 3.1 Preparation

1. Create a new branch for the React Hook Form upgrade
   ```bash
   git checkout -b feature/react-hook-form-v8-upgrade
   ```

2. Update React Hook Form and resolver dependencies
   ```bash
   cd admin
   npm install react-hook-form@latest @hookform/resolvers@latest
   ```

#### 3.2 Form Migration

1. Update form implementations:

   ```tsx
   // Before (v7)
   const { control, formState, getValues, setValue, watch, handleSubmit } = useForm({
     mode: "onChange",
     resolver: SingleForwardLineItemFormResolver,
     defaultValues: row,
   });

   // After (v8)
   const { control, formState, getValues, setValue, watch, handleSubmit } = useForm({
     mode: "onChange",
     resolver: SingleForwardLineItemFormResolver,
     defaultValues: row,
     // New v8 options if needed
   });
   ```

2. Update Controller components:

   ```tsx
   // Check for any changes needed in Controller usage
   <Controller
     name="assets.0.projectVintage.project.id"
     control={control}
     render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
       // Component implementation (may need updates)
     }}
   />
   ```

3. Update useFieldArray implementations:

   ```tsx
   // Check for any changes needed in useFieldArray usage
   const {
     fields: lineItems,
     prepend,
     append,
     remove,
   } = useFieldArray({
     control,
     name: "trade.counterparties",
   });
   ```

#### 3.3 Resolver Migration

1. Update class-validator resolver usage:

   ```tsx
   // Before
   import { classValidatorResolver } from "@hookform/resolvers/class-validator";
   
   const resolver = classValidatorResolver(MyModel);
   
   // After (check for any API changes)
   import { classValidatorResolver } from "@hookform/resolvers/class-validator";
   
   const resolver = classValidatorResolver(MyModel);
   ```

2. Consider migrating to zod for better TypeScript integration:

   ```tsx
   // Using zod instead of class-validator
   import { zodResolver } from "@hookform/resolvers/zod";
   import { z } from "zod";
   
   const schema = z.object({
     // Define schema
   });
   
   const resolver = zodResolver(schema);
   ```

## 4. Other Library Upgrades

### 4.1 SWR Upgrade

1. Update SWR to the latest version:
   ```bash
   cd admin
   npm install swr@latest
   ```

2. Update custom hooks that use SWR:
   - `src/utils/hooks/useProjectPricingRequest.ts`
   - Any other custom hooks using SWR

3. Check for API changes:
   ```tsx
   // Before
   const { data, error, isLoading, isValidating, mutate } = useSWR<VintagePricingResponse[]>(
     enable ? key : null,
     fetcher,
     swrOptions,
   );
   
   // After (check for any API changes)
   const { data, error, isLoading, isValidating, mutate } = useSWR<VintagePricingResponse[]>(
     enable ? key : null,
     fetcher,
     swrOptions,
   );
   ```

### 4.2 Notistack Upgrade

1. Update Notistack to the latest version:
   ```bash
   cd admin
   npm install notistack@latest
   ```

2. Update the SnackbarProvider implementation:
   - `src/providers/themed-snackbar-provider.tsx`

### 4.3 Date Libraries Consolidation

1. Consider consolidating date libraries:
   - Currently using both dayjs and date-fns
   - Standardize on one library (preferably dayjs since it's used with MUI Date Pickers)

2. Update date formatting utilities:
   - `src/utils/formatters/dateFormatter.ts`
   - `src/utils/formatters/utcDateFormatter.ts`
   - `src/utils/formatters/estDateFormatter.ts`
   - `src/utils/formatters/localTimeDateFormatter.ts`
   - `src/utils/formatters/dateRangeFormatter.ts`

## 5. Implementation Strategy

### 5.1 Phased Approach

Implement the upgrades in phases to minimize risk:

1. **Phase 1: Material UI Upgrade**
   - Update dependencies
   - Migrate theme provider
   - Update components
   - Test and validate

2. **Phase 2: React Hook Form Upgrade**
   - Update dependencies
   - Migrate form implementations
   - Test and validate

3. **Phase 3: Other Library Upgrades**
   - Update SWR, Notistack, and other libraries
   - Test and validate

### 5.2 Branch Strategy

Use feature branches for each upgrade:

1. Create a base branch for library upgrades:
   ```bash
   git checkout -b feature/library-upgrades
   ```

2. Create feature branches for each library:
   ```bash
   git checkout -b feature/material-ui-v6-upgrade feature/library-upgrades
   git checkout -b feature/react-hook-form-v8-upgrade feature/library-upgrades
   git checkout -b feature/other-libraries-upgrade feature/library-upgrades
   ```

3. Merge each feature branch back to the base branch after testing:
   ```bash
   git checkout feature/library-upgrades
   git merge feature/material-ui-v6-upgrade
   ```

## 6. Testing Strategy

### 6.1 Unit Testing

1. Run existing unit tests after each library upgrade
2. Update tests that fail due to API changes
3. Add new tests for new functionality

### 6.2 Integration Testing

1. Test components that use the upgraded libraries
2. Test interactions between different libraries
3. Test server-side rendering with the new libraries

### 6.3 Manual Testing

1. Test all forms and UI components
2. Test error handling and edge cases
3. Test performance and responsiveness

### 6.4 Cross-Browser Testing

1. Test in Chrome, Firefox, Safari, and Edge
2. Test on different devices and screen sizes

## 7. Rollback Plan

If significant issues are encountered during the upgrade process:

### 7.1 Material UI Rollback

1. Revert to v5:
   ```bash
   npm install @mui/material@5.11.8 @mui/icons-material@5.11.0 @mui/lab@5.0.0-alpha.137 @mui/x-date-pickers@6.5.0
   ```
2. Remove @mui/material-nextjs
3. Restore the original theme provider

### 7.2 React Hook Form Rollback

1. Revert to v7:
   ```bash
   npm install react-hook-form@7.43.5 @hookform/resolvers@2.9.11
   ```
2. Restore original form implementations

### 7.3 Other Libraries Rollback

1. Revert to original versions:
   ```bash
   npm install swr@2.0.3 notistack@3.0.1
   ```
2. Restore original implementations

## Conclusion

This upgrade plan provides a structured approach to updating Material UI, React Hook Form, and other libraries in the rubicon-admin module to work better with Next.js 15 and React 19. By following this plan, you can minimize disruption and ensure a smooth transition to the latest versions of these libraries.

## Progress Tracking

| Library | Status | Notes |
|---------|--------|-------|
| Material UI | 📝 Planned | |
| MUI Lab | 📝 Planned | |
| MUI X Date Pickers | 📝 Planned | |
| React Hook Form | 📝 Planned | |
| @hookform/resolvers | 📝 Planned | |
| SWR | 📝 Planned | |
| Notistack | 📝 Planned | |
