# TypeScript Errors in Utils Folder - Updated

## Summary
Total: 92 errors in 8 files

### Error Categories:

1. **Class Validator Decorator Issues (72 errors)** - Most critical
   - Error: `Unable to resolve signature of property decorator when called as an expression`
   - Affects: @IsNotEmpty, @ValidateNested, @Type, @Transform, @ValidateIf, etc.
   - Root cause: Decorator compatibility with TypeScript 5.8+ and experimentalDecorators

2. **Missing Modern JavaScript Features (6 errors)**
   - `replaceAll` method (4 errors) - need ES2021+
   - `Object.hasOwn` method (2 errors) - need ES2022+

3. **Missing Properties on PrimitiveTypeBook (12 errors)**
   - Properties: priceVintagesAllocated, priceVintagesPendingBuy, priceVintagesPendingSell, amountVintagesAllocated, amountVintagesPendingBuy, amountVintagesPendingSell

## Current Configuration Issue:
- Main tsconfig.json has es2022 lib but utils uses tsconfig.minimal.json with only ES2020
- Missing experimentalDecorators and emitDecoratorMetadata in minimal config

## Priority Fix Order:
1. ✅ Update tsconfig.minimal.json with proper lib and decorator settings
2. Fix class-validator decorator compatibility issues (72 errors - main issue)
3. Update PrimitiveTypeBook type definitions (12 errors)
4. Test all fixes

## Progress Update:
✅ 1. Updated tsconfig.minimal.json to include ES2022 and decorator metadata
✅ 2. Added reflect-metadata imports to all model files
✅ 3. Fixed all 72 class-validator decorator issues!

## Remaining Issues (13 errors):
Only missing properties on PrimitiveTypeBook type:
- priceVintagesAllocated
- priceVintagesPendingBuy
- priceVintagesPendingSell
- amountVintagesAllocated
- amountVintagesPendingBuy
- amountVintagesPendingSell

✅ 4. Added missing properties to PrimitiveTypeBook class with correct types
✅ 5. Fixed type mismatches (price properties as strings, amount properties as numbers)

## ✅ COMPLETED - All TypeScript errors in utils folder have been fixed!

**Final Results:**
- ✅ Fixed 72 class-validator decorator issues by adding reflect-metadata imports
- ✅ Fixed 6 modern JavaScript feature issues by updating tsconfig.minimal.json lib to ES2022
- ✅ Fixed 13 missing property issues by adding properties to PrimitiveTypeBook with correct types
- ✅ Total: 92 errors → 0 errors

**Changes Made:**
1. Updated `tsconfig.minimal.json` to include ES2022 lib and decorator metadata settings
2. Added `import "@utils/reflect-metadata"` to all model files
3. Added missing properties to `PrimitiveTypeBook` class with proper types:
   - Price properties as `string` (for decimal precision)
   - Amount properties as `number` (for integer values)

**Type Check Result:** ✅ PASSED - 0 errors
