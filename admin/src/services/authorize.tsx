import { PropsWithChildren } from "react";
import useAuth from "@/providers/auth-provider";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import NotAuthorizedErrorPage from "@/components/error/unauthorized-err-page";

interface AuthorizeProps {
  /**
   * if this is set to true, it will show page if at least one permission is avaiable.
   * it is up to the developer to further limit resources on that page.
   */
  partiallyAuthorize?: boolean;
  permissions: PermissionEnum[];
  unauthorizedPage?: JSX.Element;
}

export function Authorize({
  partiallyAuthorize = false,
  children,
  permissions,
  unauthorizedPage,
}: PropsWithChildren<AuthorizeProps>): JSX.Element {
  const { user: loginUser } = useAuth();

  const isAuthorized = loginUser?.[partiallyAuthorize ? "hasSomePermissions" : "hasPermissions"](permissions) || false;

  if (!isAuthorized) return unauthorizedPage ?? <NotAuthorizedErrorPage />;

  return <>{children}</>;
}

export function ShowIfAuthorized({
  children,
  permissions,
}: PropsWithChildren<Pick<AuthorizeProps, "permissions">>): JSX.Element {
  const { user: loginUser } = useAuth();

  const isAuthorized = loginUser?.hasPermissions(permissions) || false;

  return isAuthorized ? <>{children}</> : <></>;
}
