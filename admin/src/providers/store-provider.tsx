import { DEFAULT_BASKET_ID } from "@constants/constants";
import { AppStoreProvider, StoreContextType, createStoreContext } from "@rubiconcarbon/frontend-shared";
import { PropsWithChildren, useContext } from "react";

type LocalState = {
  navigation: {
    pinned: boolean;
    expanded: boolean;
    forceCollapse: boolean;
  };
};

type SessionState = object;

type EphemeralState = {
  transactions: {
    viewing: "transactions" | "sales" | "trades";
    searchTerm: string;
    pageNumber: number;
    rowsPerPage: number;
  };
  organizations: {
    viewing: "portal" | "counterparty";
  };
  customer: {
    viewing: "holdings" | "transactions" | "quotes" | "documents" | "users";
  };
  portfolio: { selected: string };
};

const InitialLocalState: LocalState = {
  navigation: {
    pinned: false,
    expanded: false,
    forceCollapse: false,
  },
};

const InitialSessionState: SessionState = {};

const InitialEphemeralState: EphemeralState = {
  transactions: {
    viewing: "transactions",
    searchTerm: "",
    pageNumber: 0,
    rowsPerPage: 100,
  },
  organizations: {
    viewing: "portal",
  },
  customer: {
    viewing: "holdings",
  },
  portfolio: {
    selected: DEFAULT_BASKET_ID,
  },
};

const StoreContext = createStoreContext<LocalState, SessionState, EphemeralState>(
  InitialLocalState,
  InitialSessionState,
  InitialEphemeralState,
);

const StoreProvider = ({ children }: PropsWithChildren): JSX.Element => {
  return (
    <AppStoreProvider
      persistentKey={`${window.location.hostname.replaceAll(".", "-")}-admin-app-state`}
      initialState={{
        local: InitialLocalState,
        session: InitialSessionState,
        ephemeral: InitialEphemeralState,
      }}
      StoreContext={StoreContext}
    >
      {children}
    </AppStoreProvider>
  );
};

export const useStoreProvider = (): StoreContextType<LocalState, SessionState, EphemeralState> =>
  useContext(StoreContext);

export default StoreProvider;
