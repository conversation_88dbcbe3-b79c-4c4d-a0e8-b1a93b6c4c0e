import { createTheme, ThemeProvider as MuiThemeProvider } from "@mui/material/styles";
import palette from "@components/ui/theme/palette";
import components from "@components/ui/theme//components";

type ThemeProviderProps = {
  children: React.ReactNode;
};

function ThemeProvider(props: ThemeProviderProps): JSX.Element {
  const { children } = props;
  const theme = createTheme({
    palette,
    components,
  });

  return <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>;
}

export default ThemeProvider;
