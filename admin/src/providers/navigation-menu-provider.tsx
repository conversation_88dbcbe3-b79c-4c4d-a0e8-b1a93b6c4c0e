"use client";

import { PropsWithChildren, ReactNode, createContext, useCallback, useContext, useMemo } from "react";
import {
  ShoppingBasketRounded,
  ForestRounded,
  WorkRounded,
  InsertChartRounded,
  NotificationAddRounded,
  HomeRounded,
  RssFeedRounded,
  TopicRounded,
  QueryStatsRounded,
  ReceiptRounded,
  Assessment,
} from "@mui/icons-material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { useFeatures } from "flagged";
import { useAppPathname } from "@hooks/router-hooks";
import useAuth from "@providers/auth-provider";
import { PORTFOLIOS_PAGE_LABEL, USERS_PAGE_LABEL } from "@constants/pages-labels";
import { Nullable } from "@rubiconcarbon/frontend-shared";

type Comparator = "equals" | "startsWith" | "endsWith" | "includes";

export type NavigationMenuItem = {
  type: "page" | "submenu";
  label: string;
  description?: ReactNode;
  headIcon?: ReactNode;
  expandIcon?: ReactNode;
  collapseIcon?: ReactNode;
  link: string;
  submenu?: NavigationMenuItem[];
  permissions: PermissionEnum[];
  flags?: string[];
  expanded?: boolean;
  expandable?: boolean;
  partiallyAuthorize?: boolean;

  /**
   * this property will be removed either when all scheduled features
   * are implemented or removed.
   * used to denote code-wise, what is done and what is yet to be done
   */
  hidden?: boolean;
};

type NavigationMenuContextData = {
  permissibleMenus: NavigationMenuItem[];
  activeMenuPaths: string[];
  getPermissableMenu: (
    menuLink: string,
    menus: NavigationMenuItem[],
    comparator?: Comparator,
  ) => NavigationMenuItem | null;
  isActiveMenu: (link: string) => boolean;
};

const MenuItems: NavigationMenuItem[] = [
  {
    type: "page",
    label: "Internal Platform",
    link: "/",
    headIcon: <HomeRounded />,
    permissions: [],
    expandable: false,
  },
  {
    type: "page",
    label: "My Positions",
    link: "/my-positions",
    permissions: [PermissionEnum.REPORTING_MARKET_DATA],
    headIcon: <Assessment />,
  },
  {
    type: "submenu",
    label: "Trading",
    description: "Buy, sell, and explore market data.",
    headIcon: <QueryStatsRounded />,
    link: "/trading",
    submenu: [
      {
        type: "page",
        label: "Market Data",
        description: "Rubicon inventory and market data analysis.",
        link: "/trading/market-data",
        permissions: [PermissionEnum.REPORTING_MARKET_DATA],
      },
      {
        type: "page",
        label: "Quotes",
        link: `/trading/quotes`,
        permissions: [PermissionEnum.QUOTES_READ],
      },
      {
        type: "page",
        label: "Transactions",
        link: "/trading/transactions",
        partiallyAuthorize: true,
        permissions: [PermissionEnum.CUSTOMER_SALES_READ, PermissionEnum.TRADES_READ],
      },
      {
        type: "page",
        label: "Bid/Ask",
        description: "Aggregated external market data.",
        link: "/trading/bid-ask",
        permissions: [PermissionEnum.REPORTING_MARKET_DATA],
      },
      {
        type: "page",
        label: "Forward Delivery",
        link: `/trading/forward-delivery`,
        permissions: [PermissionEnum.FORWARDS_READ],
      },
      {
        type: "page",
        label: "Marketing Agreements",
        link: `/trading/marketing-agreements`,
        permissions: [PermissionEnum.MARKETING_AGREEMENTS_READ],
      },
    ],
    permissions: [],
  },
  {
    type: "submenu",
    label: "Inventory Management",
    description: "Manage portfolios.",
    headIcon: <ShoppingBasketRounded />,
    link: "/inventory-management",
    submenu: [
      {
        type: "page",
        label: "Books",
        link: "/inventory-management/books",
        permissions: [PermissionEnum.BOOKS_READ],
      },
      {
        type: "page",
        label: "Basket Sandbox",
        link: "/inventory-management/basket-sanbox",
        permissions: [],
        hidden: true,
      },
      {
        type: "page",
        label: "Order Management",
        link: "/inventory-management/order-management",
        permissions: [],
        hidden: true,
      },
      {
        type: "page",
        label: PORTFOLIOS_PAGE_LABEL,
        link: `/inventory-management/portfolios`,
        permissions: [PermissionEnum.MODEL_PORTFOLIOS_READ],
      },
      {
        type: "page",
        label: "Reserves",
        link: `/inventory-management/reserves`,
        permissions: [PermissionEnum.RESERVES_READ],
      },
      {
        type: "page",
        label: "Portfolio Sandbox",
        link: `/inventory-management/portfolio-sandbox`,
        permissions: [PermissionEnum.MODEL_PORTFOLIOS_READ],
      },
      {
        type: "page",
        label: "Reconciliation",
        link: "/inventory-management/reconciliation",
        permissions: [PermissionEnum.REPORTING_RECON],
      },
    ],
    permissions: [],
  },
  {
    type: "page",
    label: "Retirements & Transfers",
    headIcon: <ReceiptRounded />,
    link: "/retirements",
    permissions: [PermissionEnum.RETIREMENTS_READ],
  },
  {
    type: "submenu",
    label: "Customer Management",
    description: "Administer customer accounts, manage users, and control access.",
    headIcon: <WorkRounded />,
    link: "/customer-management",
    submenu: [
      {
        type: "page",
        label: USERS_PAGE_LABEL,
        link: "/customer-management/users",
        permissions: [PermissionEnum.USERS_READ],
      },
      {
        type: "page",
        label: "Organizations",
        link: "/customer-management/organizations",
        partiallyAuthorize: true,
        permissions: [PermissionEnum.ORGANIZATIONS_READ, PermissionEnum.COUNTERPARTIES_READ],
      },
      {
        type: "page",
        label: "User Activity",
        link: "/customer-management/user-activity",
        permissions: [PermissionEnum.USERS_READ],
      },
    ],
    permissions: [],
  },
  {
    type: "page",
    label: "Projects",
    description: "Explore and evaluate projects.",
    headIcon: <ForestRounded />,
    link: "/projects",
    permissions: [PermissionEnum.PROJECTS_READ],
  },
  {
    type: "page",
    label: "Market Intelligence",
    description: "Admin view to curate market news.",
    headIcon: <RssFeedRounded />,
    link: "/market-intelligence",
    permissions: [PermissionEnum.LOGIN],
  },
  {
    type: "page",
    label: "Data Management",
    description: " Load and sync data.",
    headIcon: <TopicRounded />,
    link: "/data-management",
    permissions: [PermissionEnum.REPORTING_DATA_MANAGEMENT],
  },
  {
    type: "page",
    label: "Notifications",
    description: "Subscribe to notifications and reports.",
    headIcon: <NotificationAddRounded />,
    link: "/notification-settings",
    permissions: [PermissionEnum.LOGIN],
  },
  {
    type: "page",
    label: "Reports Dashboard",
    description: "<Place holder>",
    headIcon: <InsertChartRounded />,
    link: "/reports-dashboard",
    permissions: [],
    hidden: true,
  },
];

const NavigationMenuContext = createContext<Partial<NavigationMenuContextData>>({});

export const NavigationMenuProvider = ({ children }: PropsWithChildren): JSX.Element => {
  const features = useFeatures();
  const pathname = useAppPathname();
  const { user: loginUser } = useAuth();

  const canViewMenu = useCallback(
    ({ permissions = [], flags = [], partiallyAuthorize }: NavigationMenuItem) => {
      const permitted = loginUser?.[partiallyAuthorize ? "hasSomePermissions" : "hasPermissions"](permissions);
      const featurable = flags.every((flag) => !!features[flag]);

      return permitted && featurable;
    },
    [features, loginUser],
  );

  const permissableMenuItemsRecursively = useCallback(
    (menus: NavigationMenuItem[]): NavigationMenuItem[] =>
      menus
        .reduce((accum, menu) => {
          if (!menu?.hidden && canViewMenu(menu)) {
            if (menu?.submenu && !!menu.submenu.length)
              accum.push({
                ...menu,
                submenu: permissableMenuItemsRecursively(menu.submenu),
              });
            else accum.push(menu);
          }

          return accum;
        }, [] as NavigationMenuItem[])
        .filter(({ type, submenu }) => (type === "submenu" && !!submenu ? !!submenu.length : true)),
    [canViewMenu],
  );

  const permissibleMenus = useMemo(
    () => permissableMenuItemsRecursively(Object.assign([], MenuItems)),
    [permissableMenuItemsRecursively],
  );

  const getPermissableMenu = useCallback(
    (menuLink: string, menus: NavigationMenuItem[], comparator: Comparator = "equals"): NavigationMenuItem | null => {
      let permissableMenu: Nullable<NavigationMenuItem> = null;

      for (const menu of menus) {
        if (menu) {
          if (comparator === "equals" ? menu.link === menuLink : menu.link[comparator](menuLink))
            permissableMenu = menu;
          else if (menu.submenu) permissableMenu = getPermissableMenu(menuLink, menu.submenu, comparator);

          if (permissableMenu) return permissableMenu;
        }
      }

      return permissableMenu;
    },
    [],
  );

  const activeMenuPaths: string[] = useMemo(() => {
    if (!pathname) return [];

    const navTokens = pathname.split("/");

    let currentMenu: Nullable<NavigationMenuItem> = null;

    const activePath = Object.assign([], navTokens.slice(navTokens.length === 1 ? 0 : 1)).reduce(
      (activePath: string[], token) => {
        const menu = getPermissableMenu(token, currentMenu ? [currentMenu] : MenuItems, "endsWith");

        if (menu) {
          activePath.push(menu.link);
          currentMenu = menu;
        }

        return activePath;
      },
      [],
    );

    return activePath;
  }, [pathname, getPermissableMenu]);

  const isActiveMenu = useCallback((link: string): boolean => activeMenuPaths.includes(link), [activeMenuPaths]);

  return (
    <NavigationMenuContext.Provider
      value={{
        activeMenuPaths,
        permissibleMenus,
        isActiveMenu,
        getPermissableMenu,
      }}
    >
      {children}
    </NavigationMenuContext.Provider>
  );
};

const useNavigationMenu = (): Partial<NavigationMenuContextData> => useContext(NavigationMenuContext);

export default useNavigationMenu;
