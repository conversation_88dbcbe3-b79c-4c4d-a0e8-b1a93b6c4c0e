import { SpanProcessor, WebTracerProvider } from "@opentelemetry/sdk-trace-web";
import { registerInstrumentations } from "@opentelemetry/instrumentation";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";
import { OTLPExporterNodeConfigBase } from "@opentelemetry/otlp-exporter-base";
import { Resource } from "@opentelemetry/resources";
import { SemanticResourceAttributes } from "@opentelemetry/semantic-conventions";
import { getWebAutoInstrumentations } from "@opentelemetry/auto-instrumentations-web";
import { PropsWithChildren } from "react";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { Span } from "@opentelemetry/api";
import { APIAuthResponse, decodeUser } from "@/app/libs/auth-helpers";
import { useMount } from "react-use";

export declare type EventName = keyof HTMLElementEventMap;

async function getUserEmail(): Promise<string> {
  try {
    const res = await fetch("/auth/cookie");
    const data: APIAuthResponse = await res.json();

    const user = decodeUser(data);

    return user?.email ?? "";
  } catch {
    return "";
  }
}

const serviceName = process.env.NEXT_PUBLIC_APP_NAME ?? "rubicon-admin";
const deploymentEnvironment = process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT ?? "local";

const initializeTrace = (email: string): void => {
  // run only on client
  if (typeof window !== "undefined") {
    import("@opentelemetry/context-zone").then(({ ZoneContextManager }) => {
      const collectorOptions: OTLPExporterNodeConfigBase = {
        url: "/tracing",
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Headers": "*",
        },
        concurrencyLimit: 10,
      };

      const resource = Resource.default().merge(
        new Resource({
          [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
          [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: deploymentEnvironment,
        }),
      );

      const provider = new WebTracerProvider({ resource: resource });
      // @tr11 to take a look at this. "addSpanProcessor" is deprecated.
      provider.addSpanProcessor(
        new BatchSpanProcessor(new OTLPTraceExporter(collectorOptions)) as unknown as SpanProcessor, // @tr11 to take a look at this. type casting to "SpanProcessor" to avoid build issues.
      );

      provider.register({ contextManager: new ZoneContextManager() });

      // Registering instrumentations
      registerInstrumentations({
        instrumentations: [
          getWebAutoInstrumentations({
            "@opentelemetry/instrumentation-xml-http-request": {
              enabled: true,
              applyCustomAttributesOnSpan: (span: Span): void => {
                span.setAttribute("user", email);
              },
            },
            "@opentelemetry/instrumentation-document-load": { enabled: false },
            "@opentelemetry/instrumentation-fetch": {
              enabled: true,
              ignoreUrls: [/.*_next.*/],
              applyCustomAttributesOnSpan: (span: Span): void => {
                span.setAttribute("user", email);
              },
            },
            "@opentelemetry/instrumentation-user-interaction": {
              shouldPreventSpanCreation: (eventType: EventName, element: HTMLElement, span: Span) => {
                span.setAttribute("user", email);
                span.setAttribute("content", element?.innerText ?? element?.textContent);
              },
            },
          }),
        ],
      });
    });
  }
};

// Provider
export function TelemetryProvider(props: PropsWithChildren): JSX.Element {
  useMount(() => {
    getUserEmail().then((email) => initializeTrace(email));
  });
  return <>{props.children}</>;
}
