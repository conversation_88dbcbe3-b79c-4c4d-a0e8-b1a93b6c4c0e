import { styled } from "@mui/material/styles";
import Alert, { AlertColor } from "@mui/material/Alert";

import {
  CustomContentProps,
  SnackbarAction,
  SnackbarContent,
  SnackbarKey,
  SnackbarProvider,
  closeSnackbar,
} from "notistack";
import React, { memo, forwardRef } from "react";

const StyledSnackbarProvider = styled(SnackbarProvider)(() => ({}));

const actionIsFunction = (action: SnackbarAction): action is (id: SnackbarKey) => React.ReactNode =>
  typeof action === "function";

interface MuiAlertStyleContentProps extends CustomContentProps {
  node?: JSX.Element;
}

const getVariantColor = (variant: string): AlertColor => {
  switch (variant) {
    case "default":
      return "info";
    case "customWarning":
      return "warning";
    default:
      return variant as AlertColor;
  }
};

const MuiAlertStyleContent = forwardRef<HTMLDivElement, MuiAlertStyleContentProps>((props, forwardedRef) => {
  const { id, message, action: componentOrFunctionAction, variant, style, node, className } = props;

  let action = componentOrFunctionAction;
  if (actionIsFunction(action)) {
    action = action(id);
  }

  const alertColor = getVariantColor(variant);

  return (
    <SnackbarContent ref={forwardedRef} className={className}>
      <Alert severity={alertColor} onClose={() => closeSnackbar(id)} action={action} style={style}>
        {message}
        {node}
      </Alert>
    </SnackbarContent>
  );
});

MuiAlertStyleContent.displayName = "MuiAlertContent";
export const MuiAlertContent = memo(MuiAlertStyleContent);

StyledSnackbarProvider.defaultProps = {
  Components: {
    success: MuiAlertContent,
    info: MuiAlertContent,
    error: MuiAlertContent,
    warning: MuiAlertContent,
    default: MuiAlertContent,
  },
  maxSnack: 1,
  autoHideDuration: 10000,
  anchorOrigin: {
    vertical: "top",
    horizontal: "center",
  },
};

export default StyledSnackbarProvider;
