"use client";

import { Breadcrumb } from "@models/breadcrumb";
import { PropsWithChildren, Suspense, createContext, useCallback, useContext, useEffect, useState } from "react";
import { usePathname, useParams } from "next/navigation";
import { Nullable } from "@rubiconcarbon/frontend-shared";
import GlobalLoading from "@components/loading/loading";

type StringFunction = (path: string) => Nullable<string>;

type SegmentToBreadcrumbRecord = Record<string, string | StringFunction>;

type BreadCrumbContextData = {
  breadcrumbs: Breadcrumb[];
  updateBreadcrumbName: (current: string, update: string) => void;
};

const IGNORE_BREADCRUMB_TOKEN = "~IGNORE~";

/**
 * ### DYNAMIC SEGMENT TO BREADCRUMB MAPPER
 * ---------------------------------------
 *
 * **Breadcrumbs** are mapped based on the segments in the path
 *
 * Use this to map **dynamic route segment** to **user-friendly breadcrumb labels**.
 *
 * **keys** are the dynamic sting sections in the project structure.
 *
 * **values** can either be,
 *  - a user friendly string label
 *  - a function that takes the current path as a string parameter and returns a user friendly string label
 *
 * if you rather ignore a breadcrumb from appearing based on the segments in the path, simply add **IGNORE_BREADCRUMB_TOKEN** as the value
 */
const DynamicSegmentToBreadcrumbName: SegmentToBreadcrumbRecord = {
  "customer-sale-id": (path: string) => (path.endsWith("upload-document") ? IGNORE_BREADCRUMB_TOKEN : "Sale Details"), // todo: remove this after sales + trade merge
  "retirement-id": (path: string) =>
    path.endsWith("upload-retirement-certification") ? IGNORE_BREADCRUMB_TOKEN : "Details",
  "basket-id": (path: string) => (path.endsWith("edit-composition") ? IGNORE_BREADCRUMB_TOKEN : "Portfolio Details"),
  "user-id": IGNORE_BREADCRUMB_TOKEN,
  "organization-id": IGNORE_BREADCRUMB_TOKEN,
  "project-id": "Project Details",
  "market-report-id": (path: string) => (path.endsWith("edit-market-news") ? IGNORE_BREADCRUMB_TOKEN : "Market Report"),
  "portfolio-id": (path: string) =>
    path.endsWith("edit-portfolio-sandbox") ? IGNORE_BREADCRUMB_TOKEN : "Portfolio Detail",
  "book-id": "Book Detail",
  "trade-id": (path: string) => (path.includes("edit-trade") ? IGNORE_BREADCRUMB_TOKEN : "Trade Details"), // todo: remove this after sales + trade merge
  "transaction-id": (path: string) => (path.includes("edit") ? IGNORE_BREADCRUMB_TOKEN : "Transaction Details"),
};

/**
 * ### STATIC SEGMENT TO BREADCRUMB MAPPER
 * ---------------------------------------
 *
 * **Breadcrumbs** are mapped based on the segments in the path
 *
 * Use this to map **static route segment** to **user-friendly breadcrumb labels**.
 *
 * **keys** are the dynamic sting sections in the project structure.
 *
 * **values** can either be,
 *  - a user friendly string label
 *  - a function that takes the current path as a string parameter and returns a user friendly string label
 *
 * if you rather ignore a breadcrumb from appearing based on the segments in the path, simply add **IGNORE_BREADCRUMB_TOKEN** as the value
 */
const StaticSegmentToBreadcrumbName: SegmentToBreadcrumbRecord = {
  permissions: (path: string) => (path.includes("users") ? "Set organization & permissions" : null),
  retirements: "Retirements & Transfers", // added a static mapping due to the would be need for emails/documents change if I changed the url
};

const toTitleCase = (value: string, separator: string = "-"): string => {
  if (!value.includes(separator)) return value.charAt(0).toUpperCase() + value.slice(1);

  return value
    .split(separator)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const BreadCrumbContext = createContext<Partial<BreadCrumbContextData>>({});

export const BreadcrumbProvider = ({ children }: PropsWithChildren): JSX.Element => {
  const pathname = usePathname();
  const params = useParams();
  const [breadcrumbs, setBreadcrumbs] = useState<Breadcrumb[]>([]);

  useEffect(() => {
    if (!pathname) return;

    // Get search params from Next.js hook
    const searchParams = new URLSearchParams(window.location.search);
    const fullUrl = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : "");

    // Extract path segments from the pathname (not including query params)
    const pathSegments = pathname.split("/").filter(Boolean);

    // Get a list of just the param keys without values
    const paramKeys = Object.keys(params ?? {});

    // Build breadcrumbs
    const builtCrumbs: Breadcrumb[] = [];
    let currentPath = "";

    // Process each segment
    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      currentPath += `/${segment}`;

      // 1. Check if it's a static segment with a mapping
      if (StaticSegmentToBreadcrumbName[segment]) {
        const mapping = StaticSegmentToBreadcrumbName[segment];

        let displayName: string;
        if (typeof mapping === "function") {
          const result = mapping(currentPath);
          if (!result) {
            displayName = toTitleCase(segment);
          } else {
            displayName = result;
          }
        } else {
          displayName = mapping as string;
        }

        if (displayName !== IGNORE_BREADCRUMB_TOKEN) {
          builtCrumbs.push({
            name: displayName,
            path: currentPath,
          });
        }
        continue;
      }

      // 2. Check if this might be a parameter based on pathname pattern
      // NOTE: This implementation assumes parameter names follow the convention of
      // "[collection-name]-id" or are otherwise related to their parent segment name.
      // For example, "book-id" for a parameter under the "books" route.
      let paramType: Nullable<string> = null;

      // Check each parameter key to see if it might match this segment position
      for (const key of paramKeys) {
        // See if we have a mapping for this parameter key
        if (DynamicSegmentToBreadcrumbName[key]) {
          // Check if this segment is the corresponding value for this param key
          if (i > 0) {
            const prevSegment = pathSegments[i - 1];
            // If previous segment is like 'books' and current key is 'book-id'
            if (key.includes(prevSegment) || prevSegment.includes(key.replace("-id", ""))) {
              paramType = key;
              break;
            }
          }
        }
      }

      // 3. If we identified a parameter type, use the mapping
      if (paramType) {
        const mapping = DynamicSegmentToBreadcrumbName[paramType];

        // When using mapping functions, make sure they have access to the full URL
        // including potential segments after this one and query parameters
        // This ensures functions that check for patterns like "edit" will work
        let displayName: string;
        if (typeof mapping === "function") {
          // Use fullUrl to check conditions in the mapping function
          const result = mapping(fullUrl);
          // Skip if marked to ignore
          if (result === IGNORE_BREADCRUMB_TOKEN) {
            continue;
          }
          displayName = result || toTitleCase(segment);
        } else if (mapping === IGNORE_BREADCRUMB_TOKEN) {
          continue;
        } else {
          displayName = mapping;
        }

        builtCrumbs.push({
          name: displayName,
          path: currentPath,
        });
      } else {
        // 4. Default: use title case for display
        builtCrumbs.push({
          name: toTitleCase(segment),
          path: currentPath,
        });
      }
    }

    setBreadcrumbs(builtCrumbs);
  }, [pathname, params]);

  const updateBreadcrumbName = useCallback((current: string, update: string): void => {
    setTimeout(() => {
      setBreadcrumbs((previous) =>
        previous.map((breadcrumb) => (breadcrumb.name === current ? { ...breadcrumb, name: update } : breadcrumb)),
      );
    });
  }, []);

  return (
    <Suspense fallback={<GlobalLoading />}>
      <BreadCrumbContext.Provider value={{ breadcrumbs, updateBreadcrumbName }}>{children}</BreadCrumbContext.Provider>
    </Suspense>
  );
};

const useBreadcrumbs = (): Partial<BreadCrumbContextData> => useContext(BreadCrumbContext);

export default useBreadcrumbs;
