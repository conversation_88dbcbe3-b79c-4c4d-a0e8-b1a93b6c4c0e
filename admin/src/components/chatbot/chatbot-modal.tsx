import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { PropsWithChildren, useContext, useState } from "react";
import classes from "./chatbot.module.scss";
import ChatbotResults from "./chatbot-results";
import ChatbotInput from "./chatbot-input";
import { AuthContext } from "@providers/auth-provider";

export default function Chatbot(
  props: PropsWithChildren<{ isOpen: boolean; close: () => void; allowInspect: boolean }>,
): JSX.Element {
  const { token } = useContext(AuthContext);
  const defaultAPIbaseURL = "/api";
  const apiPath = process.env.NEXT_PUBLIC_API_BASE_URL || defaultAPIbaseURL;
  const [lastCheck, setLastCheck] = useState(new Date());
  const [disabled, setDisabled] = useState(false);

  const submit = async (q: string): Promise<void> => {
    if (q === "") return;
    const { id } = await fetch(`${apiPath}/reporting/chatbot`, {
      method: "POST",
      headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
      body: JSON.stringify({ q }),
    }).then((response) => response.json());

    // update input state and prevent multiple queries
    setDisabled(true);
    setLastCheck(new Date());
    setTimeout(() => {
      setDisabled(false);
    }, 1000);

    // fetch result
    setTimeout(() => {
      fetch(`${apiPath}/reporting/chatbot?q=${id}`, {
        method: "GET",
        headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
      })
        .then((response) => response.json())
        .then(() => {
          setLastCheck(new Date());
        });
    }, 100);
  };

  return (
    <Modal
      open={props.isOpen}
      onClose={props.close}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
      className={classes.modal}
    >
      <div className={classes.chatbotModal}>
        <Box className={classes.modalDiv}>
          <Typography id="modal-modal-title" variant="h5" component="h5">
            Rubicon Carbon Assistant
          </Typography>
          <Typography id="modal-modal-description">Early Beta AI Assistant.</Typography>
          <ChatbotResults lastCheck={lastCheck} closeDialog={props.close} allowInspect={props.allowInspect} />
          <ChatbotInput submit={submit} disabled={disabled}></ChatbotInput>
        </Box>
      </div>
    </Modal>
  );
}
