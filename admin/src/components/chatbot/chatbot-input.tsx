import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import TextField from "@mui/material/TextField";
import Send from "@mui/icons-material/Send";
import { PropsWithChildren, useCallback, useRef, useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import { Maybe } from "@rubiconcarbon/frontend-shared";

interface ChatbotInputProps {
  submit: (string) => void;
  disabled: boolean;
}

export default function ChatbotInput(props: PropsWithChildren<ChatbotInputProps>): JSX.Element {
  const [value, setValue] = useState("");
  const inputRef = useRef();

  const submit = useCallback(
    (val: string): void => {
      if (val === "") return;
      props.submit(val);
      if (inputRef?.current !== undefined) (inputRef as any).current.value = "";
    },
    [props],
  );

  return (
    <>
      <TextField
        inputRef={inputRef}
        variant="outlined"
        size="small"
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <Maybe condition={props.disabled}>
                <CircularProgress size={20} />
              </Maybe>
              <Maybe condition={!props.disabled}>
                <IconButton onClick={() => submit(value)} color="primary">
                  <Send />
                </IconButton>
              </Maybe>
            </InputAdornment>
          ),
        }}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={(e) => (e.key === "Enter" ? submit(value) : null)}
        disabled={props.disabled}
      />
    </>
  );
}
