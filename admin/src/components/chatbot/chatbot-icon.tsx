import Fab from "@mui/material/Fab";
import classes from "./chatbot.module.scss";
import chatIcon from "@assets/images/chat-assistant-simple.svg";
import Image from "next/image";
import { useCallback, useState } from "react";
import Chatbot from "./chatbot-modal";
import { ShowIfAuthorized } from "@/services/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import useAuth from "@providers/auth-provider";

export default function ChatbotIcon(): JSX.Element {
  const { user: loginUser } = useAuth();
  const [open, setOpen] = useState(false);
  const handleOpen = useCallback((): void => setOpen(true), []);
  const handleClose = useCallback((): void => setOpen(false), []);

  const detailsAllowed = loginUser?.hasPermissions([PermissionEnum.CHATBOT_INSPECT]) || false;

  return (
    <ShowIfAuthorized permissions={[PermissionEnum.CHATBOT_USAGE]}>
      <Chatbot isOpen={open} close={handleClose} allowInspect={detailsAllowed} />
      <Fab color="secondary" aria-label="add" onClick={handleOpen} className={classes.icon}>
        <Image src={chatIcon} alt="Rubicon Carbon Assistant" priority width={25} />
      </Fab>
    </ShowIfAuthorized>
  );
}
