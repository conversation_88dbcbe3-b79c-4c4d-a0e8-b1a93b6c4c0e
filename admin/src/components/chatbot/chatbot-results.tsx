import { AuthContext } from "@providers/auth-provider";
import Typography from "@mui/material/Typography";
import { useCallback, useContext, useEffect, useRef, useState } from "react";
import { sortedUniqBy, orderBy } from "lodash";
import Tooltip from "@mui/material/Tooltip";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import List from "@mui/material/List";
import ContentCopy from "@mui/icons-material/ContentCopy";
import {
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  ListItem,
} from "@mui/material";
import classes from "./chatbot.module.scss";
import Info from "@mui/icons-material/Info";
import { format as sqlFormat } from "sql-formatter";
import { ChatbotResultInterface, ChatbotResult } from "./chatbot-result";

interface ChatbotResultsProps {
  lastCheck: Date;
  closeDialog: () => void;
  allowInspect: boolean;
}

export default function ChatbotResults(props: ChatbotResultsProps): JSX.Element {
  const { token } = useContext(AuthContext);
  const [reachedTop, setReachedTop] = useState(false);
  const [history, setHistory] = useState<ChatbotResultInterface[]>([]);
  const scrollRef = useRef(null);

  const defaultAPIbaseURL = "/api";

  const load = useCallback(
    (queryString?: string): void => {
      queryString = queryString ?? "";
      const apiPath = process.env.NEXT_PUBLIC_API_BASE_URL || defaultAPIbaseURL;
      fetch(`${apiPath}/reporting/chatbot/history${queryString}`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
      })
        .then((response) => response.json())
        .then((data: ChatbotResultInterface[]) => {
          if (data.length === 0) setReachedTop(true);

          if (data.find((x) => !x.finished) !== undefined) {
            // results are still computing
          }

          data = orderBy(data.concat(history), (x) => x.id);
          data = sortedUniqBy(data, (x) => x.id);
          setHistory(data);
        });
    },
    [token, history, setHistory],
  );

  const loadMoreHistory = useCallback((): void => {
    const queryString = history.length ? `?last_id=${history.at(0).id}` : "";
    load(queryString);
  }, [load, history]);

  // always scroll to the last message
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior: "auto" });
    }
  }, [history]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(load, [props.lastCheck]); // refresh initially and when there are new questions

  return (
    <List
      style={{
        flexGrow: 2,
        backgroundColor: "#f1f1f1",
        borderRadius: "10px",
        padding: "0 15px",
        margin: "10px 0",
        overflowY: "scroll",
      }}
    >
      <Maybe condition={!reachedTop}>
        <div style={{ display: "flex", justifyContent: "center" }}>
          <a onClick={loadMoreHistory} className={classes.loadMoreButton} style={{ marginTop: "10px" }}>
            Load More
          </a>
        </div>
      </Maybe>
      {history.map((x, i) => {
        return (
          <ListItem key={x.id} style={{ marginTop: "10px", display: "block" }} ref={scrollRef}>
            <Maybe
              condition={
                i == 0 ||
                new Date(x.created_at * 1000).toLocaleDateString() !=
                  new Date(history.at(i - 1).created_at * 1000).toLocaleDateString()
              }
            >
              <Divider style={{ marginBottom: "10px" }}>
                <Typography variant="caption">
                  {new Date(x.created_at * 1000).toLocaleDateString(undefined, {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </Typography>
              </Divider>
            </Maybe>
            <ChatbotResultsItem item={x} closeDialog={props.closeDialog} allowInspect={props.allowInspect} />
          </ListItem>
        );
      })}
    </List>
  );
}

export function ChatbotResultsItem(props: {
  item: ChatbotResultInterface;
  closeDialog: () => void;
  allowInspect: boolean;
}): JSX.Element {
  const [open, setOpen] = useState(false);
  const [expandedResults, setExpandedResults] = useState(false);
  const handleClose = (): void => {
    setOpen(false);
    setExpandedResults(false);
  };

  const { item } = props;

  return (
    <>
      <div>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="body1" color="primary" fontWeight={600} style={{ flexShrink: 1 }}>
            {item.q}
          </Typography>
          <div style={{ marginLeft: 10, flexShrink: 0 }}>
            <Tooltip
              title={
                <>
                  <div>id: {item.id}</div>
                  <div>ts: {new Date(item.created_at * 1000).toLocaleString()}</div>
                </>
              }
            >
              <Typography variant="caption">{new Date(item.created_at * 1000).toLocaleTimeString()}</Typography>
            </Tooltip>
            <Maybe condition={props.allowInspect}>
              <span onClick={() => setOpen(true)}>
                <Info sx={{ height: 11, cursor: "pointer" }} />
              </span>
            </Maybe>
          </div>
        </div>
        <Maybe condition={item.finished && (item.result == null || item.result?.data?.length == 0)}>
          <div>- Unable to fetch results or no results found.</div>
        </Maybe>
        <Maybe condition={item.finished && item.result != null && item.result?.data?.length > 0}>
          <ChatbotResult result={item.result} id={item.id} closeDialog={props.closeDialog} />
        </Maybe>
        <Maybe condition={!item.finished}>
          <CircularProgress size={14} color="primary"></CircularProgress>
        </Maybe>
      </div>
      <Maybe condition={props.allowInspect}>
        <Dialog onClose={handleClose} open={open} maxWidth={"lg"} fullWidth={true}>
          <DialogTitle>AI Assistant Question</DialogTitle>
          <DialogContent>
            <table width={"100%"} style={{ borderCollapse: "collapse" }}>
              <tr>
                <th style={{ paddingBottom: 10 }}>Question</th>
                <td style={{ paddingBottom: 10 }} colSpan={3}>
                  {item.q}
                </td>
              </tr>
              <tr>
                <th style={{ paddingBottom: 10 }}>Timestamp</th>
                <td style={{ paddingBottom: 10 }}>{new Date(item.created_at * 1000).toLocaleString()}</td>
                <th style={{ paddingBottom: 10 }}>Execution Time</th>
                <td style={{ paddingBottom: 10 }}>
                  {item.execution <= 1 ? `${item.execution} ms` : `${item.execution} s`}
                </td>
              </tr>
              <tr>
                <td style={{ borderBottom: "1px solid #ddd" }} colSpan={4} />
              </tr>
              <tr>
                <th>
                  SQL Query{" "}
                  <span
                    onClick={() => {
                      navigator.clipboard.writeText(
                        "SET LOCAL search_path TO chatbot;\n\n" +
                          sqlFormat(item.query, {
                            language: "postgresql",
                            keywordCase: "upper",
                          }),
                      );
                    }}
                  >
                    <ContentCopy sx={{ cursor: "pointer", height: 14 }} />
                  </span>
                </th>
                <td colSpan={3}>
                  <pre>
                    {item.query
                      ? sqlFormat(item.query, {
                          language: "postgresql",
                          keywordCase: "upper",
                        })
                      : ""}
                  </pre>
                </td>
              </tr>
              <tr>
                <th>
                  Results{" "}
                  <Maybe condition={expandedResults}>
                    <Button onClick={() => setExpandedResults(false)} size="small">
                      hide
                    </Button>
                  </Maybe>
                </th>
                <td colSpan={3}>
                  <Maybe condition={expandedResults}>
                    <pre>{JSON.stringify(item.result?.data, null, 2)}</pre>
                  </Maybe>
                  <Maybe condition={!expandedResults}>
                    <Button onClick={() => setExpandedResults(true)} size="small">
                      show
                    </Button>
                  </Maybe>
                </td>
              </tr>
            </table>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Close</Button>
          </DialogActions>
        </Dialog>
      </Maybe>
    </>
  );
}
