import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { DocumentTypeUILabel } from "@constants/documents";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { FileUploadHandlers } from "@/types/headless-downloader";
import { CloseRounded } from "@mui/icons-material";
import { Typography, Stack, Box, Select, InputLabel, FormControl, SelectChangeEvent, MenuItem } from "@mui/material";
import { DocumentType, DocumentUpdateRequest, uuid, DocumentUploadUrlRequest } from "@rubiconcarbon/shared-types";
import { useState, useMemo, useCallback, MouseEvent, useEffect, PropsWithChildren } from "react";
import { useGetSetState, useToggle } from "react-use";
import { useLogger } from "@providers/logging";
import useDocumentsApi from "@hooks/use-documents-api";
import Attachments from "./attachments";
import { Maybe, px } from "@rubiconcarbon/frontend-shared";

import dialogClasses from "./dialog.module.scss";

export interface FileType {
  label: string;
  value: DocumentType;
}

type DocumentsModalProps = {
  open: boolean;
  relatedKey: string;

  /**
   * we should always have at least one of the following added to the request for a document upload.
   * marketing agreements are the only exception for now.
   */
  counterpartyId?: uuid;
  organizationId?: uuid;
  customerPortfolioId?: uuid;

  getTypeOptions?: FileType[];
  saveTypeOptions: FileType[];
  canUpload?: boolean;
  onUploadSuccess?: () => void;
  onUploadError?: () => void;
  onPositiveClick?: (callback: () => void) => void;
  onNegativeClick?: (callback: () => void) => void;
  onClose: (callback: () => void, uploaded?: boolean) => Promise<void>;
};

const DocumentsModal = ({
  open,
  relatedKey,
  counterpartyId,
  organizationId,
  customerPortfolioId,
  getTypeOptions = null,
  saveTypeOptions,
  canUpload = true,
  children,
  onUploadSuccess,
  onUploadError,
  onPositiveClick,
  onNegativeClick,
  onClose,
}: PropsWithChildren<DocumentsModalProps>): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<DocumentUploadUrlRequest>();
  const [updatePayload, setUpdatePayload] = useState<DocumentUpdateRequest>();
  const [uploadHandlers, setUploadHandlers] = useState<FileUploadHandlers>({});
  const [uploading, setUploading] = useToggle(false);
  const [refreshAttachments, setRefreshAttachments] = useToggle(false);
  const [fileType, setFileType] = useState<DocumentType | "">("");
  const [uploaded, setUploaded] = useToggle(false);

  useEffect(() => {
    setFileType("");
  }, []);

  const { retrieveUploadLink, update } = useDocumentsApi({
    updatePayload,
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error?.message}`, {});
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      setTimeout(() => onUploadSuccess?.());
    },
    onUpdateError: (error: any) => {
      enqueueError("Successful upload but was unable to update file details");
      logger.error(
        `Successfully upload file but was unable to update file details for TradeStatus ${status}: ${error?.message}`,
        {},
      );
      onUploadError?.();
    },
  });

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      relatedKey,
      filename: file.name,
      type: fileType === "" ? undefined : fileType,
      isPublic: true,
      ...px({ counterpartyId, organizationId, customerPortfolioId }, [undefined, null]),
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const close = useCallback((): void => {
    setUploadHandlers({});
    setUploading(false);
  }, [setUploading]);

  const onFileUploadSuccess = useCallback(
    async (file: File, metadata: OnFileUploadSuccessMetaData): Promise<void> => {
      setUpdatePayload({
        id: uuid(metadata?.s3FileId),
        relatedKey,
        filename: file.name,
        type: fileType === "" ? undefined : fileType,
        isPublic: true,
        ...px({ counterpartyId, organizationId, customerPortfolioId }, [undefined, null]),
      });

      setUploaded(true);

      setTimeout(async () => {
        await update();
        setRefreshAttachments(!refreshAttachments);
        setFileType("");
        close();
      });
    },
    [
      relatedKey,
      fileType,
      counterpartyId,
      organizationId,
      customerPortfolioId,
      setUploaded,
      update,
      setRefreshAttachments,
      refreshAttachments,
      close,
    ],
  );

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  const hasFileToUpload = useMemo(() => Object.values(uploadHandlers).some((fn) => !!fn), [uploadHandlers]);

  const handleExposeUploadHandler = (
    inputId: string,
    handler: (event: MouseEvent<HTMLButtonElement>) => Promise<void>,
  ): void => {
    setUploadHandlers({ [inputId]: handler });
  };

  const handleFileUpload = async (): Promise<void> => {
    const [[inputId, handler]] = Object.entries(uploadHandlers) as [
      string,
      (event: MouseEvent<HTMLButtonElement>) => Promise<void>,
    ][];
    await handler({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>);
    setUploadHandlers({ [inputId]: null });
  };

  const handleFileTypeChange = (event: SelectChangeEvent<DocumentType>): void => {
    const value = event.target.value as DocumentType;
    setFileType(value);
  };

  const onRemoveSuccess = (): void => {
    enqueueSuccess(`Successfully deleted document for ${relatedKey}`);
    setRefreshAttachments(!refreshAttachments);
  };

  return (
    <GenericDialog
      open={open}
      dismissIcon={<CloseRounded />}
      title={
        <Typography fontWeight={500} sx={{ fontSize: "20px" }}>
          Documents
        </Typography>
      }
      positiveAction={{
        buttonText: "Upload",
        disabled: !fileType || !hasFileToUpload || uploading,
      }}
      negativeAction={{
        buttonText: "Cancel",
      }}
      onClose={() => onClose(close, uploaded)}
      onPositiveClick={async () => (fileType ? await handleFileUpload() : onPositiveClick(close))}
      onNegativeClick={() => onNegativeClick?.(close)}
      classes={{
        root: dialogClasses.Dialog,
        title: dialogClasses.StatusTitle,
        content: dialogClasses.Content,
        actions: dialogClasses.StatusActions,
      }}
    >
      <Stack rowGap={2} padding={2}>
        <Maybe condition={canUpload}>
          {() => (
            <>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                Please first select the document type that you want to upload
              </Typography>
              <Box>
                <FormControl sx={{ width: "100%" }} disabled={saveTypeOptions.length === 0}>
                  <InputLabel id="file-type-label">Select document type</InputLabel>
                  <Select
                    labelId="File type"
                    id="file-type"
                    sx={{ maxWidth: "340px" }}
                    label="Select document type"
                    value={fileType}
                    disabled={!canUpload}
                    onChange={(event: SelectChangeEvent<DocumentType>) => handleFileTypeChange(event)}
                  >
                    <MenuItem value="" sx={{ height: "35px" }}>
                      {""}
                    </MenuItem>
                    {saveTypeOptions.map(({ label, value }) => (
                      <MenuItem key={value} value={value}>
                        {label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
              <Stack rowGap={2}>
                <Maybe condition={!!fileType}>
                  <Typography variant="body2" fontWeight={100}>
                    Please upload{" "}
                    <Typography component="span" variant="body2">
                      {DocumentTypeUILabel[fileType]}
                    </Typography>
                    .
                  </Typography>
                </Maybe>
                <UploaderWidget
                  inputId="document-uploads"
                  cancelButtonText="back"
                  uploadLink={getS3UploadApiLink}
                  allowedExtensions={["image/jpeg", "image/png", "application/pdf"]}
                  canDragAndDrop={!!fileType}
                  canUpload={!!fileType}
                  externallyUpload
                  onExposeUploadHandler={handleExposeUploadHandler}
                  onFileUploadSuccess={onFileUploadSuccess}
                  onFileUploadError={onFileUploadError}
                  onUploadingStatusChange={(status: boolean) => setUploading(status)}
                  onFileRemoval={async () => {
                    setUploadHandlers({});
                    return true;
                  }}
                  clearOnFileUploadSuccess
                  maxFiles={5}
                />
              </Stack>
            </>
          )}
        </Maybe>
        <Attachments
          refresh={refreshAttachments}
          relatedKey={relatedKey}
          types={(getTypeOptions || saveTypeOptions).map(({ value }) => value)}
          onRemoveAttachSuccess={onRemoveSuccess}
        />
        {children}
      </Stack>
    </GenericDialog>
  );
};

export default DocumentsModal;
