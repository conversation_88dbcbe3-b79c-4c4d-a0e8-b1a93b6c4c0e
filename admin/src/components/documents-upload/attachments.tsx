import { Attachment } from "@components/attachment-list/attachment-list";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { Box, Stack, Typography } from "@mui/material";
import { DocumentType } from "@rubiconcarbon/shared-types";
import { useLogger } from "@providers/logging";
import Awaited from "../ui/await/components/awaited";
import { isNothing, Maybe, px } from "@rubiconcarbon/frontend-shared";
import { useMemo } from "react";

import dialogClasses from "./dialog.module.scss";

type AttachmentsProps = {
  relatedKey: string;
  refresh: boolean;
  types?: DocumentType[];
  onRemoveAttachSuccess: () => void;
};

const Attachments = ({ relatedKey, refresh, types = [], onRemoveAttachSuccess }: AttachmentsProps): JSX.Element => {
  const { documents, fetching, fetch } = useDocumentsApi({
    query: {
      relatedKey,
      ...px({ types: !isNothing(types, ["array"]) && types }, [undefined, null, false]),
    },
  });

  const { logger } = useLogger();

  const hasDocuments = useMemo(() => !!documents?.length, [documents?.length]);

  usePerformantEffect(() => {
    if (!fetching && !!relatedKey) setTimeout(async () => await fetch());
  }, [relatedKey, refresh]);

  const onRemoveError = async (error: any): Promise<void> => {
    logger.error(`Unable to delete attachment: ${error?.message}`, {});
  };

  const onRemoveSuccess = async (): Promise<void> => {
    onRemoveAttachSuccess();
  };

  const deleteConfirmation = {
    title: "Delete this document?",
    content: (
      <Box sx={{ width: "400px" }}>
        Are you sure you want to <strong>delete</strong> the document?
      </Box>
    ),
  };

  return (
    <Stack gap={1} mb={1}>
      <Maybe condition={fetching}>
        <Awaited
          variant="rounded"
          itemSx={{
            height: 40,
            width: "100%",
          }}
        />
      </Maybe>
      <Maybe condition={!fetching}>
        <Maybe condition={hasDocuments}>
          {() => (
            <>
              {documents.map((document) => (
                <Box key={document?.id} sx={{ padding: "10px" }} className={dialogClasses.boxContainer}>
                  <Attachment
                    document={document}
                    onRemoveError={onRemoveError}
                    onRemoveSuccess={onRemoveSuccess}
                    deleteConfirmation={deleteConfirmation}
                  />
                </Box>
              ))}
            </>
          )}
        </Maybe>
        <Maybe condition={!hasDocuments}>
          <Typography padding={2}>No documents have been uploaded.</Typography>
        </Maybe>
      </Maybe>
    </Stack>
  );
};

export default Attachments;
