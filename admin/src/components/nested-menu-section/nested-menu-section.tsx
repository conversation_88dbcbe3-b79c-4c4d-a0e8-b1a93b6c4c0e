import { NavigationMenuItem } from "@providers/navigation-menu-provider";
import { Grid, Stack, Typography } from "@mui/material";
import Link from "next/link";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { ArrowForwardRounded } from "@mui/icons-material";
import COLORS from "../ui/theme/colors";
import { useMemo } from "react";

import classes from "./styles/nested-menu-section.module.scss";

type NestedMenuSectionProps = {
  menus: NavigationMenuItem[];
};

const NestedMenuSection = ({ menus }: NestedMenuSectionProps): JSX.Element => {
  const isSingle = useMemo(() => menus.length === 1, [menus]);
  const multiMenu = useMemo(() => (isSingle ? menus[0]?.submenu || [] : menus), [isSingle, menus]);

  return (
    <Grid container direction="column" gap={2}>
      <Grid item container gap={2} rowGap={5}>
        {multiMenu.map(({ link, label, description }) => (
          <Grid className={classes.Section} key={link} item xs={12} sm={5} md={3}>
            <Link className={classes.Link} href={link}>
              <Stack className={classes.LinkItem} direction="row" justifyContent="space-between" alignItems="center">
                <Stack gap={1}>
                  <Typography color={COLORS.black}>{label}</Typography>
                  <Maybe condition={!!description}>
                    <Typography variant="body2" color={COLORS.darkGrey}>
                      {description}
                    </Typography>
                  </Maybe>
                </Stack>
                <ArrowForwardRounded className={classes.Proceed} color="primary" />
              </Stack>
            </Link>
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};

export default NestedMenuSection;
