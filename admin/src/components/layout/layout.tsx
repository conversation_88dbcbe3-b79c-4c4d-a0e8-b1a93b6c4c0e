import { PropsWithChildren } from "react";
import { Stack } from "@mui/material";
import { classcat } from "@rubiconcarbon/frontend-shared";
import { useStoreProvider } from "@providers/store-provider";
import SideBar from "./components/side-bar";
import Main from "./components/main";

import classes from "./styles/layout.module.scss";

export default function Layout({ children }: PropsWithChildren): JSX.Element {
  const { localState } = useStoreProvider();
  const { navigation } = localState;
  const { expanded = false, pinned = false, forceCollapse = false } = navigation || {};

  return (
    <Stack className={classcat([classes.Layout, { [classes.LayoutPinned]: pinned }])} direction="row">
      <SideBar expanded={expanded} pinned={pinned} forceCollapse={forceCollapse} />
      <Main>{children}</Main>
    </Stack>
  );
}
