import useNavigationMenu from "@providers/navigation-menu-provider";
import { NavigationMenuItem } from "@providers/navigation-menu-provider";
import { ArrowForwardIosRounded } from "@mui/icons-material";
import {
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from "@mui/material";
import { useState, useEffect, useCallback, MouseEvent } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";

import classes from "../styles/nav-menu.module.scss";

type NavMenuItemProps = {
  menu: NavigationMenuItem;
  onMenuClick?: () => void;
};

const NavMenuItem = ({ menu, onMenuClick }: NavMenuItemProps): JSX.Element => {
  const {
    label,
    link,
    headIcon,
    expandIcon = <ArrowForwardIosRounded className={classes.NavMenuItemExpandIcon} />,
    collapseIcon = <ArrowForwardIosRounded className={classes.NavMenuItemExpandIcon} sx={{ rotate: "90deg" }} />,
    expanded,
    expandable = true,
    submenu,
  } = menu;
  const { activeMenuPaths } = useNavigationMenu();
  const [expand, setExpand] = useState<boolean>(!expandable && expanded);

  useEffect(() => {
    setExpand(activeMenuPaths.includes(link));
  }, [activeMenuPaths, link]);

  const handleMenuClick = useCallback(() => {
    if (!expand || (!!expand && activeMenuPaths?.includes(menu.link))) setExpand(!expand);
    if (onMenuClick) onMenuClick();
  }, [activeMenuPaths, expand, menu.link, onMenuClick]);

  const handleToggleExpand = useCallback(
    (event: MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      setExpand(!expand);
    },
    [expand],
  );

  return (
    <>
      <ListItem className={classes.NavMenuItem} alignItems="center">
        <ListItemButton
          className={`${classes.NavMenuItemButton} ${activeMenuPaths.includes(menu.link) ? classes.NavMenuItemActive : ""}`}
        >
          <Link className={classes.NavMenuItemLink} href={link} onClick={handleMenuClick}>
            <Maybe condition={!!headIcon}>
              <ListItemIcon className={classes.NavMenuItemHeaderIcon}>{headIcon}</ListItemIcon>
            </Maybe>
            <ListItemText
              className={classes.NavMenuItemText}
              primary={
                <Typography className={classes.NavMenuItemTextPrimary} variant="body2" color="primary">
                  {label}
                </Typography>
              }
            />
          </Link>
          <Maybe condition={expandable && !!submenu && submenu.length > 0}>
            <IconButton className={classes.NavMenuItemExpandButton} onClick={handleToggleExpand}>
              <Maybe condition={expand}>{collapseIcon}</Maybe>
              <Maybe condition={!expand}>{expandIcon}</Maybe>
            </IconButton>
          </Maybe>
        </ListItemButton>
      </ListItem>
      <Maybe condition={!!submenu && expand}>
        <ListItem className={classes.NavMenuItemSubMenu}>
          <Stack className={classes.NavMenuItemSubMenuItem} direction="row">
            <Divider className={classes.NavMenuItemSubMenuItemDivider} />
            <List className={classes.NavMenuItemSubMenuItemList}>
              {submenu?.map((childMenu) => (
                <NavMenuItem key={childMenu.link} menu={childMenu} onMenuClick={onMenuClick} />
              ))}
            </List>
          </Stack>
        </ListItem>
      </Maybe>
    </>
  );
};

export default NavMenuItem;
