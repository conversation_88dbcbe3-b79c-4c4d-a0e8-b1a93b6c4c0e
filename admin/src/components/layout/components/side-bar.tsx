import { useStore<PERSON>rovider } from "@providers/store-provider";
import { Box, Drawer, Stack } from "@mui/material";
import { classcat, Maybe } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";
import Image from "next/image";
import NavMenuList from "./nav-menu-list";
import PinNav from "@components/pin-nav/pin-nav";

import logo from "@assets/images/rubicon-carbon-logo.svg";
import logoExtended from "@assets/images/rubicon-logo-primary.svg";
import layoutClasses from "../styles/layout.module.scss";

type LayoutProps = {
  expanded: boolean;
  pinned: boolean;
  forceCollapse: boolean;
};

const SideBar = ({ expanded, pinned, forceCollapse }: LayoutProps): JSX.Element => {
  const { updateLocalState } = useStoreProvider();

  const expand = (): void => {
    if (!pinned) {
      if (forceCollapse) updateLocalState("navigation.forceCollapse", false);
      else updateLocalState("navigation.expanded", true);
    }
  };

  const collapse = (): void => {
    updateLocalState("navigation.expanded", false);
  };

  return (
    <Drawer
      variant="permanent"
      elevation={4}
      classes={{
        root: layoutClasses.SideBarRoot,
        paper: classcat([
          layoutClasses.SideBar,
          layoutClasses.SideBarPaper,
          { [layoutClasses.SideBarPaperExpanded]: !forceCollapse && (expanded || pinned) },
        ]),
      }}
      onMouseEnter={expand}
      onMouseLeave={collapse}
    >
      <Stack className={layoutClasses.Header} direction="row" justifyContent="space-between">
        <Link className={layoutClasses.Home} href="/" onClick={() => {}}>
          <Image
            className={layoutClasses.HomeLogo}
            height="50"
            src={forceCollapse || !(expanded || pinned) ? logo : logoExtended}
            alt="Rubicon Carbon"
            priority
          />
        </Link>
        <Maybe condition={!forceCollapse && (expanded || pinned)}>
          <PinNav />
        </Maybe>
      </Stack>
      <Box className={layoutClasses.Content}>
        <NavMenuList type="sidebar" />
      </Box>
    </Drawer>
  );
};

export default SideBar;
