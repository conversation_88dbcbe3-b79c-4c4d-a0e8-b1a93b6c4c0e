import useAuth from "@providers/auth-provider";
import { Menu, MenuItem, Typography, Divider } from "@mui/material";
import { Maybe as MaybeType, PermissionEnum } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { forwardRef, RefObject } from "react";
import { MenuProps } from "../types/menu-props";
import { Maybe } from "@rubiconcarbon/frontend-shared";

import classes from "../styles/main-header.module.scss";

const ProfileMenu = forwardRef<MaybeType<HTMLButtonElement>, MenuProps>(
  ({ open, onClose }: MenuProps, ref: RefObject<MaybeType<HTMLButtonElement>>) => {
    const { user: loginUser, logout } = useAuth();

    const settings = [
      {
        name: "Notification Settings",
        path: "/notification-settings",
        isAuthorized: loginUser?.hasPermission(PermissionEnum.LOGIN) || false,
      },
    ].filter((item) => !!item && item.isAuthorized);

    return (
      <Menu
        className={classes.ProfileMenu}
        anchorEl={open ? ref.current : null}
        open={open}
        onClick={onClose}
        onClose={onClose}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
        slotProps={{
          paper: {
            className: classes.ProfileMenuPaper,
            elevation: 0,
            sx: {
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
              margin: "2px 0 0 -9px",
              "&:before": {
                content: '""',
                display: "block",
                position: "absolute",
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: "background.paper",
                transform: "translateY(-50%) rotate(45deg)",
                zIndex: 0,
              },
            },
          },
        }}
      >
        <MenuItem className={`${classes.ProfileMenuItem} ${classes.NonButton}`} disableRipple>
          <Typography variant="body1">{loginUser?.name}</Typography>
          <Typography className={classes.ProfileUserEmailText} variant="body2">
            {loginUser?.email}
          </Typography>
        </MenuItem>
        <Divider />
        <Maybe condition={!!settings.length}>
          {settings.map(({ name, path }) => (
            <MenuItem className={classes.ProfileMenuItem} key={path}>
              <Link className={classes.ProfileMenuSettingLink} href={path}>
                <Typography variant="body1">{name}</Typography>
              </Link>
            </MenuItem>
          ))}
          <Divider />
        </Maybe>
        <MenuItem className={classes.ProfileMenuItem} onClick={logout}>
          <Typography className={classes.LogOutText} variant="button">
            log out
          </Typography>
        </MenuItem>
      </Menu>
    );
  },
);
ProfileMenu.displayName = "ProfileMenu";

export default ProfileMenu;
