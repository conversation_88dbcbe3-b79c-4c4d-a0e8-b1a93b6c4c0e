import React from "react";
import { PropsWithChildren } from "react";
import { Stack, Typography } from "@mui/material";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import useNavigationMenu from "@providers/navigation-menu-provider";

import classes from "../styles/page.module.scss";

export default function Page({ children }: PropsWithChildren): JSX.Element {
  const { breadcrumbs = [] } = useBreadcrumbs();
  const { permissibleMenus = [], getPermissableMenu } = useNavigationMenu();

  const hasBreadcrumb = breadcrumbs.length > 0 && !!breadcrumbs[breadcrumbs.length - 1];
  const breadcrumb = breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1] : undefined;

  const menu =
    breadcrumb?.path && getPermissableMenu ? getPermissableMenu(breadcrumb.path, permissibleMenus, "equals") : null;

  return (
    <Stack className={classes.Page} gap={2}>
      <Maybe condition={hasBreadcrumb}>
        <Stack
          className={classes.Breadcrumb}
          gap={0.5}
          sx={{
            top: breadcrumbs.length > 1 ? 104 : 64,
          }}
        >
          <Typography className={classes.Header} variant="h5">
            {breadcrumb?.name}
          </Typography>
          <Maybe condition={!!menu && !!menu?.description}>
            <Typography className={classes.SubHeader} variant="button">
              {menu?.description}
            </Typography>
          </Maybe>
        </Stack>
      </Maybe>
      {children}
    </Stack>
  );
}
