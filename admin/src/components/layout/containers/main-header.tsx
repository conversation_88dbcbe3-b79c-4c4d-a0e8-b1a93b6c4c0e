import { AppBar, Avatar, Box, IconButton, Stack, Toolbar, Tooltip, useMediaQuery } from "@mui/material";
import { CloseRounded, Menu as MenuIcon } from "@mui/icons-material";
import { MouseEvent, useContext, useEffect, useRef, useState } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { AuthContext } from "@providers/auth-provider";
import FullscreenMenu from "../components/fullscreen-menu";
import ProfileMenu from "../components/profile-menu";
import { classcat } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";
import Image from "next/image";

import classes from "../styles/main-header.module.scss";
import layoutClasses from "../styles/layout.module.scss";
import logoExtended from "@assets/images/rubicon-logo-primary.svg";

const MainHeader = (): JSX.Element => {
  const { user: loginUser } = useContext(AuthContext);
  const isMobile = useMediaQuery("(max-width: 900px)");

  const profileMenuRef = useRef<HTMLButtonElement | null>(null);
  const [openMobileMenu, setOpenMobileMenu] = useState<boolean>(false);
  const [openProfileMenu, setOpenProfileMenu] = useState<boolean>(false);
  const [tooltipHovered, setToolTipHovered] = useState<boolean>(false);

  useEffect(() => {
    if (!isMobile && openMobileMenu) onCloseMobileMenu();
  }, [isMobile, openMobileMenu]);

  useEffect(() => {
    if (openProfileMenu && openMobileMenu) onCloseMobileMenu();
  }, [openMobileMenu, openProfileMenu]);

  useEffect(() => {
    if (openProfileMenu) setToolTipHovered(false);
  }, [tooltipHovered, openProfileMenu]);

  const toggleMobileMenuOpened = (event: MouseEvent<HTMLButtonElement>): void => {
    event.preventDefault();
    setOpenMobileMenu(!openMobileMenu);
  };

  const toggleProfileMenuOpened = (event: MouseEvent<HTMLButtonElement>): void => {
    event.preventDefault();
    setOpenProfileMenu(!openProfileMenu);

    if (!isMobile && openMobileMenu) onCloseMobileMenu();
  };

  const onCloseMobileMenu = (): void => setOpenMobileMenu(false);

  const onCloseProfileMenu = (): void => setOpenProfileMenu(false);

  const handleOnHover = (event: MouseEvent<HTMLDivElement>, hovered: boolean): void => {
    event.preventDefault();
    setToolTipHovered(hovered);
  };

  return (
    <AppBar className={classcat([layoutClasses.Header, classes.AppBar])} position="fixed" elevation={0}>
      <Toolbar component={Stack} className={classes.Toolbar} direction="row" alignItems="center">
        <>
          <IconButton className={classes.MobileMenuIconButton} size="large" onClick={toggleMobileMenuOpened}>
            <Maybe condition={openMobileMenu}>
              <CloseRounded className={classes.MobileMenuIconIcon} />
            </Maybe>
            <Maybe condition={!openMobileMenu}>
              <MenuIcon className={classes.MobileMenuIconIcon} />
            </Maybe>
          </IconButton>
          <FullscreenMenu open={openMobileMenu} onClose={onCloseMobileMenu} />
        </>

        <Link className={classes.Home} href="/" onClick={onCloseMobileMenu}>
          <Image className={classes.HomeLogo} height="50" src={logoExtended} alt="Rubicon Carbon" priority />
        </Link>

        <Tooltip
          title="Open settings"
          arrow={true}
          open={tooltipHovered && !openProfileMenu}
          onMouseEnter={(event: MouseEvent<HTMLDivElement>) => handleOnHover(event, true)}
          onMouseLeave={(event: MouseEvent<HTMLDivElement>) => handleOnHover(event, false)}
        >
          <Box>
            <IconButton ref={profileMenuRef} onClick={toggleProfileMenuOpened}>
              <Avatar alt={loginUser?.name ?? ""} src={loginUser?.picture} />
            </IconButton>
            <ProfileMenu ref={profileMenuRef} open={openProfileMenu} onClose={onCloseProfileMenu} />
          </Box>
        </Tooltip>
      </Toolbar>
    </AppBar>
  );
};

export default MainHeader;
