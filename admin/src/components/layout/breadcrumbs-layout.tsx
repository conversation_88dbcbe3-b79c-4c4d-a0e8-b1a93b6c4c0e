"use client";

import { PropsWithChildren } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import BreadCrumbs from "../ui/breadcrumbs/breadcrumbs";
import { BreadcrumbProvider } from "@providers/breadcrumb-provider";
import { useAppPathname } from "@hooks/router-hooks";

export default function BreadcrumbsLayout({ children }: PropsWithChildren): JSX.Element {
  const pathname = useAppPathname();

  return (
    <BreadcrumbProvider>
      <>
        <Maybe condition={pathname !== "/"}>
          <BreadCrumbs />
        </Maybe>
        <>{children}</>
      </>
    </BreadcrumbProvider>
  );
}
