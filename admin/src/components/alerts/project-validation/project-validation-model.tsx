import { ExtendedAllocation } from "@/types/book";
import { AssetType, TrimmedProjectVintageResponse, uuid } from "@rubiconcarbon/shared-types";

export enum ViolationEnum {
  BUFFER = "Buffer",
  RCT_STANDARD = "Below RCT Standard",
  SUSPENDED = "Suspended",
  COUNTRY = "More than 35 percent of the portfolio is allocated to a single country",
  PROJECT = "More than 25 percent of the portfolio is allocated to a single project",
  COMPLIANCE_TYPE = "Not compliance market credit",
}

export interface VintageViolations {
  registryId: string;
  projectName: string;
  vintageId: uuid;
  vintageName: string;
  violationsType: ViolationEnum[];
}

export interface ProjectViolations {
  violationsType: ViolationEnum[];
}

export interface ProjectVintage {
  id: uuid;
  projectName: string;
  projectId: uuid;
  projectRegistryId?: string;
  amountAllocated: number;
  projectLocation?: string;
}

export function mapProjectVintage(inputData: ExtendedAllocation[] = []): ProjectVintage[] {
  const nonVintageAssetse = inputData.filter((f) => f.asset.type !== AssetType.REGISTRY_VINTAGE);
  if (nonVintageAssetse.length > 0) {
    // todo : @kofi/@adir idk when this is used so idk if we should have an alert or something here.
  }

  // since this is to map projectVintage, only map allocations that are vintages
  // maybe also filter out ones that don't have a projectVintage
  // though that should be an error so maybe throw an error if it doesn't
  return inputData
    .filter((f) => f.asset.type === AssetType.REGISTRY_VINTAGE)
    .map((row) => {
      const projectVintage = row.detailedAsset as TrimmedProjectVintageResponse;
      return {
        id: projectVintage.id,
        projectName: projectVintage.project.name,
        projectId: projectVintage.project.id,
        projectRegistryId: projectVintage.project.registryProjectId,
        amountAllocated: row.amountAllocated,
        location: projectVintage.project?.country.name,
      };
    });
}
