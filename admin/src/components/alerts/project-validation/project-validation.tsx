import React, { useState } from "react";
import { Badge, Box, Stack, Table, TableBody, TableCell, TableRow, Typography } from "@mui/material";
import { TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import Decimal from "decimal.js";
import { isEmpty } from "lodash";
import CancelIcon from "@mui/icons-material/Cancel";
import {
  ViolationEnum,
  VintageViolations,
  ProjectVintage,
  ProjectViolations,
  mapProjectVintage,
} from "./project-validation-model";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { alphabeticalThenNumberSort } from "@utils/comparators/comparator";
import { ExtendedAllocation } from "@/types/book";

import classes from "../styles/validation.module.scss";

const PROJECT_LOCATION_PERCENT_LIMIT = 35;
const PROJECT_ALLOCATION_PERCENT_LIMIT = 25;
const COMPLIANCE_PROJECT_TYPE = "Compliance Permit";

const getVintagesViolationList = (
  vintages: TrimmedProjectVintageResponse[],
  alertsDef: AlertsDef,
): VintageViolations[] => {
  const violations: VintageViolations[] = [];
  if (!isEmpty(vintages)) {
    vintages.forEach((pv) => {
      const violationsTypeArr: ViolationEnum[] = [];
      if (alertsDef?.complianceType === true && pv?.project?.projectType?.type !== COMPLIANCE_PROJECT_TYPE)
        violationsTypeArr.push(ViolationEnum.COMPLIANCE_TYPE);
      if (alertsDef?.suspended === true && pv?.project?.suspended) violationsTypeArr.push(ViolationEnum.SUSPENDED);
      if (alertsDef?.rctStandard === true && pv?.project?.rctStandard === false)
        violationsTypeArr.push(ViolationEnum.RCT_STANDARD);
      if (
        alertsDef?.buffer === true &&
        !!pv?.riskBufferPercentage &&
        new Decimal(pv.riskBufferPercentage).mul(100).greaterThan(100)
      ) {
        violationsTypeArr.push(ViolationEnum.BUFFER);
      }

      if (violationsTypeArr.length > 0) {
        const existingViolation = violations.find((v) => v.vintageId === pv.id);
        if (existingViolation) {
          existingViolation.violationsType = violationsTypeArr;
        } else {
          violations.push({
            projectName: pv?.project.name,
            registryId: pv?.project?.registryProjectId,
            vintageId: pv.id,
            vintageName: pv.name,
            violationsType: violationsTypeArr,
          });
        }
      }
    });
  }
  return violations;
};

function sumVintagesByGroup(projectVintages: ProjectVintage[], groupField: string): Record<string, number> {
  return projectVintages.reduce(
    (groups, pv) => {
      const accumulator = { ...groups };
      const key = pv[groupField];
      if (!accumulator[key]) {
        accumulator[key] = 0;
      }
      accumulator[key] += pv.amountAllocated;
      return accumulator;
    },
    {} as Record<string, number>,
  );
}

function mapGroupsToArray(groups: Record<string, number>): {
  name: string;
  value: number;
}[] {
  if (groups) {
    const sum = Object.keys(groups).reduce((a, b) => a + groups[b], 0);
    return Object.keys(groups).map((groupName) => {
      const roundedGroupValue = Math.round(((groups[groupName] * 100) / sum) * 100) / 100;
      return {
        name: groupName,
        value: roundedGroupValue,
      };
    });
  }
  return null;
}

const isGroupExceedPercentage = (projectVintages: ProjectVintage[], groupName: string, percent: number): boolean => {
  const groups = mapGroupsToArray(sumVintagesByGroup(projectVintages, groupName));
  if (groups) {
    return groups.find((g) => g.value > percent) ? true : false;
  }
  return false;
};

const getProjectsViolationList = (bookAllocations: ExtendedAllocation[], alertsDef: AlertsDef): ProjectViolations[] => {
  const projectViolations: ProjectViolations[] = [];

  const projectVintages = mapProjectVintage(bookAllocations);

  if (!isEmpty(projectVintages)) {
    if (
      alertsDef?.location === true &&
      isGroupExceedPercentage(projectVintages, "location", PROJECT_LOCATION_PERCENT_LIMIT)
    ) {
      projectViolations.push({
        violationsType: [ViolationEnum.COUNTRY],
      });
    }
    if (
      alertsDef?.allocation === true &&
      isGroupExceedPercentage(projectVintages, "projectId", PROJECT_ALLOCATION_PERCENT_LIMIT)
    ) {
      projectViolations.push({
        violationsType: [ViolationEnum.PROJECT],
      });
    }
  }

  return projectViolations;
};

export interface AlertsDef {
  rctStandard?: boolean;
  suspended?: boolean;
  buffer?: boolean;
  location?: boolean;
  allocation?: boolean;
  complianceType?: boolean;
}

interface ProjectValidationProps {
  extendedAllocations: ExtendedAllocation[];
  entity?: "Book" | "Portfolio";
  alertsDef?: AlertsDef;
}

export default function ProjectValidation(props: ProjectValidationProps): JSX.Element {
  const {
    extendedAllocations,
    entity = "Portfolio",
    alertsDef = {
      rctStandard: true,
      suspended: true,
      buffer: true,
      location: true,
      allocation: true,
      complianceType: false,
    },
  } = props;

  const [showViolationDetails, setShowViolationDetails] = useState<boolean>(true);

  if (!extendedAllocations) return;

  const vintagesViolations = getVintagesViolationList(
    extendedAllocations?.map((c) => c.detailedAsset as TrimmedProjectVintageResponse),
    alertsDef,
  );
  const projectViolations = getProjectsViolationList(extendedAllocations, alertsDef);
  const violationsCount =
    (vintagesViolations
      ? vintagesViolations.reduce((accumulator, currentValue) => accumulator + currentValue.violationsType.length, 0)
      : 0) + (projectViolations ? projectViolations.length : 0);

  const showHideHandler = (): void => {
    setShowViolationDetails(!showViolationDetails);
  };

  return (
    <Maybe condition={vintagesViolations?.length > 0 || projectViolations?.length > 0}>
      <Box className={classes.violationContainer}>
        <Stack direction="row">
          <WarningAmberIcon className={classes.violationIcon} />
          <Stack direction="column" sx={{ width: "100%" }}>
            <Box className={classes.violationBox}>
              <Typography variant="body2" component="h4" className={classes.violationTitle}>
                {`${entity} policy violation`}
              </Typography>
              <Typography variant="body2" component="h4" className={classes.dismissBtn} onClick={showHideHandler}>
                {showViolationDetails ? (
                  "Hide"
                ) : (
                  <>
                    Show
                    <Badge
                      badgeContent={violationsCount}
                      color={"error"}
                      overlap="circular"
                      style={{ transform: "translate(10px, -8px)" }}
                    ></Badge>
                  </>
                )}
              </Typography>
            </Box>
            <Box className={classes.violationDetailsBox}>
              <Maybe condition={showViolationDetails}>
                <Typography variant="body2" className={classes.violationSubTitle}>
                  {`The following assets are violating the ${entity.toLowerCase()} policy.`}
                </Typography>
                <Table>
                  <TableBody>
                    {vintagesViolations
                      .sort((a, b) =>
                        alphabeticalThenNumberSort(
                          { x: a.projectName, y: +a.vintageName },
                          { x: b.projectName, y: +b.vintageName },
                        ),
                      )
                      .map((v) => (
                        <TableRow key={v.vintageId} className={classes.tableRow}>
                          <TableCell align="left" className={classes.tableCell} sx={{ width: "380px" }}>
                            <Stack direction="row" gap={0.5}>
                              <CancelIcon className={classes.cancelIcon} />
                              <Typography variant="body2" component="p">
                                {`Violation(s): ${v.violationsType.join(", ")}`}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell align="left" className={classes.tableCell} sx={{ paddingLeft: "15px" }}>
                            {`${v?.registryId} - ${v.projectName} - ${v.vintageName}`}
                          </TableCell>
                        </TableRow>
                      ))}
                    {projectViolations.map((p, idx) => (
                      <TableRow key={`${p.violationsType.toString()}_${idx}`} className={classes.tableRow}>
                        <TableCell colSpan={2} align="left" className={classes.tableCell}>
                          <Stack direction="row" gap={0.5}>
                            <CancelIcon className={classes.cancelIcon} />
                            <Typography variant="body2" component="p">
                              {`Violation(s): ${p.violationsType}`}
                            </Typography>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Maybe>
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Maybe>
  );
}
