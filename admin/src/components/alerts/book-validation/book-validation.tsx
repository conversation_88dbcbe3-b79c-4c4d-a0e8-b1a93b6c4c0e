import React, { useState } from "react";
import { Badge, Box, Stack, Table, TableBody, TableCell, TableRow, Typography } from "@mui/material";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { isEmpty } from "lodash";
import CancelIcon from "@mui/icons-material/Cancel";
import { ViolationEnum, BookViolations } from "./book-validation-model";
import { Maybe, calculator, isNothing, pickFromRecord, toDecimal } from "@rubiconcarbon/frontend-shared";
import { AssetType, GroupingParentResponse, GroupedPriceResponse } from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";

import classes from "../styles/validation.module.scss";

export interface ViolationInput {
  holdingPriceMax: Decimal;
  totalPriceAllocated: Decimal;
  totalPricePendingBuy: Decimal;
  totalPricePendingSell: Decimal;
}

export function hasBookViolation(violationInput: ViolationInput): boolean {
  const { holdingPriceMax, totalPriceAllocated, totalPricePendingBuy, totalPricePendingSell } = violationInput;
  if (!isNothing(holdingPriceMax) && !toDecimal(holdingPriceMax).isZero()) {
    const total = calculator(totalPriceAllocated).add(totalPricePendingBuy).subtract(totalPricePendingSell).calculate();
    if (!!total && calculator(total, { treatNothingAsNaN: true }).isGreaterThan(holdingPriceMax)) {
      return true;
    }
  }
  return false;
}

const getBooksViolationList = (groupings: GroupingParentResponse[]): BookViolations[] => {
  const booksViolations: BookViolations[] = [];
  if (!isEmpty(groupings)) {
    groupings.forEach((grouping) => {
      const vintagesAllocation = grouping.ownerAllocationsByAssetType?.find(
        ({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE,
      );
      if (
        hasBookViolation({
          holdingPriceMax: grouping?.limit?.holdingPriceMax,
          ...pickFromRecord<Partial<GroupedPriceResponse>>(vintagesAllocation?.groupedPrices, [
            "totalPriceAllocated",
            "totalPricePendingBuy",
            "totalPricePendingSell",
          ]),
        } as ViolationInput)
      ) {
        booksViolations.push({
          bookName: grouping.name,
          BookType: grouping?.books?.at(0)?.type,
          violationsType: [ViolationEnum.LIMIT_EXCEEDED],
        });
      }
    });
  }

  return booksViolations;
};

interface BookValidationProps {
  books: GroupingParentResponse[];
}

export default function BookValidation(props: BookValidationProps): JSX.Element {
  const { books } = props;
  const [showViolationDetails, setShowViolationDetails] = useState<boolean>(true);
  if (!books) return;

  const booksViolations = getBooksViolationList(books);
  const violationsCount = booksViolations
    ? booksViolations.reduce((accumulator, currentValue) => accumulator + currentValue.violationsType.length, 0)
    : 0;

  const showHideHandler = (): void => {
    setShowViolationDetails(!showViolationDetails);
  };

  return (
    <Maybe condition={violationsCount > 0}>
      <Box className={classes.violationContainer}>
        <Stack direction="row">
          <WarningAmberIcon className={classes.violationIcon} />
          <Stack direction="column" sx={{ width: "100%" }}>
            <Box className={classes.violationBox}>
              <Typography variant="body2" component="h4" className={classes.violationTitle}>
                {`Book policy violation`}
              </Typography>
              <Typography variant="body2" component="h4" className={classes.dismissBtn} onClick={showHideHandler}>
                {showViolationDetails ? (
                  "Hide"
                ) : (
                  <>
                    Show
                    <Badge
                      badgeContent={violationsCount}
                      color={"error"}
                      overlap="circular"
                      style={{ transform: "translate(10px, -8px)" }}
                    ></Badge>
                  </>
                )}
              </Typography>
            </Box>
            <Box className={classes.violationDetailsBox}>
              <Maybe condition={showViolationDetails}>
                <Table>
                  <TableBody>
                    {booksViolations.map((b, idx) => (
                      <TableRow key={`${b.violationsType.toString()}_${idx}`} className={classes.tableRow}>
                        <TableCell colSpan={2} align="left" className={classes.tableCell}>
                          <Stack direction="row" gap={0.5}>
                            <CancelIcon className={classes.cancelIcon} />
                            <Typography variant="body2" component="p">
                              {`Violation(s): ${b.violationsType.join(", ")}`}
                            </Typography>
                            <Typography variant="body2" component="p" sx={{ paddingLeft: "20px" }}>
                              {b.bookName}
                            </Typography>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Maybe>
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Maybe>
  );
}
