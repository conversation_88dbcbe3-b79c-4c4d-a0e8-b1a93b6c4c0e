import { AxiosContext } from "@providers/axios-provider";
import { CSSProperties, useCallback, useContext, useEffect, useRef } from "react";
import { embedDashboard, UiConfigType } from "@superset-ui/embedded-sdk";

export function SupersetDashboard(props: {
  dashboard: string;
  config: UiConfigType;
  style?: CSSProperties | undefined;
  auth?: { token: string; url: string };
}): JSX.Element {
  const { api } = useContext(AxiosContext);
  const containerRef = useRef(null);

  const { dashboard, config, style } = props;

  const styleIframe = useCallback(() => {
    const iframe = (containerRef?.current as any)?.children?.[0];

    if (iframe) {
      iframe.style.width = "100%";
      iframe.style.height = "100%";
      iframe.style.border = "none";
      iframe.width = "100%";
      iframe.height = "100%";
    }

    (containerRef.current as any).minHeight = "100%";
  }, [containerRef]);

  useEffect(() => {
    let unmount: (() => void) | undefined = undefined;

    (async (): Promise<void> => {
      let auth = props.auth;
      if (!auth) {
        auth = await api
          .post("admin/superset/token", { resources: [{ id: dashboard, type: "dashboard" }] })
          .then((x) => x.data);
      }

      if (containerRef?.current) {
        embedDashboard({
          id: dashboard,
          supersetDomain: auth?.url ?? "",
          mountPoint: containerRef.current,
          fetchGuestToken: async () => auth?.token ?? "",
          dashboardUiConfig: config,
        }).then((x) => {
          styleIframe();
          unmount = x.unmount;
        });

        styleIframe(); // try to style it right away if it's already mounted
      }
    })();

    return (): void => {
      if (unmount) unmount();
    };
  }, [api, containerRef, config, styleIframe, props.auth, dashboard]);

  return <div ref={containerRef} style={style}></div>;
}
