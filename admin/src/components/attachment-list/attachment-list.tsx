import { CS<PERSON>roperties, MouseE<PERSON>, ReactNode, useContext, useState } from "react";
import dateFormatter from "@/utils/formatters/date-formatter";
import { Delete } from "@mui/icons-material";
import { ListItem, Typography, IconButton, List, Stack, Divider } from "@mui/material";
import { DocumentResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import useRefreshableS3Link from "@/hooks/use-refreshable-s3-link";
import { TEN_MINUTES } from "@constants/constants";
import { DocumentTypeUILabel } from "@constants/documents";
import ConfirmationModal from "../ui/dialogs/confirmation-dialog";
import Link from "next/link";
import useDocumentsApi from "@hooks/use-documents-api";
import DialogTheme from "../ui/dialogs/dialog-themes";
import { AuthContext } from "@providers/auth-provider";
import MatIcon from "../ui/mat-icon/mat-icon";

import classes from "./attachment-list.module.scss";

export type AttachmentDocument = {
  as?: "link" | "button" | "icon";
  canDelete?: boolean;
  renderedFileName?: ReactNode;
} & DocumentResponse;

type AttachmentProps = {
  document: AttachmentDocument;
  style?: CSSProperties;
} & Pick<AttachmentListProps, "deleteConfirmation" | "onRemoveSuccess" | "onRemoveError">;

type AttachmentListProps = {
  attachments: AttachmentDocument[];
  deleteConfirmation?: {
    title: string;
    content: ReactNode | ((props: { document: AttachmentDocument }) => JSX.Element);
  };
  style?: CSSProperties;
  onRemoveSuccess?: () => Promise<void>;
  onRemoveError?: (error: any) => Promise<void>;
};

export const Attachment = ({
  document: doc,
  deleteConfirmation,
  style = {},
  onRemoveSuccess,
  onRemoveError,
}: AttachmentProps): JSX.Element => {
  const { user } = useContext(AuthContext);
  const {
    id,
    type,
    filename,
    uploadedAt,
    createdAt,
    downloadUrl,
    as = "link",
    canDelete = true,
    renderedFileName,
  } = doc;

  const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);

  const refreshedLinks = useRefreshableS3Link<DocumentResponse, { downloadUrl: string }>({
    seedLinks: { downloadUrl },
    fetchApiUrl: `/admin/documents/${id}`,
    every: TEN_MINUTES,
    linkProps: ["downloadUrl"],
  });

  const { remove } = useDocumentsApi({
    id,
    onRemoveSuccess: () => {
      onRemoveSuccess?.();
      setShowConfirmationModal(false);
    },
    onRemoveError: (error: any) => {
      onRemoveError?.(error);
      setShowConfirmationModal(false);
    },
  });

  const handleDocumentRemoval = async (event: MouseEvent<HTMLButtonElement>): Promise<void> => {
    event.preventDefault();
    await remove();
  };

  return (
    <>
      <Stack
        className={as === "button" ? classes.Button : ""}
        direction="row"
        alignItems="center"
        gap={0.5}
        sx={{ justifyContent: "space-between" }}
      >
        <Link className={classes.Link} href={refreshedLinks?.downloadUrl} target="_blank" download={filename}>
          <Maybe condition={as === "icon"}>
            <Stack direction="row" alignItems="center" gap={0.5}>
              <IconButton>
                <MatIcon value="file_download" variant="round" size={24} />
              </IconButton>
              <Maybe condition={canDelete}>
                <Divider orientation="vertical" sx={{ height: 24 }} />
              </Maybe>
            </Stack>
          </Maybe>
          <Maybe condition={as !== "icon"}>
            <Stack>
              <Typography className={classes.FileNameText} style={style}>
                {renderedFileName || (
                  <>
                    {DocumentTypeUILabel[type]} - {dateFormatter(createdAt, "MMM dd, yyyy. hh:mm:ss a")}
                  </>
                )}
              </Typography>
              <Maybe condition={as === "button"}>
                <Typography className={classes.UploadedAtText}>Uploaded: {dateFormatter(uploadedAt)}</Typography>
              </Maybe>
            </Stack>
          </Maybe>
        </Link>
        <Maybe condition={canDelete}>
          <IconButton
            disabled={!user?.hasPermission(PermissionEnum.DOCUMENTS_DELETE)}
            onClick={(event: MouseEvent<HTMLButtonElement>) =>
              deleteConfirmation ? setShowConfirmationModal(true) : handleDocumentRemoval(event)
            }
          >
            <Delete color={as === "link" ? "inherit" : "primary"} />
          </IconButton>
        </Maybe>
      </Stack>
      <Maybe condition={!!deleteConfirmation}>
        <ConfirmationModal
          isOpen={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          dialogTheme={DialogTheme.ERROR}
          title={deleteConfirmation?.title ?? ""}
          dialogButtons={[
            {
              label: "Delete",
              variant: "contained",
              tooltip: "proceed with delete",
              color: "error",
              style: { borderRadius: "4px" },
              onClickHandler: handleDocumentRemoval,
            },
          ]}
        >
          <Typography variant="body1" component="p">
            {typeof deleteConfirmation?.content === "function"
              ? deleteConfirmation?.content({ document: doc })
              : deleteConfirmation?.content}
          </Typography>
        </ConfirmationModal>
      </Maybe>
    </>
  );
};

const AttachmentList = ({
  attachments = [],
  deleteConfirmation,
  style = {},
  onRemoveSuccess,
  onRemoveError,
}: AttachmentListProps): JSX.Element => {
  return (
    <List className={classes.List}>
      {attachments.map((document) => (
        <ListItem key={document.id} className={classes.Item}>
          <Attachment
            document={document}
            deleteConfirmation={deleteConfirmation}
            style={style}
            onRemoveSuccess={onRemoveSuccess}
            onRemoveError={onRemoveError}
          />
        </ListItem>
      ))}
    </List>
  );
};

export default AttachmentList;
