"use client";

import { Box, LinearProgress } from "@mui/material";
import { keyframes } from "@mui/system";

// Create a subtle fade animation
const fade = keyframes`
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
`;

// Global loading component
export default function GlobalLoading(): JSX.Element {
  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="80vh"
      sx={{
        animation: `${fade} 1.5s ease-in-out infinite`,
      }}
    >
      <Box sx={{ width: "40%", maxWidth: "400px" }}>
        <LinearProgress
          sx={{
            height: 4,
            borderRadius: 2,
          }}
        />
      </Box>
    </Box>
  );
}
