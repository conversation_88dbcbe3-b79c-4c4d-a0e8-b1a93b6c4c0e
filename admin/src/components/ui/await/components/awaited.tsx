import { SxProps, Skeleton, Grid } from "@mui/material";
import { uuid } from "@rubiconcarbon/shared-types";
import { ElementType, PropsWithChildren, useEffect, useState, useMemo } from "react";

type Condition = boolean | (() => boolean);

type AwaitedProps = {
  component?: ElementType;
  direction?: "row" | "column";
  variant: "text" | "circular" | "rectangular" | "rounded";
  repeat?: number;
  animation?: false | "wave" | "pulse";
  sx?: SxProps;
  className?: string;
  itemSx?: SxProps;
  itemClassName?: string;
  delay?: number;
  animate?: boolean | (() => boolean);
};

const resolve = (condition: Condition): boolean => (typeof condition === "function" ? condition() : condition);

/**
 * The `Awaited` component generates a specified number of skeleton loading components in a grid layout.
 * It supports delayed animation start and conditional rendering based on either a boolean value
 * or a function that returns a boolean.
 */
const Awaited = ({
  component = "div",
  direction = "row",
  variant,
  repeat = 1,
  animation = "pulse",
  sx = {},
  className = "",
  itemSx = {},
  itemClassName = "",
  delay = 0,
  animate = true,
}: PropsWithChildren<AwaitedProps>): JSX.Element => {
  const [isVisible, setIsVisible] = useState(delay === 0);

  const canAnimate = useMemo(() => resolve(animate), [animate]);

  useEffect(() => {
    if (delay > 0 && canAnimate) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, delay);

      return (): void => clearTimeout(timer);
    }
  }, [canAnimate, delay]);

  if (!canAnimate) return null;

  if (!isVisible) {
    return (
      <Grid className={className} item container direction={direction} gap={1} sx={sx}>
        {new Array(repeat).fill(null).map(() => (
          <Grid key={uuid()} item width="100%">
            <div style={{ visibility: "hidden" }}>
              <Skeleton
                className={itemClassName}
                component={component}
                variant={variant}
                animation={false}
                sx={itemSx}
              />
            </div>
          </Grid>
        ))}
      </Grid>
    );
  }

  const skeleton = (
    <Skeleton className={itemClassName} component={component} variant={variant} animation={animation} sx={itemSx} />
  );

  return (
    <Grid className={className} item container direction={direction} gap={1} sx={sx}>
      {new Array(repeat)
        .fill(null)
        .map(() => uuid())
        .map((key) => (
          <Grid key={key} item width="100%">
            {skeleton}
          </Grid>
        ))}
    </Grid>
  );
};

export default Awaited;
