import { Box } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs, { Dayjs } from "dayjs";
import { useCallback, useState } from "react";

interface IFormField {
  error?: boolean;
  message?: string;
  value?: Dayjs;
}

export type DateRange = {
  dateFrom: IFormField;
  dateTo: IFormField;
};

interface DateRangeProps {
  dateFrom?: Dayjs;
  dateTo?: Dayjs;
  disabled?: boolean;
  shouldDisableYear?: (date: Dayjs, range?: "from" | "to") => boolean;
  onChange: (from: Dayjs, to: Dayjs) => void;
}

export default function YearRangePicker(props: DateRangeProps): JSX.Element {
  const { dateFrom, dateTo, disabled, shouldDisableYear, onChange } = props;
  const [from, setFrom] = useState<IFormField>({
    value: dateFrom,
    error: false,
    message: "",
  });
  const [to, setTo] = useState<IFormField>({
    value: dateTo,
    error: false,
    message: "",
  });

  const dateFromChangeHandler = useCallback(
    (selectedDate) => {
      const year = dayjs(selectedDate).year();
      const newFromDate = dayjs(new Date(`1/1/${year}`));

      setFrom({
        ...from,
        value: newFromDate,
      });

      if (to?.value) onChange(newFromDate, to.value);
    },
    [from, to.value, onChange],
  );

  const dateToChangeHandler = useCallback(
    (selectedDate) => {
      const year = dayjs(selectedDate).year();
      const newToDate = dayjs(new Date(`12/31/${year}`));

      setTo({
        ...to,
        value: newToDate,
      });

      if (from?.value) onChange(from.value, newToDate);
    },
    [to, from.value, onChange],
  );

  return (
    <Box display={"inline-flex"}>
      <DatePicker
        views={["year"]}
        format="YYYY"
        sx={{
          marginBottom: "5px",
        }}
        slotProps={{
          textField: {
            size: "small",
            error: from?.error,
            helperText: from?.message,
            sx: { width: "105px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
          },
          popper: {
            sx: {
              "*.MuiPickersYear-yearButton": {
                "&:disabled": {
                  color: "gray",
                },
              },
            },
          },
        }}
        value={from.value}
        disabled={disabled}
        shouldDisableYear={(year: Dayjs) => !!shouldDisableYear?.(year, "from")}
        onChange={dateFromChangeHandler}
      />
      <Box sx={{ padding: "8px" }}> - </Box>
      <DatePicker
        views={["year"]}
        format="YYYY"
        sx={{
          marginBottom: "5px",
        }}
        slotProps={{
          textField: {
            size: "small",
            error: to?.error,
            helperText: to?.message,
            sx: { width: "105px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
          },
          popper: {
            sx: {
              "*.MuiPickersYear-yearButton": {
                "&:disabled": {
                  color: "gray",
                },
              },
            },
          },
        }}
        value={to.value}
        disabled={disabled}
        shouldDisableYear={(year: Dayjs) => !!shouldDisableYear?.(year, "to")}
        onChange={dateToChangeHandler}
      />
    </Box>
  );
}
