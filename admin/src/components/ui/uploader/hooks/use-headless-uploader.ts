import { AxiosContext } from "@providers/axios-provider";
import { ChangeEvent, DragEvent, MouseEvent, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { defaultUniqueFileKey, hasError } from "../utils/hook";
import {
  HeadlessUploaderProps,
  FileDataRecord,
  GeneralUploadErrorType,
  UploaderErrorType,
  FileDataError,
  FileData,
  OnFileUploadSuccessMetaData,
} from "../types/hook";
import { AxiosError, AxiosProgressEvent } from "axios";
import { FileType } from "@/types/file-type";
import { useUpdateEffect } from "react-use";
import usePerformantEffect from "@hooks/use-performant-effect";

type UseHeadlessUploaderReturn = {
  selectedFiles: File[];
  records: FileDataRecord;
  generalErrors: "TOO_MANY_FILES"[];
  dragging: boolean;
  uploading: boolean;
  disableUpload: boolean;
  handleFileChange: (event: ChangeEvent<HTMLInputElement>) => void;
  handleFileDrop: (event: DragEvent<HTMLElement>) => void;
  handleDragOver: (event: DragEvent<HTMLElement>) => void;
  handleDragLeave: (event: DragEvent<HTMLElement>) => void;
  handleFileRemoval: (event: MouseEvent<HTMLButtonElement>, file: File) => void;
  handleUpload: (event: MouseEvent<HTMLButtonElement>) => Promise<void>;
  handleSingleFileUploadRetry: (event: MouseEvent<HTMLButtonElement>, file: File) => Promise<void>;
};

const useHeadlessUploader = (props: HeadlessUploaderProps): UseHeadlessUploaderReturn => {
  const {
    inputId,
    api,
    encType,
    maxFiles = 1,
    maxFileSize,
    concurrentUploads = 2,
    allowedExtensions = [],
    useUniqueLink = true,
    canUpload = true,
    canDragAndDrop,
    isS3Upload = true,
    hasInterrimStep,
    uploadOnFileChange,
    clearOnFileUploadSuccess,
    uploadMethod = "put",
    uniqueFileKey = defaultUniqueFileKey,
    uploadLink,
    onExposeUploadHandler,
    onFileDrop,
    onFileChange,
    onFileRemoval = async (): Promise<boolean> => true,
    onFileUploadSuccess,
    onFileUploadError,
    onUploadingStatusChange,
    handleUploadRequest,
  } = props;

  const { http } = useContext(AxiosContext);

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [records, setRecords] = useState<FileDataRecord>({});
  const [generalErrors, setGeneralErrors] = useState<GeneralUploadErrorType[]>([]);
  const [dragging, setDragging] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);

  const uploadableFiles = useMemo(() => {
    const filtered = Object.values(records).filter(({ progress, errors }) => progress < 100 && !hasError(errors));
    return filtered;
  }, [records]);

  const uploadFailureFiles = useMemo(() => {
    const filtered = Object.values(records).filter(({ errors }) => hasError(errors) && errors.uploadFailure);
    return filtered;
  }, [records]);

  const disableUpload = useMemo(
    () => !canUpload || uploading || uploadableFiles.length === 0,
    [canUpload, uploading, uploadableFiles],
  );

  useEffect(
    () => {
      const _generalErrors = buildGeneralErrors(["TOO_MANY_FILES"]);
      setGeneralErrors(_generalErrors);

      if (_generalErrors.length > 0) return;

      const selectedFileKeys = selectedFiles.map((file) => uniqueFileKey(file));

      setRecords((previous) =>
        selectedFiles.reduce(
          (accum: FileDataRecord, file: File) => {
            const key = uniqueFileKey(file);

            const checkers = [
              "EMPTY_FILE",
              maxFileSize ? "FILE_TOO_BIG" : null,
              allowedExtensions.length ? "INVALID_FILE_TYPE" : null,
            ].filter((value) => !!value) as UploaderErrorType[];

            const errors = buildFileErrors(file, checkers);

            if (accum[key]) {
              accum = {
                [key]: {
                  ...accum[key],
                  errors,
                },
              };
            } else {
              accum = {
                ...accum,
                [key]: {
                  file,
                  progress: 0,
                  errors,
                },
              };
            }

            return accum;
          },
          maxFiles > 1
            ? Object.keys(previous)
                .filter((key) => selectedFileKeys.includes(key))
                .reduce((accum, key) => ({ ...accum, [key]: previous[key] }), {})
            : {},
        ),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedFiles],
  );

  useEffect(() => {
    if (onExposeUploadHandler) {
      if (uploadableFiles.length) {
        if (!hasInterrimStep) onExposeUploadHandler(inputId, handleUpload);
      } else onExposeUploadHandler(inputId, null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasInterrimStep, uploadableFiles, uploadOnFileChange]);

  usePerformantEffect(() => {
    if (onUploadingStatusChange) onUploadingStatusChange(uploading);
  }, [uploading]);

  useUpdateEffect(() => {
    if (clearOnFileUploadSuccess) {
      Object.values(records)
        .filter(({ progress }) => progress === 100)
        .forEach(({ file }) => {
          const key = uniqueFileKey(file);

          const filteredSelectedFiles = Object.assign(
            [],
            selectedFiles.filter((file: File) => key !== uniqueFileKey(file)),
          );
          setSelectedFiles(filteredSelectedFiles);
        });
    }
  }, [clearOnFileUploadSuccess, records]);

  const buildFileErrors = useCallback(
    (file: File, checkers: UploaderErrorType[]): FileDataError => {
      const errors: FileDataError = {};

      for (const checker of checkers) {
        switch (checker) {
          case "EMPTY_FILE":
            if (file.size === 0) errors.emptyFile = true;
            else errors.emptyFile = false;

            break;
          case "FILE_TOO_BIG":
            if (maxFileSize !== Infinity && file.size > (maxFileSize ?? file.size)) errors.fileTooBig = true;
            else errors.fileTooBig = false;

            break;
          case "INVALID_FILE_TYPE":
            if (!allowedExtensions.includes(file.type as FileType)) errors.invalidType = true;
            else errors.invalidType = false;

            break;
          default:
            break;
        }
      }

      return errors;
    },
    [maxFileSize, allowedExtensions],
  );

  const buildGeneralErrors = useCallback(
    (checkers: GeneralUploadErrorType[]): GeneralUploadErrorType[] => {
      const errors: GeneralUploadErrorType[] = [];

      for (const checker of checkers) {
        switch (checker) {
          case "TOO_MANY_FILES":
            if (selectedFiles.length > maxFiles) errors.push("TOO_MANY_FILES");
            break;
          default:
            break;
        }
      }

      return errors;
    },
    [selectedFiles, maxFiles],
  );

  const clearFileErrors = (file: File): void => {
    const key = uniqueFileKey(file);

    const record = records[key];
    record.errors = {};

    setRecords((previous) => ({
      ...previous,
      [key]: record,
    }));
  };

  const uploadApi = useCallback(
    async (file?: File): Promise<void> => {
      let _uploadLink = !useUniqueLink ? (typeof uploadLink === "string" ? uploadLink : await uploadLink(file)) : "";

      const filesToUpload = Object.assign(
        [],
        file
          ? [
              [...uploadableFiles, ...uploadFailureFiles].find(
                ({ file: _file }) => uniqueFileKey(_file) === uniqueFileKey(file),
              ),
            ]
          : uploadableFiles,
      ) as FileData[];

      const concurrentPromises: Promise<void>[][] = [];

      while (filesToUpload.length) {
        const batch: Promise<void>[] = [];

        for (let i = 0; i < concurrentUploads && filesToUpload.length > 0; i++) {
          const fileData = filesToUpload.shift();

          if (!fileData) continue;

          const { file } = fileData;

          let payload: File | FormData = file;

          const key = uniqueFileKey(file);

          if (encType?.endsWith("form-data")) {
            const formData = new FormData();
            formData.append("file", file);
            payload = formData;
          }

          _uploadLink = useUniqueLink
            ? typeof uploadLink === "string"
              ? uploadLink
              : await uploadLink(file)
            : _uploadLink;

          batch.push(
            (api || http)
              ?.[uploadMethod]?.(_uploadLink, payload, {
                headers: { "Content-Type": encType || file.type },
                onUploadProgress: (event: AxiosProgressEvent) => {
                  const { loaded, total = 0 } = event;
                  const progress = Math.round((loaded * 100) / (total || 1));

                  const record = records[key];
                  record.progress = progress;

                  setRecords((previous) => ({
                    ...previous,
                    [key]: record,
                  }));
                },
              })
              .then((response) => {
                const successMetadata: OnFileUploadSuccessMetaData = {
                  response,
                };

                if (isS3Upload) {
                  const idTokens = _uploadLink.split("?X-Amz-Algorithm")?.[0].split("/");
                  successMetadata.s3FileId = idTokens[idTokens.length - 1];
                }

                if (onFileUploadSuccess) {
                  onFileUploadSuccess(file, successMetadata);
                }
              })
              .catch((error: AxiosError) => {
                if (onFileUploadError) {
                  onFileUploadError(error, file);
                }

                const record = records[key];
                record.errors.uploadFailure = true;
                record.progress = 0;

                setRecords((previous) => ({
                  ...previous,
                  [key]: record,
                }));
              }),
          );

          clearFileErrors(file);
        }

        concurrentPromises.push(batch);
      }

      setUploading(true);

      for (const batch of concurrentPromises) await Promise.all(batch);

      setUploading(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [uploadLink, uploadableFiles],
  );

  const handleFileChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      event.preventDefault();

      if (event?.target?.files?.length) {
        if (onFileChange) onFileChange(event);

        const files = Array.from(event.target.files);

        if (maxFiles > 1) {
          const uniqueFiles: Record<string, File> = {};

          for (const file of [...selectedFiles, ...files]) {
            const key = uniqueFileKey(file);
            if (!uniqueFiles[key]) uniqueFiles[key] = file;
          }

          setSelectedFiles(Object.values(uniqueFiles));
        } else setSelectedFiles([files[0]]);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedFiles],
  );

  const handleFileDrop = useCallback(
    (event: DragEvent<HTMLElement>) => {
      event.preventDefault();

      if (canDragAndDrop) {
        if (onFileDrop) onFileDrop(event);

        if (event.dataTransfer.files.length) {
          setDragging(false);

          const files = Array.from(event.dataTransfer.files);

          if (maxFiles > 1) {
            const uniqueFiles: Record<string, File> = {};

            for (const file of [...selectedFiles, ...files]) {
              const key = uniqueFileKey(file);
              if (!uniqueFiles[key]) uniqueFiles[key] = file;
            }

            setSelectedFiles(Object.values(uniqueFiles));
          } else setSelectedFiles([files[0]]);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [canDragAndDrop, selectedFiles],
  );

  const handleDragOver = useCallback(
    (event: DragEvent<HTMLElement>) => {
      event.preventDefault();
      if (canDragAndDrop) setDragging(true);
    },
    [canDragAndDrop, setDragging],
  );

  const handleDragLeave = useCallback(
    (event: DragEvent<HTMLElement>) => {
      event.preventDefault();
      if (canDragAndDrop) setDragging(false);
    },
    [canDragAndDrop, setDragging],
  );

  const handleFileRemoval = useCallback(
    async (event: MouseEvent<HTMLButtonElement>, file: File) => {
      event.preventDefault();

      const removed = await onFileRemoval(file);

      if (removed) {
        const filtered = Object.assign([], selectedFiles).filter(
          (_file) => uniqueFileKey(_file) !== uniqueFileKey(file),
        );
        setSelectedFiles(filtered);
      }
    },
    [selectedFiles, onFileRemoval, uniqueFileKey, setSelectedFiles],
  );

  const handleUpload = useCallback(
    async (event: MouseEvent<HTMLButtonElement>): Promise<void> => {
      event.preventDefault();

      try {
        if (handleUploadRequest) await handleUploadRequest(uploadApi);
        else await uploadApi();
      } finally {
        setUploading(false);
      }
    },
    [handleUploadRequest, uploadApi, setUploading],
  );

  const handleSingleFileUploadRetry = useCallback(
    async (event: MouseEvent<HTMLButtonElement>, file: File) => {
      event.preventDefault();

      try {
        await uploadApi(file);
      } finally {
        setUploading(false);
      }
    },
    [uploadApi, setUploading],
  );

  return {
    selectedFiles,
    records,
    generalErrors,
    dragging,
    uploading,
    disableUpload,
    handleFileChange,
    handleFileDrop,
    handleDragOver,
    handleDragLeave,
    handleFileRemoval,
    handleUpload,
    handleSingleFileUploadRetry,
  };
};

export default useHeadlessUploader;
