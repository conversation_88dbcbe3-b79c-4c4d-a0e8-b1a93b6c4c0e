import { useRef, useEffect, MutableRefObject } from "react";
import { recordIsEmpty } from "../utils/widget";
import { FileDataRecord } from "../types/hook";

const useUploaderInput = (records: FileDataRecord): MutableRefObject<HTMLInputElement> => {
  const inputRef = useRef<HTMLInputElement>();

  useEffect(() => {
    if (recordIsEmpty(records) && inputRef.current) inputRef.current.value = null;
  }, [records, inputRef]);

  return inputRef;
};

export default useUploaderInput;
