import { MouseEvent, ReactNode } from "react";
import { HeadlessUploaderProps } from "./hook";
import { MatIconVariant } from "../../mat-icon/mat-icon";
import { Nullable } from "@rubiconcarbon/frontend-shared";

export type WidgetIcon = {
  name: string; // icon name from Material Icons
  variant?: MatIconVariant;
};

export type UploadConfirmationProp = {
  title: string;
  content: ReactNode | ((props: { file: Nullable<File> }) => JSX.Element);
};

export type CommonWidgetProps = {
  uploadText?: string;
  errorSubText?: string;
  externallyUpload?: boolean;
  thumbnailSize?: number;
  uploadConfirmation?: UploadConfirmationProp;
  uniqueFileKey?: (file: File) => string;
};

export type UploaderWidgetProps = {
  dragAndDropText?: string;
  helperSubText?: string;
  cancelButtonText?: string;
  uploadButtonText?: string;
  dismissUploaderOnFileExhaustion?: boolean;
  uploadIcon?: WidgetIcon;
  removeIcon?: WidgetIcon;
  handleUploadClosure?: (event: MouseEvent<HTMLButtonElement>) => void;
} & CommonWidgetProps &
  HeadlessUploaderProps;

export type InlineUploaderWidgetProps = {
  required?: boolean;
  disabled?: boolean;
} & CommonWidgetProps &
  Omit<HeadlessUploaderProps, "maxFiles" | "concurrentUploads" | "canDragAndDrop" | "onFileDrop">;
