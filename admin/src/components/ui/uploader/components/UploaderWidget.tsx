import { <PERSON>rrorOut<PERSON>, Replay } from "@mui/icons-material";
import {
  Button,
  capitalize,
  Fade,
  Grid,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography,
} from "@mui/material";
import { MouseEvent, useMemo } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import fileSizeFormatter from "@/utils/formatters/file-size-formatter";
import useHeadlessUploader from "../hooks/use-headless-uploader";
import useUploaderFeedbackData from "../hooks/use-uploader-feedback-data";
import { FileData } from "../types/hook";
import { UploaderWidgetProps } from "../types/widget";
import useUploaderInput from "../hooks/use-uploader-input";
import useNavigationInterrupter from "@/hooks/use-navigation-interceptor";
import useUploadConfirmer from "../hooks/use-upload-confirmer";
import { defaultUniqueFile<PERSON>ey, hasError } from "../utils/hook";
import FeedbackSubTextSeparator from "./FeedbackSubTextSeparator";
import { FIFTEEN_MB } from "@constants/constants";
import Image from "next/image";
import MatIcon from "../../mat-icon/mat-icon";

import classes from "../styles/uploader-widget.module.scss";

const UploaderWidget = (props: UploaderWidgetProps): JSX.Element => {
  const {
    inputId,
    api,
    encType,
    uploadText = "Click To Upload",
    dragAndDropText = "Drag And Drop",
    uploadButtonText = "Upload",
    cancelButtonText = "Cancel",
    uploadIcon,
    removeIcon,
    dismissUploaderOnFileExhaustion,
    maxFiles = 1,
    maxFileSize = FIFTEEN_MB,
    concurrentUploads,
    allowedDimension,
    allowedAspectRatio,
    allowedExtensions,
    helperSubText,
    uploadMethod,
    useUniqueLink,
    canUpload = true,
    canDragAndDrop,
    isS3Upload,
    externallyUpload,
    thumbnailSize = 50,
    hasInterrimStep,
    uploadOnFileChange,
    uploadConfirmation,
    uniqueFileKey = defaultUniqueFileKey,
    uploadLink,
    clearOnFileUploadSuccess,
    onExposeUploadHandler,
    onFileDrop,
    onFileChange,
    onFileRemoval,
    onFileUploadSuccess,
    onFileUploadError,
    onUploadingStatusChange,
    handleUploadClosure,
    handleUploadRequest,
  } = props;

  const {
    records,
    generalErrors,
    dragging,
    uploading,
    disableUpload,
    handleFileChange,
    handleFileDrop,
    handleDragOver,
    handleDragLeave,
    handleFileRemoval,
    handleUpload,
    handleSingleFileUploadRetry,
  } = useHeadlessUploader({
    api,
    encType,
    inputId,
    maxFiles,
    maxFileSize,
    concurrentUploads,
    allowedExtensions,
    uploadMethod,
    uniqueFileKey,
    useUniqueLink,
    hasInterrimStep,
    canUpload,
    canDragAndDrop,
    isS3Upload,
    uploadOnFileChange,
    uploadLink,
    clearOnFileUploadSuccess,
    onExposeUploadHandler,
    onFileDrop,
    onFileChange,
    onFileRemoval,
    onFileUploadSuccess,
    onFileUploadError,
    onUploadingStatusChange,
    handleUploadRequest,
  });

  const inputRef = useUploaderInput(records);
  const feedbackRecords = useUploaderFeedbackData(records);
  const {
    navigating: showNavigationModal,
    continueNavigation,
    cancelNavigation,
  } = useNavigationInterrupter({ shouldIntercept: () => uploading });
  const {
    show: showConfirmationModal,
    setShow: setShowConfirmationModal,
    continueUpload: continueUpload,
  } = useUploadConfirmer(handleUpload);

  const dismissUploaderSection = useMemo(
    () => dismissUploaderOnFileExhaustion && feedbackRecords.length === maxFiles,
    [dismissUploaderOnFileExhaustion, feedbackRecords.length, maxFiles],
  );

  const disableUploaderSection = useMemo(
    () => !canUpload || feedbackRecords.length === maxFiles,
    [canUpload, feedbackRecords, maxFiles],
  );

  const navigationDialogButtons: ButtonDef[] = [
    {
      label: "Interrupt anyway",
      variant: "contained",
      onClickHandler: continueNavigation,
      tooltip: "proceed with navigation",
    },
  ];

  return (
    <Grid container gap={2}>
      {/* upload zone */}
      <Maybe condition={!dismissUploaderSection}>
        <Grid
          item
          className={`${classes.Uploader} ${dragging ? "dragging" : ""} ${disableUploaderSection ? classes.Disabled : ""}`}
          container
          direction="column"
          justifyContent="center"
          alignItems="center"
          gap={1}
          {...(canDragAndDrop && {
            onDrop: handleFileDrop,
            onDragOver: handleDragOver,
            onDragLeave: handleDragLeave,
          })}
        >
          <MatIcon value={uploadIcon?.name || "upload_file"} variant={uploadIcon?.variant} />
          <Grid className={classes.Zone} item container direction="row" justifyContent="center">
            <Grid className={classes.Verbiage} item>
              <label htmlFor={inputId}>
                <Typography>{uploadText}</Typography>
              </label>
              <input
                ref={inputRef}
                type="file"
                multiple={maxFiles > 1}
                id={inputId}
                name={inputId}
                {...((allowedExtensions?.length ?? 0 > 0) ? { accept: allowedExtensions?.join(",") } : {})}
                disabled={disableUploaderSection}
                onChange={handleFileChange}
              />
            </Grid>
            <Maybe condition={!!canDragAndDrop}>
              <Grid item>
                <Typography className={classes.DragAndDropText}>&nbsp;Or {dragAndDropText}</Typography>
              </Grid>
            </Maybe>
          </Grid>
          <Grid item container direction="column" justifyContent="start" alignItems="center">
            <Typography className={classes.SubText} align="center">
              {`${allowedDimension ? `${allowedDimension?.["exclusive"] ? `${capitalize(allowedDimension?.["exclusive"])}imum size:` : ""} ${allowedDimension?.["size"]?.[0] || allowedDimension[0]} x ${allowedDimension?.["size"]?.[1] || allowedDimension[1]}px | ` : ""}`}{" "}
              {`${allowedAspectRatio ? `Aspect ratio: ${allowedAspectRatio[0]}:${allowedAspectRatio[1]} | ` : ""}`}{" "}
              {`${allowedExtensions ? `Accepted file type${allowedExtensions?.length > 1 ? "s" : ""}: ${allowedExtensions?.map((type: string) => type.split("/")[1].toLocaleUpperCase()).join(", ")}` : ""}`}{" "}
              {`${maxFileSize ? `| Max. file size ${fileSizeFormatter(maxFileSize, "MB")}` : ""}`}
            </Typography>
            <Typography className={classes.SubText} align="center">
              {helperSubText}
            </Typography>
          </Grid>
        </Grid>
      </Maybe>
      {/* feedback zone */}
      <Fade
        appear
        in={feedbackRecords.length > 0 || generalErrors.length > 0}
        easing={{ enter: "ease-in", exit: "cubic" }}
      >
        <Grid item className={classes.Feedback} container direction="column">
          <Maybe condition={generalErrors.length > 0}>
            <Maybe condition={generalErrors.includes("TOO_MANY_FILES")}>
              <Grid className={classes.GlobalErrors} item container direction="column" justifyContent="center">
                <Grid item container justifyContent="center" alignItems="center" gap={1}>
                  <Grid item>
                    <ErrorOutline />
                  </Grid>
                  <Grid item>
                    <Typography textAlign="center">
                      {"Trying to upload too many files."} {`Maximum number of files allowed is ${maxFiles}`}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Maybe>
          </Maybe>
          <Maybe condition={feedbackRecords.length > 0}>
            <Grid item>
              <List className={classes.List}>
                {feedbackRecords.map(({ file, progress, errors }: FileData) => {
                  const key = uniqueFileKey(file);
                  const isImageType = ["image/jpeg", "image/png", "image/gif"].includes(file?.type);

                  return (
                    <ListItem
                      key={key}
                      className={classes.Item}
                      secondaryAction={
                        <>
                          <Maybe condition={progress === 0 || progress === 100}>
                            <IconButton
                              onClick={(event: MouseEvent<HTMLButtonElement>) => handleFileRemoval(event, file)}
                            >
                              <MatIcon
                                value={removeIcon?.name || "clear"}
                                variant={removeIcon?.variant}
                                color="inherit"
                              />
                            </IconButton>
                          </Maybe>
                          <Maybe condition={!!errors.uploadFailure}>
                            <IconButton
                              onClick={(event: MouseEvent<HTMLButtonElement>) =>
                                handleSingleFileUploadRetry(event, file)
                              }
                            >
                              <Replay />
                            </IconButton>
                          </Maybe>
                        </>
                      }
                      divider
                    >
                      <ListItemAvatar>
                        <Maybe condition={isImageType && !!thumbnailSize}>
                          <Image
                            src={URL.createObjectURL(file)}
                            alt={file?.name}
                            width={thumbnailSize}
                            height={thumbnailSize}
                            style={{
                              borderRadius: "5px",
                              border: "0.5px solid lightgray",
                            }}
                          />
                        </Maybe>
                        <Maybe condition={!isImageType || !thumbnailSize}>
                          <MatIcon value={uploadIcon?.name || "upload_file"} variant={uploadIcon?.variant} size={25} />
                        </Maybe>
                      </ListItemAvatar>
                      <ListItemText
                        primary={<span className={classes.FileName}>{file?.name}</span>}
                        secondary={
                          <Grid component="span" container direction="column" gap={1}>
                            <Grid className={classes.Details} component="span" container direction="row">
                              <Grid component="span" item>
                                <Typography component="span" variant="body2">
                                  {fileSizeFormatter(file?.size, "MB")}
                                </Typography>
                              </Grid>
                              <Maybe condition={!!progress}>
                                <FeedbackSubTextSeparator />
                                <Grid component="span" item>
                                  <Typography
                                    component="span"
                                    variant="body2"
                                    color={progress >= 100 ? "primary" : "inherit"}
                                  >
                                    {progress >= 100 ? "Complete" : "Uploading"}
                                  </Typography>
                                </Grid>
                              </Maybe>
                              <Maybe condition={hasError(errors)}>
                                <FeedbackSubTextSeparator />
                                <Grid className={classes.Errors} component="span" item>
                                  <Typography component="span" variant="body2">
                                    <Maybe condition={!!errors.emptyFile}>
                                      <span>File has no content</span>
                                      <Maybe condition={!!errors.invalidType}>
                                        <FeedbackSubTextSeparator />
                                      </Maybe>
                                    </Maybe>
                                    <Maybe condition={!!errors.invalidType}>
                                      <span>Invalid file type</span>
                                      <Maybe condition={!!errors.fileTooBig}>
                                        <FeedbackSubTextSeparator />
                                      </Maybe>
                                    </Maybe>
                                    <Maybe condition={!!errors.fileTooBig}>
                                      <span>File is too big</span>
                                      <Maybe condition={!!errors.uploadFailure}>
                                        <FeedbackSubTextSeparator />
                                      </Maybe>
                                    </Maybe>
                                    <Maybe condition={!!errors.uploadFailure}>
                                      <span>Upload error</span>
                                    </Maybe>
                                  </Typography>
                                </Grid>
                              </Maybe>
                            </Grid>
                            <Maybe condition={!!progress}>
                              <Grid component="span" item>
                                <LinearProgress variant="determinate" value={progress} />
                              </Grid>
                            </Maybe>
                          </Grid>
                        }
                      />
                    </ListItem>
                  );
                })}
              </List>
            </Grid>
          </Maybe>
        </Grid>
      </Fade>
      {/* actions zone */}
      <Maybe condition={!externallyUpload}>
        <Grid className={classes.Actions} container direction="row" justifyContent="center" alignItems="center" gap={2}>
          <Maybe condition={!!handleUploadClosure}>
            <Button onClick={handleUploadClosure}>
              <Typography>{cancelButtonText}</Typography>
            </Button>
          </Maybe>
          <Button
            variant="contained"
            disabled={disableUpload}
            onClick={uploadConfirmation ? (): void => setShowConfirmationModal(true) : handleUpload}
          >
            <Typography>{uploadButtonText}</Typography>
          </Button>
        </Grid>
      </Maybe>
      {/* confirmation modal zone */}
      <Grid>
        {/* Interruption Modal */}
        <ConfirmationModal
          isOpen={showNavigationModal}
          onClose={cancelNavigation}
          title={"Interrupt upload?"}
          onCloseButtonLabel="Stay"
          dialogButtons={navigationDialogButtons}
        >
          <Typography variant="body1" component="p">
            The upload may not successfully complete
          </Typography>
        </ConfirmationModal>
        {/* Upload Confirmation Modal */}
        <Maybe condition={!!uploadConfirmation}>
          <ConfirmationModal
            isOpen={showConfirmationModal}
            onClose={() => setShowConfirmationModal(false)}
            title={uploadConfirmation?.title || "Confirm upload"}
            dialogButtons={[
              {
                label: "Yes, Proceed",
                variant: "contained",
                tooltip: "proceed with upload",
                onClickHandler: continueUpload,
              },
            ]}
          >
            <Typography variant="body1" component="p">
              {typeof uploadConfirmation?.content === "function"
                ? uploadConfirmation?.content({ file: null })
                : uploadConfirmation?.content}
            </Typography>
          </ConfirmationModal>
        </Maybe>
      </Grid>
    </Grid>
  );
};

export default UploaderWidget;
