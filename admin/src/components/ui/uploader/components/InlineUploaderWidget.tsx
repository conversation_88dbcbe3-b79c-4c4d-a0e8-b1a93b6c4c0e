import { use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useState } from "react";
import { InlineUploaderWidgetProps } from "../types/widget";
import useHeadlessUploader from "../hooks/use-headless-uploader";
import useUploaderFeedbackData from "../hooks/use-uploader-feedback-data";
import useUploaderInput from "../hooks/use-uploader-input";
import { defaultUniqueFileKey } from "../utils/hook";
import { Button, ButtonGroup, Grid, LinearProgress, Tooltip, Typography } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { Clear, FileUpload, Info, Replay, UploadFile } from "@mui/icons-material";
import COLORS from "../../theme/colors";
import useUploadConfirmer from "../hooks/use-upload-confirmer";
import ConfirmationModal, { ButtonDef } from "../../dialogs/confirmation-dialog";
import useNavigationInterceptor from "@hooks/use-navigation-interceptor";
import { FIFTEEN_MB } from "@constants/constants";

import classes from "../styles/inline-uploader-widget.module.scss";

const InlineUploader = (props: InlineUploaderWidgetProps): JSX.Element => {
  const [hasInterrimStep, setHasInterrimStep] = useState<boolean>(!!props.uploadConfirmation);

  const {
    api,
    encType,
    inputId,
    uploadText = "Choose file to upload",
    errorSubText = "Upload file required.",
    maxFileSize = FIFTEEN_MB,
    allowedExtensions,
    uploadMethod,
    useUniqueLink,
    canUpload = true,
    disabled,
    isS3Upload,
    uploadOnFileChange,
    required = false,
    externallyUpload,
    uploadConfirmation,
    uniqueFileKey = defaultUniqueFileKey,
    uploadLink,
    clearOnFileUploadSuccess,
    onExposeUploadHandler,
    onFileChange,
    onFileRemoval,
    onFileUploadSuccess,
    onFileUploadError,
    onUploadingStatusChange,
    handleUploadRequest,
  } = props;

  const {
    selectedFiles,
    records,
    uploading,
    disableUpload,
    handleFileChange,
    handleFileRemoval,
    handleUpload,
    handleSingleFileUploadRetry,
  } = useHeadlessUploader({
    api,
    encType,
    inputId,
    maxFileSize,
    allowedExtensions,
    uploadMethod,
    uniqueFileKey,
    useUniqueLink,
    hasInterrimStep,
    canUpload,
    uploadOnFileChange,
    isS3Upload,
    uploadLink,
    clearOnFileUploadSuccess,
    onExposeUploadHandler,
    onFileChange,
    onFileRemoval,
    onFileUploadSuccess,
    onFileUploadError,
    onUploadingStatusChange,
    handleUploadRequest,
  });

  const inputRef = useUploaderInput(records);
  const feedbackRecords = useUploaderFeedbackData(records);

  const {
    navigating: showNavigationModal,
    continueNavigation,
    cancelNavigation,
  } = useNavigationInterceptor({
    shouldIntercept: () => uploading,
  });

  const {
    show: showConfirmationModal,
    setShow: setShowConfirmationModal,
    continueUpload,
  } = useUploadConfirmer(handleUpload);

  const navigationDialogButtons: ButtonDef[] = [
    {
      label: "Interrupt anyway",
      variant: "contained",
      onClickHandler: continueNavigation,
      tooltip: "proceed with navigation",
    },
  ];

  const file = useMemo(() => selectedFiles?.[0], [selectedFiles]);
  const feedback = useMemo(() => feedbackRecords?.[0], [feedbackRecords]);
  const isUploading = useMemo(() => uploading && !!feedback?.progress, [uploading, feedback?.progress]);
  const errors = useMemo(() => feedback?.errors || {}, [feedback?.errors]);

  const emptyError = Object.keys(errors).length === 0;
  const hasErrors = emptyError ? undefined : Object.values(errors || {}).some((value) => value);
  const errorTexts = Object.entries(errors || {}).reduce((accum, [key, value]) => {
    switch (key) {
      case "emptyFile":
        if (value) accum.push("File has no content");
        break;
      case "invalidType":
        if (value) accum.push("Invalid file type");
        break;
      case "fileTooBig":
        if (value) accum.push("File is too big");
        break;
      case "uploadFailure":
        if (value) accum.push("Upload error");
        break;
      default:
        break;
    }

    return accum;
  }, []);

  useEffect(
    () => {
      if (!!file && hasErrors !== undefined && !uploading && !hasErrors && uploadOnFileChange && !!uploadConfirmation) {
        setShowConfirmationModal(true);
        setHasInterrimStep(true);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [file, hasErrors, uploadConfirmation, uploadOnFileChange],
  );

  const handleConfirmationClose = useCallback(
    (event: MouseEvent<HTMLButtonElement>) => {
      if (uploadOnFileChange) handleFileRemoval(event, file);
      setShowConfirmationModal(false);
      setHasInterrimStep(false);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [file],
  );

  return (
    <>
      <Grid container direction="column" justifyContent="center" alignItems="start" className={classes.Zone}>
        <Grid item container gap={1} alignItems="center">
          {/* Upload Input */}
          <Maybe condition={!file}>
            <Button className={classes.Verbiage} disabled={disabled} variant="outlined">
              <label htmlFor={inputId}>
                <Typography variant="body2">{uploadText}</Typography>
              </label>
              <input
                ref={inputRef}
                type="file"
                name={inputId}
                id={inputId}
                disabled={disabled}
                {...(allowedExtensions?.length > 0 ? { accept: allowedExtensions?.join(",") } : {})}
                onChange={handleFileChange}
              />
            </Button>
          </Maybe>

          {/* Upload Feedback */}
          <Maybe condition={!!file}>
            <Grid item container justifyContent="start" alignItems="center" width="fit-content">
              {/* Upload File Icon */}
              <Maybe condition={uploadOnFileChange}>
                <Grid item container width={35}>
                  <UploadFile color="primary" fontSize="large" />
                </Grid>
              </Maybe>

              {/* Upload Filename Text */}
              <Grid
                item
                container
                justifyContent="start"
                alignContent={file ? "start" : "center"}
                direction={file ? "column" : "row"}
                width="fit-content"
              >
                <Grid item>
                  <Typography className={classes.FileName}>{file?.name}</Typography>
                </Grid>

                {/* Upload File Progress */}
                <Maybe condition={isUploading}>
                  <Grid item>
                    <LinearProgress variant="determinate" value={feedback?.progress || 0} />
                  </Grid>
                </Maybe>
                <Maybe condition={feedback?.progress === 100}>
                  <Grid item>
                    <Typography className={classes.HelperText} color={COLORS.rubiconGreen}>
                      Completed
                    </Typography>
                  </Grid>
                </Maybe>

                {/* Upload File Errors */}
                <Maybe condition={hasErrors}>
                  <Grid className={classes.Errors} item container gap={1}>
                    <Grid item>
                      <Typography className={classes.HelperText}>Error{errorTexts.length > 1 ? "s" : ""}</Typography>
                    </Grid>
                    <Grid item>
                      <Tooltip className={classes.ErrorTooltip} title={errorTexts.join(", ")}>
                        <Info className={classes.ErrorInfo} />
                      </Tooltip>
                    </Grid>
                  </Grid>
                </Maybe>
              </Grid>
            </Grid>
          </Maybe>

          {/* Upload Actions */}
          <Grid item>
            <ButtonGroup variant="text">
              <Maybe condition={!isUploading && !uploadOnFileChange && feedback?.progress !== 100 && !externallyUpload}>
                <Button
                  disabled={disableUpload}
                  onClick={uploadConfirmation ? (): void => setShowConfirmationModal(true) : handleUpload}
                >
                  <FileUpload fontSize="small" />
                </Button>
              </Maybe>
              <Maybe condition={errors.uploadFailure}>
                <Button onClick={(event) => handleSingleFileUploadRetry(event, file)}>
                  <Replay fontSize="small" />
                </Button>
              </Maybe>
              <Maybe condition={!isUploading && !!file}>
                <Button onClick={(event) => handleFileRemoval(event, file)}>
                  <Clear fontSize="small" />
                </Button>
              </Maybe>
            </ButtonGroup>
          </Grid>
        </Grid>

        {/* Validation Error Text */}
        <Maybe condition={required}>
          <Grid item>
            <Typography className={classes.HelperText} color="error">
              {errorSubText}
            </Typography>
          </Grid>
        </Maybe>
      </Grid>

      {/* Interruption Modal */}
      <ConfirmationModal
        isOpen={showNavigationModal}
        onClose={cancelNavigation}
        title={"Interrupt upload?"}
        onCloseButtonLabel="Stay"
        dialogButtons={navigationDialogButtons}
      >
        <Typography variant="body1" component="p">
          The upload may not successfully complete
        </Typography>
      </ConfirmationModal>

      {/* Upload Confirmation Modal */}
      <Maybe condition={!!uploadConfirmation}>
        <ConfirmationModal
          isOpen={showConfirmationModal}
          onClose={handleConfirmationClose}
          title={uploadConfirmation?.title}
          dialogButtons={[
            {
              label: "Yes, Proceed",
              variant: "contained",
              tooltip: "proceed with upload",
              onClickHandler: continueUpload,
            },
          ]}
        >
          <Typography variant="body1" component="p">
            {typeof uploadConfirmation?.content === "function"
              ? uploadConfirmation?.content({ file })
              : uploadConfirmation?.content}
          </Typography>
        </ConfirmationModal>
      </Maybe>
    </>
  );
};

export default InlineUploader;
