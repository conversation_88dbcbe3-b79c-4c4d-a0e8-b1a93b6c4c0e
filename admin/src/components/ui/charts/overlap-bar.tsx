import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import ReactEcharts from "echarts-for-react";
import COLORS from "../theme/colors";

export interface ProgressBarChartData {
  name: string;
  value: number;
  barValue?: number;
  labelPosition?: string;
  title?: string;
  style?: {
    barColor?: string;
    title?: {
      fontSize?: string;
      padding?: number[];
      align?: "right" | "left" | "center";
    };
    value?: {
      fontSize?: string;
      padding?: number[];
      align?: "right" | "left" | "center";
    };
  };
}

export interface ProgressBarData {
  topRange: number;
  charts: ProgressBarChartData[];
}

interface OverlapBarProps {
  chartsArray: ProgressBarChartData[];
}

export default function OverlapBar(props: OverlapBarProps): JSX.Element {
  const { chartsArray } = props;
  const [chartData, setChartData] = useState<ProgressBarData>(null);

  useEffect(() => {
    let topRange = 0;
    chartsArray.forEach((e) => {
      e.barValue = e.value;
      if (e.barValue > topRange) topRange = e.barValue;
    });

    if (topRange > 0) {
      setChartData({
        topRange: topRange,
        charts: chartsArray,
      });
    }
  }, [chartsArray]);

  const buildSeries = (): any => {
    if (chartData !== null && chartData.charts !== null) {
      return chartData.charts.map((e) => {
        if (e.barValue)
          return {
            name: e.name,
            type: "bar",
            barGap: "-100%",
            barWidth: "8.72",
            itemStyle: {
              borderRadius: 7.5,
              ...(e.value === null
                ? { color: COLORS.mediumGrey }
                : e.style.barColor
                  ? { color: e.style.barColor }
                  : {}),
            },
            emphasis: {
              focus: "series",
            },
            data: [e.barValue],
            label: {
              show: true,
              position: e.labelPosition ?? {},
              distance: -100,
              formatter: function (): string {
                const customLabel = [`{categoryTitle|${e.title}}`];
                return customLabel.join("\n");
              },
              rich: {
                categoryTitle: {
                  fontSize: e?.style?.title?.fontSize ?? 16,
                  fontWeight: 400,
                  padding: e?.style?.title?.padding ?? [0, 0, 45, 0],
                  lineHeight: 10,
                  align: e?.style?.title?.align,
                },
                categoryValue: {
                  fontSize: e?.style?.value?.fontSize ?? 20,
                  fontWeight: 500,
                  padding: e?.style?.value?.padding ?? [0, 0, -30, 0],
                  lineHeight: 10,
                  align: e?.style?.value?.align,
                },
                noCategoryValue: {
                  fontSize: e?.style?.value?.fontSize ?? 20,
                  fontWeight: 500,
                  padding: e?.style?.value?.padding ?? [0, 0, -30, 0],
                  lineHeight: 10,
                  align: e?.style?.value?.align,
                },
              },
            },
          };
      });
    }
  };

  const option = {
    title: {
      top: 10,
      left: 5,
      text: "Buffer",
      textStyle: {
        fontSize: "14px",
        color: "#1E2022",
        fontWeight: 500,
      },
    },
    legend: {
      bottom: "35",
      left: "5",
      icon: "circle",
      data: ["actual", "recommended"],
    },

    grid: {
      left: "10",
      right: "10",
      bottom: "30",
      top: "0",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      axisLabel: { show: false },
      splitLine: { show: false },
      max: chartData ? chartData.topRange : {},
    },
    yAxis: {
      type: "category",
      axisLabel: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
    },
    series: buildSeries(),
  };

  return (
    <Box sx={{ borderColor: COLORS.white, borderRadius: 2.5 }}>
      {chartData && <ReactEcharts style={{ height: "110px", width: "100%" }} option={option} />}
    </Box>
  );
}
