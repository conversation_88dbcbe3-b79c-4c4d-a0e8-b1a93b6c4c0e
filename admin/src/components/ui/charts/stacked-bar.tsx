import React, { useMemo, useEffect, useState } from "react";
import { Box } from "@mui/material";
import ReactEcharts from "echarts-for-react";
import integerFormat from "@/utils/formatters/integer-format";
import COLORS from "../theme/colors";
import { Nullable } from "@rubiconcarbon/frontend-shared";

export interface ProgressBarChartData {
  name: string;
  value?: number;
  barValue?: number;
  emptyValuesPlaceholder?: boolean;
  labelPosition?: string;
  title?: string;
  style?: {
    barColor?: string;
    title?: {
      fontSize?: string;
      padding?: number[];
      align?: "right" | "left" | "center";
    };
    value?: {
      fontSize?: string;
      padding?: number[];
      align?: "right" | "left" | "center";
    };
  };
}

export interface ProgressBarData {
  topRange: number;
  charts: ProgressBarChartData[];
}

interface StackedBarProps {
  chartsArray: ProgressBarChartData[];
  minWidthPercentage?: number;
}

export default function StackedBar(props: StackedBarProps): JSX.Element {
  const { chartsArray = [], minWidthPercentage = 5 } = props;
  const [chartData, setChartData] = useState<Nullable<ProgressBarData>>(null);

  const initialTotal = useMemo<number>(
    () => chartsArray.reduce((accumulator, currentValue) => accumulator + (currentValue.value ?? 0), 0),
    [chartsArray],
  );

  const minValue = useMemo<number>(() => (minWidthPercentage * initialTotal) / 100, [initialTotal, minWidthPercentage]);

  const zeroValue = useMemo<number>(() => (0.2 * initialTotal) / 100, [initialTotal]);

  useEffect(() => {
    chartsArray.forEach((e) => {
      e.barValue = e.value;
      if (e?.value ?? 0 < minValue) {
        if (!((e.value === 0 || e.value === undefined) && e.emptyValuesPlaceholder === false)) {
          e.barValue = minValue;
        } else if ((e.value === 0 || e.value === undefined) && e.emptyValuesPlaceholder === false) {
          e.barValue = zeroValue;
        }
      }
    });

    const total = chartsArray.reduce((accumulator, currentValue) => accumulator + (currentValue?.barValue ?? 0), 0);

    setChartData({
      topRange: total,
      charts: chartsArray,
    });
  }, [minValue, chartsArray, zeroValue]);

  const buildSeries = (): any => {
    if (chartData !== null && chartData.charts !== null) {
      return chartData.charts.map((e) => {
        if (e.barValue)
          return {
            name: e.name,
            type: "bar",
            stack: "total",
            barWidth: "8.72",
            itemStyle: {
              borderRadius: 7.5,
              ...(e.value === null
                ? { color: COLORS.mediumGrey }
                : e?.style?.barColor
                  ? { color: e.style.barColor }
                  : {}),
            },
            emphasis: {
              focus: "series",
            },
            data: [e.barValue],
            label: {
              show: true,
              position: e.labelPosition ?? {},
              distance: -110,
              formatter: function (): string {
                const customLabel = [
                  `{categoryTitle|${e.title}}`,
                  `{${
                    e?.value === undefined || isNaN(e.value)
                      ? "noCategoryValue|N/A"
                      : `categoryValue|${integerFormat(e.value)}`
                  } }`,
                ];
                return customLabel.join("\n");
              },
              rich: {
                categoryTitle: {
                  fontSize: e?.style?.title?.fontSize ?? 16,
                  fontWeight: 400,
                  padding: e?.style?.title?.padding ?? [0, 0, 45, 0],
                  lineHeight: 10,
                  align: e?.style?.title?.align,
                },
                categoryValue: {
                  fontSize: e?.style?.value?.fontSize ?? 20,
                  fontWeight: 500,
                  padding: e?.style?.value?.padding ?? [0, 0, -30, 0],
                  lineHeight: 10,
                  align: e?.style?.value?.align,
                },
                noCategoryValue: {
                  fontSize: e?.style?.value?.fontSize ?? 20,
                  fontWeight: 500,
                  padding: e?.style?.value?.padding ?? [0, 0, -30, 0],
                  lineHeight: 10,
                  align: e?.style?.value?.align,
                },
              },
            },
          };
      });
    }
  };

  const option = {
    grid: {
      left: "10",
      right: "10",
      bottom: "10",
      top: "10",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      axisLabel: { show: false },
      splitLine: { show: false },
      max: chartData ? chartData.topRange : {},
    },
    yAxis: {
      type: "category",
      axisLabel: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisTick: { show: false },
    },
    series: buildSeries(),
  };

  return (
    <Box sx={{ borderColor: COLORS.white, borderRadius: 2.5, height: "130px" }}>
      {chartData && initialTotal > 0 && <ReactEcharts style={{ height: "130px", width: "100%" }} option={option} />}
    </Box>
  );
}
