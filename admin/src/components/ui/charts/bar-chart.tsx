import React, { useState, useEffect, useCallback } from "react";
import { Box } from "@mui/material";
import ReactEcharts from "echarts-for-react";

export interface BarChartData {
  title: string;
  source: (string | number)[][];
  style: {
    colors: string[];
  };
  selectedLegend?: string[];
}

interface BarChartProps {
  data: BarChartData;
}

export default function BarChart(props: BarChartProps): JSX.Element {
  const { data } = props;
  const [chartData, setChartData] = useState<BarChartData>(null);

  useEffect(() => {
    if (!!data && !!data.source) {
      setChartData(data);
    }
  }, [data]);

  const getSelectedLegend = useCallback(() => {
    const selectedLegend = {};
    if (!!chartData && !!chartData?.source && chartData?.selectedLegend?.length > 0) {
      for (let i = 1; i < chartData?.source[0].length; i++) {
        selectedLegend[chartData?.source[0][i]] =
          chartData?.selectedLegend.indexOf(chartData?.source[0][i].toString()) !== -1 ? true : false;
      }
    }
    return selectedLegend;
  }, [chartData]);

  const getBarWidth = (source: (string | number)[][]): number | null => {
    if (source?.length) {
      return source?.length < 6 ? 70 : null;
    }
    return null;
  };

  const buildSeries = (): any[] => {
    const series = [];
    if (!!chartData && !!chartData.source) {
      for (let i = 1; i < data.source[0].length; i++) {
        series.push({
          type: "bar",
          barGap: 0,
          barWidth: getBarWidth(data?.source),

          smooth: false,
          itemStyle: {
            color: data.style?.colors[i - 1],
          },
        });
      }
    }

    return series;
  };

  const option = {
    title: {
      top: 5,
      left: 10,
      text: `${data.title}`,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
    },
    legend: {
      top: 5,
      right: "40",
      icon: "circle",
      selected: getSelectedLegend(),
    },
    grid: {
      left: "60",
      right: "40",
      bottom: "10%",
    },
    toolbox: {},

    dataset: {
      source: data.source,
    },
    xAxis: {
      type: "category",
      boundaryGap: true,
    },
    yAxis: {
      type: "value",
    },
    series: buildSeries(),
  };

  return (
    <Box>
      <ReactEcharts style={{ height: "400px", width: "100%" }} option={option} />
    </Box>
  );
}
