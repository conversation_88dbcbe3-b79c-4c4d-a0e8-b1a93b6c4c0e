import { MaybeNothing, currencyFormat, numberFormat, percentageFormat } from "@rubiconcarbon/frontend-shared";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { GenericTableColumn } from "../types/generic-table-column";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { MISSING_DATA } from "@constants/constants";
import Decimal from "decimal.js";

export const formattedColumnData = <M>(
  baseValue: any,
  row: GenericTableRowModel<M>,
  column: GenericTableColumn<M>,
): any => {
  const { type, valueOptions, missingDataValue = MISSING_DATA, deriveDataValue, transformDataValue } = column;

  const selectedOptions = typeof valueOptions === "function" ? valueOptions(row) : valueOptions;

  const resolvedValue =
    deriveDataValue?.(row) ??
    transformDataValue?.(baseValue) ??
    selectedOptions?.find(({ value }) => value === baseValue)?.label ??
    baseValue;
  const missingValue = typeof missingDataValue === "function" ? missingDataValue?.(row) : missingDataValue;

  switch (type) {
    case "number":
    case "money":
    case "percentage": {
      if (typeof resolvedValue === "string" && (resolvedValue?.startsWith("$") || resolvedValue?.endsWith("%")))
        return resolvedValue;

      let nv: MaybeNothing<Decimal>;

      if (typeof resolvedValue === "number") nv = new Decimal(resolvedValue);
      else if (typeof resolvedValue === "string") {
        try {
          nv = new Decimal(resolvedValue);
        } catch {
          /** intentionally not handling errors */
        }
      } else nv = resolvedValue;

      const isNegative = nv?.isNegative();

      return type === "number"
        ? numberFormat(nv ? nv.toString() : "", { fallback: missingValue as string })
        : type === "percentage"
          ? percentageFormat(nv ? nv.toString() : "", { fallback: missingValue as string })
          : isNegative
            ? numberFormat(nv ? nv.abs().toString() : "", {
                prepend: "- $",
                decimalPlaces: 2,
                fallback: missingValue as string,
              })
            : currencyFormat(nv ? nv.toString() : "", { fallback: missingValue as string });
    }
    case "date":
      try {
        return resolvedValue ? dateFormatterEST(resolvedValue, ["date"]) : missingValue;
      } catch {
        return resolvedValue || missingValue;
      }
    case "datetime":
      try {
        return resolvedValue ? dateFormatterEST(resolvedValue) : missingValue;
      } catch {
        return resolvedValue || missingValue;
      }
    default:
      return resolvedValue || missingValue;
  }
};
