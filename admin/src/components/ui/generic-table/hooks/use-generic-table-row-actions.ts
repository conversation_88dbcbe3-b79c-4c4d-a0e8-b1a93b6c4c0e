import { useStore, useAtomValue, use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { GenericTableExternal } from "../types/generic-table-external";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { GenericTableRowAction } from "../types/generic-table-row-action";
import { uuid } from "@rubiconcarbon/shared-types";
import { useFieldArray } from "react-hook-form";
import { isNothing } from "@rubiconcarbon/frontend-shared";
import { activeRowsAtom, expandedRowsAtom, externalAtom, rowAtomsAtom } from "../state";

type UseGenericTableRowActionsReturn<M> = {
  setAmendingRow: (change?: GenericTableRowAction, row?: GenericTableRowModel<M>) => Promise<void>;
  toggleEdit: (row?: GenericTableRowModel<M>) => void;
  cancelAmendment: (row?: GenericTableRowModel<M>) => void;
  submitRow: (row: GenericTableRowModel<M>) => Promise<void>;
  keepRowAfterAmendment: (row: GenericTableRowModel<M>) => Promise<void>;
  deleteRow: (row?: GenericTableRowModel<M>) => Promise<void>;
};

const useGenericTableRowActions = <M>(): UseGenericTableRowActionsReturn<M> => {
  const store = useStore();

  const { isMultiEditable, collapseOn, appendNewRows, resetForm, useForm, onRowFormChange, onRowRemove, onRowSubmit } =
    useAtomValue<GenericTableExternal<M>>(externalAtom as any, { store }) || {};
  const setActiveRows = useSetAtom(activeRowsAtom, { store });
  const dispatch = useSetAtom(rowAtomsAtom, { store });
  const [expanded, setExpanded] = useAtom<Record<string, boolean>>(expandedRowsAtom, { store });

  const form = useForm?.();

  const { insert, append, remove } = form
    ? // eslint-disable-next-line react-hooks/rules-of-hooks
      useFieldArray({
        control: form?.control,
        name: "amends" as any,
      })
    : { insert: null, append: null, remove: null };

  const singleEditAmendment = useCallback(
    async (change: GenericTableRowAction, row?: GenericTableRowModel<M>): Promise<void> => {
      const type = ["create", "edit"].includes(change) ? "insert" : change === "remove" ? "remove" : null;

      if (type === "insert") {
        setActiveRows([row]);
        remove?.(0);
        insert?.(0, row);

        if (row) onRowFormChange?.(row);
      } else if (type === "remove") {
        setActiveRows([]);
        remove?.(0);
        resetForm?.();
      } else if (change === "submit-row") {
        const result = row ? await onRowSubmit?.(row) : undefined;
        const { action, row: submittedRow } = result && typeof result === 'object' ? result : { action: undefined, row: undefined };

        if (submittedRow) {
          switch (action) {
            case "keep":
              dispatch({ type: "insert", item: row });

              if (row && (!collapseOn?.create || !collapseOn?.edit))
                setExpanded({
                  ...expanded,
                  [row?.id]: true,
                });
              break;
            case "remove":
              if (row) await onRowRemove?.(row);
              break;
            case "replace":
            case "update":
              dispatch({ type: "update", at: submittedRow?.id, item: submittedRow });

              if (row && (!collapseOn?.create || !collapseOn?.edit))
                setExpanded({
                  ...expanded,
                  [row?.id]: true,
                });
              break;
          }

          setActiveRows([]);
          remove?.(0);
          resetForm?.();
        }
      } else if (change === "keep-after-submit") {
        if (row?.creating)
          dispatch({ type: "insert", at: appendNewRows ? undefined : 0, item: { ...row, creating: false } });

        if (
          (!isNothing(collapseOn?.create) && !collapseOn?.create && row?.creating) ||
          (!isNothing(collapseOn?.edit) && !collapseOn?.edit && row?.editing)
        )
          setExpanded({
            ...expanded,
            [row?.id]: true,
          });

        setActiveRows([]);
        remove?.(0);
        resetForm?.();
      } else if (change === "delete" && !!row) {
        const clearActiveRow = await onRowRemove?.(row);

        if (clearActiveRow) {
          setActiveRows([]);

          remove?.(0);
          resetForm?.();

          onRowFormChange?.(row);
        }
      }
    },
    [
      appendNewRows,
      collapseOn?.create,
      collapseOn?.edit,
      dispatch,
      expanded,
      insert,
      onRowFormChange,
      onRowRemove,
      onRowSubmit,
      remove,
      resetForm,
      setActiveRows,
      setExpanded,
    ],
  );

  const multiEditAmendment = useCallback(
    async (change: GenericTableRowAction, row?: GenericTableRowModel<M>): Promise<void> => {
      const type = ["create", "edit"].includes(change) ? "insert" : change === "remove" ? "remove" : null;
      const formRows = form?.getValues()?.amends as GenericTableRowModel<M>[];
      const activeRows = formRows;

      if (type === "insert") {
        setActiveRows([...activeRows, row]);

        append?.(row);

        if (row) onRowFormChange?.(row);
      } else if (type === "remove") {
        if (row) {
          setActiveRows(activeRows?.filter(({ id }) => id !== row?.id));

          const index = activeRows?.findIndex(({ id }) => id === row?.id);
          remove?.(index);

          await onRowRemove?.(row);
        } else {
          setActiveRows([]);

          form?.reset({
            amends: [],
          });
        }
      } else if (change === "submit-row") {
        const result = row ? await onRowSubmit?.(row) : undefined;
        const { action, row: submittedRow } = result && typeof result === 'object' ? result : { action: undefined, row: undefined };

        if (submittedRow) {
          const index = activeRows?.findIndex(({ id }) => id === row?.id);

          switch (action) {
            case "keep":
              if (![-1, activeRows?.length - 1].includes(index))
                dispatch({ type: "insert", at: index, item: submittedRow });
              else dispatch({ type: "insert", item: submittedRow });

              if (!collapseOn?.create || !collapseOn?.edit)
                setExpanded({
                  ...expanded,
                  [submittedRow?.id]: true,
                });
              break;
            case "remove":
              await onRowRemove?.(submittedRow);
              break;
            case "replace":
            case "update":
              dispatch({ type: "update", at: submittedRow?.id, item: submittedRow });

              if (!collapseOn?.create || !collapseOn?.edit)
                setExpanded({
                  ...expanded,
                  [submittedRow?.id]: true,
                });
              break;
          }

          setActiveRows(activeRows?.filter(({ id }) => id !== row?.id));
          remove?.(index);
        }
      } else if (change === "keep-after-submit") {
        const index = activeRows?.findIndex(({ id }) => id === row?.id);

        if (![-1, activeRows?.length - 1].includes(index))
          dispatch({ type: "insert", at: index, item: { ...row, creating: false } });
        else dispatch({ type: "insert", item: { ...row, creating: false } });

        if (
          (!isNothing(collapseOn?.create) && !collapseOn?.create && row?.creating) ||
          (!isNothing(collapseOn?.edit) && !collapseOn?.edit && row?.editing)
        )
          setExpanded({
            ...expanded,
            [row?.id]: true,
          });

        setActiveRows(activeRows?.filter(({ id }) => id !== row?.id));
        remove?.(index);
      } else if (change === "delete" && !!row) {
        const clearActiveRow = await onRowRemove?.(row);

        if (clearActiveRow) {
          setActiveRows(activeRows?.filter(({ id }) => id !== row?.id));

          const index = activeRows?.findIndex(({ id }) => id === row?.id);
          remove?.(index);

          onRowFormChange?.(row);
        }
      }
    },
    [
      append,
      collapseOn?.create,
      collapseOn?.edit,
      dispatch,
      expanded,
      form,
      onRowFormChange,
      onRowRemove,
      onRowSubmit,
      remove,
      setActiveRows,
      setExpanded,
    ],
  );

  const setAmendingRow = useCallback(
    async (change: GenericTableRowAction, row?: GenericTableRowModel<M>): Promise<void> => {
      if (row) {
        const updated = {
          ...row,
          creating:
            change === "create" ||
            (row?.creating && change === "submit-row") ||
            (row?.creating && change === "keep-after-submit"),
          editing:
            change === "edit" ||
            (row?.editing && change === "submit-row") ||
            (row?.editing && change === "keep-after-submit"),
          deleting:
            change === "delete" ||
            (row?.deleting && change === "submit-row") ||
            (row?.deleting && change === "keep-after-submit"),
        };

        if (isMultiEditable) await multiEditAmendment(change, updated);
        else await singleEditAmendment(change, updated);
      } else {
        if (change === "create") {
          const row = {
            id: uuid(),
            creating: true,
          } as GenericTableRowModel<M>;

          if (isMultiEditable) await multiEditAmendment(change, row);
          else await singleEditAmendment(change, row);
        } else {
          if (isMultiEditable) await multiEditAmendment(change);
          else await singleEditAmendment(change);
        }
      }
    },
    [isMultiEditable, multiEditAmendment, singleEditAmendment],
  );

  const toggleEdit = useCallback(
    (row: GenericTableRowModel<M>): void => {
      if (!row?.editing) setAmendingRow("edit", row);
      else setAmendingRow("remove", row);
    },
    [setAmendingRow],
  );

  const cancelAmendment = useCallback(
    (row?: GenericTableRowModel<M>): void => {
      if (row) {
        if (row?.creating || row?.editing) setAmendingRow("remove", row);
      } else setAmendingRow("remove");
    },
    [setAmendingRow],
  );

  const submitRow = useCallback(
    async (row: GenericTableRowModel<M>): Promise<void> => {
      if (!row?.creating || !row?.editing) await setAmendingRow("submit-row", row);
    },
    [setAmendingRow],
  );

  const keepRowAfterAmendment = useCallback(
    async (row: GenericTableRowModel<M>): Promise<void> => {
      await setAmendingRow("keep-after-submit", row);
    },
    [setAmendingRow],
  );

  const deleteRow = useCallback(
    async (row: GenericTableRowModel<M>): Promise<void> => {
      if (!row?.deleting) await setAmendingRow("delete", row);
    },
    [setAmendingRow],
  );

  return {
    setAmendingRow,
    toggleEdit,
    cancelAmendment,
    submitRow,
    keepRowAfterAmendment,
    deleteRow,
  };
};

export default useGenericTableRowActions;
