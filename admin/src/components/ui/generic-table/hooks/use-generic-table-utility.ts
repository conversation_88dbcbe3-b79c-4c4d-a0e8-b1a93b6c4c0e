import { deepClone, Nullable } from "@rubiconcarbon/frontend-shared";
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useRef } from "react";
import { Resolver, Mode, UseFormReturn, DeepPartial, ExtractObjects } from "react-hook-form";
import usePerformantState from "@/hooks/use-perfomant-state";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { useGetSetState } from "react-use";
import { uuid } from "@rubiconcarbon/shared-types";
import { deepEqual } from "fast-equals";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useEnhancedForm } from "@/hooks/use-enhanced-form";

type ParameterlessVoidFunction = () => void;
type RowParameterVoidFunction<M> = (row?: GenericTableRowModel<M>) => void;
type RowParameterPromiseVoidFunction<M> = (row: GenericTableRowModel<M>) => Promise<void>;

export type ValuesEntry<M> =
  ExtractObjects<GenericTableRowModel<M>> extends never
    ? GenericTableRowModel<M>
    : DeepPartial<GenericTableRowModel<M>>;

export type Values<M> = {
  amends?: ValuesEntry<M>[];
};

type IndexedValue<M> = ExtractObjects<ValuesEntry<M>> extends never ? ValuesEntry<M> : DeepPartial<ValuesEntry<M>>;

type UseGenericTableUtilityProps<M> = {
  form?: {
    shouldFocusError?: boolean;
    resolver?: Resolver<Values<M>, any>;
    mode: Mode;
    defaultValues: Values<M>;
    context?: any;
  };
};

type UseGenericTableUtilityReturn<M> = {
  form: Nullable<ReturnType<typeof useEnhancedForm<Values<M>>>>;
  untouchedFormValue: Readonly<DeepPartial<Values<M>>>;
  currentFormValue: DeepPartial<Values<M>>;
  firstValue: IndexedValue<M>;
  lastValue: IndexedValue<M>;
  indexValue: (index: number) => IndexedValue<M>;
  table: {
    clientCanExport: boolean;
    setClientCanExport: Dispatch<SetStateAction<boolean>>;
    resetForm: ParameterlessVoidFunction;
    useForm: () => UseFormReturn<Values<M>>;
    bindAddRow: (addRow: ParameterlessVoidFunction) => void;
    bindCancelRowAmendment: (cancelRowAmendment: RowParameterVoidFunction<M>) => void;
    bindKeepRowAfterAmendment: (keepRowAfterAmendment: RowParameterPromiseVoidFunction<M>) => void;
    bindClientExport: (clientSideExport: ParameterlessVoidFunction) => void;
    handleAddRow: ParameterlessVoidFunction;
    handleCancelRowAmendment: RowParameterVoidFunction<M>;
    handleKeepRowAfterAmendment: RowParameterPromiseVoidFunction<M>;
    handleClientExport: ParameterlessVoidFunction;
  };
};

const useGenericTableUtility = <M>({ form }: UseGenericTableUtilityProps<M>): UseGenericTableUtilityReturn<M> => {
  const boundRowCreator = useRef<Nullable<ParameterlessVoidFunction>>(null);
  const boundRowAmendmentCanceler = useRef<Nullable<RowParameterVoidFunction<M>>>(null);
  const boundKeepRowAfterAmendment = useRef<Nullable<RowParameterPromiseVoidFunction<M>>>(null);
  const boundExporter = useRef<Nullable<ParameterlessVoidFunction>>(null);
  const { shouldFocusError = true, resolver, mode = "all", defaultValues, context } = form || {};

  const [untouchedValue, setUntouchedValue] = useGetSetState<DeepPartial<Values<M>>>(defaultValues);
  const [currentValue, setCurrentValue] = useGetSetState<DeepPartial<Values<M>>>(defaultValues);

  const [clientCanExport, setClientCanExport] = usePerformantState<boolean>(false);
  const [amendIds, setAmendIds] = usePerformantState<uuid[]>([]);

  const hasForm = !!mode;

  const useform = useEnhancedForm<Values<M>>({
    shouldFocusError,
    resolver,
    mode,
    defaultValues,
    context,
  });

  const amends = useform?.watch();

  const untouchedFormValue = useMemo(() => untouchedValue?.(), [untouchedValue]);
  const currentFormValue = useMemo(() => currentValue?.(), [currentValue]);

  const firstValue = useMemo(() => currentFormValue?.amends?.at?.(0), [currentFormValue]);

  const lastValue = useMemo(
    () => currentFormValue?.amends?.at?.(currentFormValue?.amends.length || 0),
    [currentFormValue],
  );

  usePerformantEffect(() => {
    setCurrentValue(amends);
  }, [amends]);

  useEffect(() => {
    const subscription = useform?.watch((value) => {
      const _amendIds = value?.amends?.flatMap((amend) => amend?.id as uuid);

      if (!deepEqual(amendIds, _amendIds)) {
        const untouchedAmends = untouchedFormValue?.amends || [];

        const updatedValue = deepClone({
          amends: value?.amends?.map((amend, index) =>
            !amendIds[index] ? amend : untouchedAmends?.find((amend) => amend?.id === amendIds[index]),
          ),
        });

        setUntouchedValue(updatedValue);
        setAmendIds(_amendIds!);
      }
    });

    return (): void => subscription?.unsubscribe();
  }, [amendIds, setAmendIds, setUntouchedValue, untouchedFormValue?.amends, useform]);

  const getForm = (): UseFormReturn<Values<M>> => useform;

  const resetForm = (): void => {
    useform?.reset(defaultValues as any);
  };

  const indexValue = useCallback(
    (index: number): IndexedValue<M> => currentFormValue?.amends?.at?.(index) as unknown as any,
    [currentFormValue],
  );

  const bindAddRow = (addRow: ParameterlessVoidFunction): void => {
    boundRowCreator.current = addRow;
  };

  const bindCancelRowAmendment = (cancelRowAmendment: RowParameterVoidFunction<M>): void => {
    boundRowAmendmentCanceler.current = cancelRowAmendment;
  };

  const bindKeepRowAfterAmendment = (keepRowAfterAmendment: RowParameterPromiseVoidFunction<M>): void => {
    boundKeepRowAfterAmendment.current = keepRowAfterAmendment;
  };

  const bindClientExport = (clientSideExport: ParameterlessVoidFunction): void => {
    boundExporter.current = clientSideExport;
  };

  const handleAddRow = (): void => {
    if (boundRowCreator.current) boundRowCreator.current();
  };

  const handleCancelRowAmendment = (row?: GenericTableRowModel<M>): void => {
    if (boundRowAmendmentCanceler.current) boundRowAmendmentCanceler.current(row);
  };

  const handleKeepRowAfterAmendment = async (row: GenericTableRowModel<M>): Promise<void> => {
    if (boundKeepRowAfterAmendment.current) await boundKeepRowAfterAmendment.current(row);
  };

  const handleClientExport = (): void => {
    if (boundExporter.current) boundExporter.current();
  };

  return {
    form: hasForm ? useform : null,
    untouchedFormValue,
    currentFormValue,
    firstValue: firstValue as unknown as any,
    lastValue: lastValue as unknown as any,
    indexValue,
    table: {
      clientCanExport,
      setClientCanExport,
      resetForm,
      useForm: getForm,
      bindAddRow,
      bindCancelRowAmendment,
      bindKeepRowAfterAmendment,
      bindClientExport,
      handleAddRow,
      handleCancelRowAmendment,
      handleKeepRowAfterAmendment,
      handleClientExport,
    },
  };
};

export default useGenericTableUtility;
