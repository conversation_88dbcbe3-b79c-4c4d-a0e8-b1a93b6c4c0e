import { CSSProperties, ReactNode } from "react";
import { GenericTableColumnType } from "./generic-table-column-type";
import { GenericTableColumnValidator } from "./generic-table-column-validator";
import { GenericTableColumnValueOption } from "./generic-table-column-value-options";
import { GenericTableSimpleType } from "./generic-table-simple-type";
import { Path } from "react-hook-form";
import { GenericTableRowModel } from "./generic-table-row-model";
import { GenericTableFormHelperTextProps } from "./generic-table-form-helper-text";
import {
  AllKeys,
  GenericRecord,
  NonEmptyArray,
  Nullable,
  UseTriggerRequestProps,
} from "@rubiconcarbon/frontend-shared";
import { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";

import { GenericTableFieldSizeEnum } from "../constants/generic-table-field-size.enum";

type LabelPosition = "before" | "after";

type OptionPosition = "before" | "after" | number;

/**
 * > **GenericTableFixedAutoCompleteOptions** is used to render auto complete options based on
 * values present in the default possible options based on current filtering.
 *
 * > It has additional conditional properties that can be used to dynamically
 * choose `when/if` and `at which position` to render.
 */
export type GenericTableFixedAutoCompleteOptions = {
  options: GenericTableColumnValueOption[];
  renderOptionOn?:
    | boolean
    | ((option: GenericTableColumnValueOption, defaultValueOptions: GenericTableColumnValueOption[]) => boolean);
  renderOptionAt?:
    | OptionPosition
    | ((option: GenericTableColumnValueOption, defaultValueOptions: GenericTableColumnValueOption[]) => OptionPosition);
};

type GenericTableCSSProperties = Omit<CSSProperties, "width" | "minWidth" | "maxWidth">;

type GenericTableAsyncAutocompleteOptions<
  PreTransformed extends GenericRecord<string, any> = GenericRecord<string, any>,
  Tranformed extends GenericRecord<string, any> = GenericRecord<string, any>,
  Value = any,
  RequestBody extends GenericRecord<string, any> = GenericRecord<string, any>,
  PathParams extends GenericRecord<string, any> = GenericRecord<string, any>,
  QueryParams extends GenericRecord<string, any> = GenericRecord<string, any>,
> = {
  keys: NonEmptyArray<AllKeys<Tranformed>>;
  request: UseTriggerRequestProps<PreTransformed, RequestBody, PathParams, QueryParams>;
  debounceMs?: number;
  preTransform?: (data: PreTransformed) => Tranformed[];
  label: (entry: Partial<Tranformed>) => string;
  displayLabel?: (entry: Partial<Tranformed>) => ReactNode;
  value: (entry: Partial<Tranformed>) => Value;
  postTransform?: (data: UseAutoCompleteOptionsReturnEntry[]) => UseAutoCompleteOptionsReturnEntry[];
};

export type GenericTableColumn<M> = {
  field: Nullable<Path<M>>;
  label?: string;

  // this is for when column is being rendered in a non-row format.
  labelPosition?: LabelPosition;

  // this takes precedence over `label` for display purposes only. it is used for rendering react elements.
  displayLabel?: ReactNode;

  // this takes precedence over `label` and `displayLabel` for display purposes only in a non-row format.
  collapsedLabel?: string | ((row: GenericTableRowModel<M>) => string);

  minWidth?:
    | number
    | GenericTableFieldSizeEnum
    | ((value?: any, row?: GenericTableRowModel<M>) => number | GenericTableFieldSizeEnum);
  width?:
    | number
    | GenericTableFieldSizeEnum
    | ((value?: any, row?: GenericTableRowModel<M>) => number | GenericTableFieldSizeEnum);
  maxWidth?:
    | number
    | GenericTableFieldSizeEnum
    | ((value?: any, row?: GenericTableRowModel<M>) => number | GenericTableFieldSizeEnum);
  fixedWidth?: boolean;
  type?: GenericTableColumnType;
  hide?: boolean | ((rows: GenericTableRowModel<M>[]) => boolean);
  placeholder?: string;
  autofocus?: boolean;
  tabIndex?: number;
  exportable?: boolean;
  sortable?: boolean;
  creatable?: boolean;
  editable?: boolean;
  filterable?: boolean;
  disable?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  collapse?: boolean | ((rows: GenericTableRowModel<M>[]) => boolean);
  useRenderedAsEdit?: boolean;
  validator?: GenericTableColumnValidator<M>;
  valueOptions?: GenericTableColumnValueOption[] | ((row: GenericTableRowModel<M>) => GenericTableColumnValueOption[]);
  fixedAutoCompleteOptions?:
    | GenericTableFixedAutoCompleteOptions
    | ((row: GenericTableRowModel<M>) => GenericTableFixedAutoCompleteOptions);
  headerTooltipContent?: ReactNode;
  dataTooltipContent?: ReactNode | ((row: GenericTableRowModel<M>) => ReactNode);
  headerCellClass?: string | ((column: GenericTableColumn<M>) => string);
  dataCellClass?: string | ((value?: any, row?: GenericTableRowModel<M>) => string);
  headerCellStyle?: GenericTableCSSProperties | ((column: GenericTableColumn<M>) => GenericTableCSSProperties);
  dataCellStyle?:
    | GenericTableCSSProperties
    | ((value?: any, row?: GenericTableRowModel<M>) => GenericTableCSSProperties);
  tooltip?: ReactNode | ((row: GenericTableRowModel<M>) => ReactNode);
  missingDataValue?: GenericTableSimpleType | ((row: GenericTableRowModel<M>) => GenericTableSimpleType);
  formHelperText?: GenericTableSimpleType | ((prop: GenericTableFormHelperTextProps<M>) => JSX.Element);
  autoCompleteLoading?:
    | boolean
    | ((options: GenericTableColumnValueOption[], row?: GenericTableRowModel<M>) => boolean);
  loadingAutoCompleteText?: ReactNode | ((row: GenericTableRowModel<M>) => ReactNode);
  noAutocompleteOption?:
    | GenericTableColumnValueOption
    | ((row: GenericTableRowModel<M>) => GenericTableColumnValueOption);
  asyncAutocompleteOptions?: GenericTableAsyncAutocompleteOptions;
  transformDataValue?: (value: any) => GenericTableSimpleType;
  deriveDataValue?: (row: GenericTableRowModel<M>) => GenericTableSimpleType;
  renderHeaderCell?: (column: GenericTableColumn<M>) => JSX.Element;
  renderDataCell?: (row: GenericTableRowModel<M>) => JSX.Element;
};
