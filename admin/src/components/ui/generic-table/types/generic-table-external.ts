import { GenericTableColumn } from "./generic-table-column";
import { GenericTableExport } from "./generic-table-export";
import { GenericTableGlobalSearch } from "./generic-table-global-search";
import { GenericTablePagination } from "./generic-table-pagination";
import { GenericTableRowModel } from "./generic-table-row-model";
import { GenericTableServerActions } from "./generic-table-server-actions";
import { GenericTableSort } from "./generic-table-sort";
import { FormEventHandler } from "react";
import { GenericTableRowToolBarOptions } from "./generic-table-row-toolbar-options";
import { Values } from "../hooks/use-generic-table-utility";
import { GenericTableOnSubmitReturn } from "./generic-table-on-submit-return";
import { useEnhancedForm } from "@hooks/use-enhanced-form";

export type GenericTableExternal<M, C = Record<string, any>> = {
  columns?: GenericTableColumn<M>[];
  rows?: GenericTableRowModel<M>[];
  serverActions?: GenericTableServerActions;
  sort?: GenericTableSort<M>;
  expandOn?: {
    create?: boolean;
    edit?: boolean;
  };
  collapseOn?: {
    create?: boolean;
    edit?: boolean;
  };
  dismissFormOnSubmit?: boolean;
  isMultiEditable?: boolean;
  isExpandable?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  appendNewRows?: boolean;
  export?: Partial<Omit<GenericTableExport<M>, "handleClientExport">>;
  pagination?: Partial<GenericTablePagination>;
  globalSearch?: GenericTableGlobalSearch<M>;
  rowToolbarOptions?: GenericTableRowToolBarOptions<M>;
  extensions?: C | ((row?: M) => C);
  resetForm?: () => void;
  useForm?: () => ReturnType<typeof useEnhancedForm<Values<M>>>;
  bindAddRow?: (addRow: () => void) => void;
  bindCancelRowAmendment?: (cancelRowAmendment: (row?: GenericTableRowModel<M>) => void) => void;
  bindKeepRowAfterAmendment?: (keepRowAfterAmendment: (row: GenericTableRowModel<M>) => Promise<void>) => void;
  onRowFormChange?: (row: M) => void;
  onFormSubmit?: FormEventHandler<HTMLFormElement>;
  onRowRemove?: (row: GenericTableRowModel<M>) => Promise<boolean>;
  onRowSubmit?: (row: GenericTableRowModel<M>) => Promise<GenericTableOnSubmitReturn<M> | void>;
};
