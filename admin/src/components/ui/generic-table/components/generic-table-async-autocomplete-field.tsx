import { InfoRounded } from "@mui/icons-material";
import { Autocomplete, FilterOptionsState, Stack, TextField, Tooltip } from "@mui/material";
import { Maybe, NO_OP, pickFromRecord, px, uniqueByKeys } from "@rubiconcarbon/frontend-shared";
import { GenericTableColumnValueOption } from "../types/generic-table-column-value-options";
import { filterAutoCompleteOptions, HelperText } from "./generic-table-editable-field";
import { KeyboardEvent, ReactNode, SyntheticEvent, useMemo, useState } from "react";
import { GenericTableColumn, GenericTableFixedAutoCompleteOptions } from "../types/generic-table-column";
import { FieldErrors, Path, PathValue, RefCallBack } from "react-hook-form";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { UseMeasureRef } from "react-use/lib/useMeasure";
import useAutoCompleteOptionsAsync from "@hooks/use-auto-complete-options-async";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Values } from "../hooks/use-generic-table-utility";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useToggle } from "react-use";

import classes from "../styles/generic-table-editable-column.module.scss";

type GenericTableAsyncAutoCompleteFieldProps<M> = {
  controllerRef: RefCallBack;
  field: Path<M>;
  disabled: boolean;
  value: PathValue<Values<M>, Path<Values<M>>>;
  row: GenericTableRowModel<M>;
  formRowIndex: number;
  errors: FieldErrors<Values<M>>;
  columns: GenericTableColumn<M>[];
  helperTextTopPadding: number;
  loading: boolean;
  loadingText: ReactNode;
  noOptionsText: ReactNode;
  otherProps: Record<string, any>;
  fixedAutoCompleteOptions: GenericTableFixedAutoCompleteOptions;
  helperTextRef: UseMeasureRef<Element>;
  onChange: (...event: any[]) => void;
};

const GenericTableAsyncAutoCompleteField = <M,>({
  controllerRef,
  field,
  disabled,
  value,
  row,
  formRowIndex,
  errors,
  columns,
  helperTextTopPadding,
  loading,
  loadingText,
  noOptionsText,
  otherProps,
  fixedAutoCompleteOptions,
  helperTextRef,
  onChange,
}: GenericTableAsyncAutoCompleteFieldProps<M>): JSX.Element => {
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [q, setQ] = useState<string>("");
  const [open, setOpen] = useToggle(false);

  const {
    label: columnLabel,
    autofocus = false,
    placeholder = "",
    tabIndex = 1,
    valueOptions = [],
    dataTooltipContent,
    tooltip,
    formHelperText,
    asyncAutocompleteOptions,
  } = useMemo(() => columns?.find(({ field: f }) => f === field), [columns, field]) || {};

  const error = useMemo(() => {
    if (formRowIndex !== -1) {
      const path = `${field}.message`;
      return pickFromRecord(errors?.amends?.at(formRowIndex) as any, [path], true)?.[path as string];
    }
    return null;
  }, [errors?.amends, field, formRowIndex]);
  const hasError = useMemo(() => !!error, [error]);

  const selectOptions = useMemo(
    () => (typeof valueOptions === "function" ? valueOptions(row) : valueOptions),
    [row, valueOptions],
  );

  if (!asyncAutocompleteOptions)
    throw new Error(
      `GenericTable: Field "${field}" is of type "async-autocomplete" but "asyncAutocompleteOptions" is not defined on the "GenericTableColumn" definition.`,
    );

  const {
    keys,
    preTransform,
    request,
    label,
    displayLabel,
    value: valueFunction,
    postTransform,
  } = asyncAutocompleteOptions;

  const options = useAutoCompleteOptionsAsync({
    q,
    keys,
    preTransform,
    request: {
      ...request,
      swrOptions: {
        ...(request?.swrOptions || {}),
        onError: (error: any): void => {
          request?.swrOptions?.onError?.(error, null, null);

          setQ("");
          enqueueError(`Unable to search for ${columnLabel}s with search term ${q}.`);
          logger.error(`Unable to search for ${columnLabel}s with search term ${q}. Error: ${error?.message}`, {});
        },
      },
    },
    label,
    displayLabel,
    value: valueFunction,
    postTransform,
  });

  const allOptions = useMemo(
    () => uniqueByKeys([...(options || []), ...selectOptions], ["label"]),
    [options, selectOptions],
  );

  const definitionTT = useMemo(
    () => (typeof dataTooltipContent === "function" ? dataTooltipContent(row) : dataTooltipContent),
    [dataTooltipContent, row],
  );

  const tt = useMemo(() => (typeof tooltip === "function" ? tooltip(row) : tooltip), [tooltip, row]);

  usePerformantEffect(() => {
    if (!q) setOpen(false);
    else
      setTimeout(() => {
        setOpen(true);
      }, 500);
  }, [noOptionsText, q]);

  const handleSearch = (event: SyntheticEvent, value: string, reason: "input" | "clear"): void => {
    event?.preventDefault();

    if (reason === "input") {
      setQ(value);
    }
    if (reason === "clear") {
      setQ("");
    }

    setOpen(false);
  };

  const handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === "Escape") setOpen(false);
  };

  const handleClose = (): void => setOpen(false);

  return (
    <Tooltip title={tt}>
      <Autocomplete
        sx={{ width: "100%", paddingTop: `${helperTextTopPadding}px` }}
        open={open}
        options={allOptions}
        value={(!!q && allOptions.find(({ value: v }) => v === value)) || null}
        loading={loading}
        loadingText={loadingText}
        noOptionsText={noOptionsText}
        filterOptions={(
          options: GenericTableColumnValueOption[],
          state: FilterOptionsState<GenericTableColumnValueOption>,
        ): GenericTableColumnValueOption[] =>
          !loading ? filterAutoCompleteOptions(options, state, fixedAutoCompleteOptions) : []
        }
        renderInput={({ InputProps, ...params }) => (
          <TextField
            autoFocus={autofocus}
            placeholder={placeholder}
            tabIndex={tabIndex}
            {...params}
            InputProps={{
              ...InputProps,
              endAdornment: (
                <Stack direction="row">
                  <Maybe condition={!!definitionTT}>
                    <Tooltip title={definitionTT}>
                      <InfoRounded className={classes.Info} />
                    </Tooltip>
                  </Maybe>
                  {InputProps.endAdornment}
                </Stack>
              ),
            }}
            {...otherProps}
            inputRef={controllerRef}
            error={hasError}
            FormHelperTextProps={{
              component: "div",
            }}
            helperText={
              <HelperText
                measureRef={helperTextRef}
                field={field}
                value={value}
                row={row}
                form={{
                  formRowIndex,
                  formHelperText: formHelperText as any,
                  errors,
                }}
              />
            }
            fullWidth
            disabled={disabled}
          />
        )}
        renderOption={(props, option: GenericTableColumnValueOption) => (
          <li
            {...props}
            {...px(
              {
                className: option?.internal ? (option?.className ?? "") : null,
                style: option?.internal ? (option?.style ?? {}) : null,
                onClick: option?.internal ? (option?.onClick ?? NO_OP) : null,
                onMouseMove: option?.internal ? (option?.onMouseMove ?? NO_OP) : null,
                onTouchStart: option?.internal ? (option?.onTouchStart ?? NO_OP) : null,
              },
              [false, null, undefined],
            )}
          >
            {option?.displayLabel || option.label}
          </li>
        )}
        disabled={disabled}
        onChange={(_, selection) => onChange(selection?.value)}
        onInputChange={handleSearch}
        onKeyDown={handleKeyDown}
        onClose={handleClose}
        classes={{
          root: classes.Autocomplete,
          inputRoot: classes.InputRoot,
          input: classes.Input,
          popupIndicator: classes.AsyncPopoutIndicator,
        }}
      />
    </Tooltip>
  );
};

export default GenericTableAsyncAutoCompleteField;
