import { TablePagination, SxProps, Box } from "@mui/material";
import { GenericPaginationActions, Maybe } from "@rubiconcarbon/frontend-shared";
import { useMemo, ChangeEvent } from "react";
import { Primitive<PERSON>tom, useAtomValue, useSetAtom, useStore } from "jotai";
import { GenericTableColumn } from "../types/generic-table-column";
import { GenericTablePagination } from "../types/generic-table-pagination";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import usePerformantEffect from "@/hooks/use-performant-effect";
import TableFooterUnit from "./table-footer-unit";
import TableRowUnit from "./table-row-unit";
import TableCellUnit from "./table-cell-unit";
import { GenericTableDivider } from "./generic-table-row";
import {
  paginationAtom,
  rowColumnsAtom,
  rowsAtom,
  sortedRowsAtom,
  updatePageNumberAtom,
  updateRowsPerPageAtom,
} from "../state";

type GenericTableFooterProps = {
  footerClass?: string;
  footerRowClass?: string;
  footerCellClass?: string;
  footerStyle?: SxProps;
  footerRowStyle?: SxProps;
  footerCellStyle?: SxProps;
};

const isValid = (value: any): boolean => value !== null && value !== undefined;

const GenericTableFooter = <M,>({
  footerClass = "",
  footerRowClass = "",
  footerCellClass = "",
  footerStyle = {},
  footerRowStyle = {},
  footerCellStyle = {},
}: GenericTableFooterProps): JSX.Element => {
  const store = useStore();

  const pagination = useAtomValue<GenericTablePagination>(paginationAtom, { store });
  const rowColumns = useAtomValue<GenericTableColumn<M>[]>(rowColumnsAtom, { store });
  const rowCount = useAtomValue<GenericTableRowModel<M>[]>(rowsAtom, { store })?.length;
  const tableRowCount = useAtomValue<PrimitiveAtom<GenericTableRowModel<M>>[]>(sortedRowsAtom, { store })?.length;

  const setPagination = useSetAtom(paginationAtom, { store });
  const setPageNumber = useSetAtom(updatePageNumberAtom, { store });
  const setRowsPerPage = useSetAtom(updateRowsPerPageAtom, { store });

  const {
    limit,
    offset,
    totalCount,
    rowsPerPageOptions = [25, 50, 75, 100],
  } = useMemo(() => pagination || {}, [pagination]);

  const canPaginate = useMemo(
    () => isValid(limit) && isValid(offset) && isValid(totalCount),
    [limit, offset, totalCount],
  );

  const canMapLimit = useMemo(
    () => [...rowsPerPageOptions, totalCount].includes(limit),
    [limit, rowsPerPageOptions, totalCount],
  );

  const shouldShowPageOptions = useMemo(() => totalCount > rowsPerPageOptions[0], [rowsPerPageOptions, totalCount]);

  const mightChangeTotalCount = useMemo(() => rowCount > tableRowCount, [rowCount, tableRowCount]);

  usePerformantEffect(() => {
    setPagination({
      ...pagination,
      offset: mightChangeTotalCount && limit > tableRowCount ? 0 : offset,
      totalCount: rowCount > tableRowCount ? tableRowCount : rowCount,
    });
  }, [limit, mightChangeTotalCount, offset, rowCount, tableRowCount]);

  usePerformantEffect(() => {
    if (canPaginate && !canMapLimit && shouldShowPageOptions) setRowsPerPage(rowsPerPageOptions[0]);
  }, [canMapLimit, canPaginate, rowsPerPageOptions, shouldShowPageOptions]);

  return (
    <Maybe condition={canPaginate}>
      <TableFooterUnit className={footerClass} sx={footerStyle} zIndex={1000}>
        <GenericTableDivider />
        <TableRowUnit
          className={footerRowClass}
          sx={{
            position: "relative",
            ...footerRowStyle,
          }}
        >
          <TableCellUnit role="" width="100%" maxWidth="100%" padding={0}>
            <TablePagination
              component={Box}
              className={footerCellClass}
              sx={{
                ...footerCellStyle,
              }}
              colSpan={rowColumns.length}
              count={totalCount || 0}
              page={offset / limit}
              rowsPerPage={canMapLimit ? limit || 0 : rowsPerPageOptions?.[0]}
              rowsPerPageOptions={
                canPaginate && shouldShowPageOptions ? [...rowsPerPageOptions, { label: "All", value: totalCount }] : []
              }
              onPageChange={(_, pageNumber: number) => setPageNumber(pageNumber)}
              onRowsPerPageChange={(event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
                setRowsPerPage(parseInt(event?.target?.value))
              }
              ActionsComponent={GenericPaginationActions}
            />
          </TableCellUnit>
        </TableRowUnit>
      </TableFooterUnit>
    </Maybe>
  );
};

export default GenericTableFooter;
