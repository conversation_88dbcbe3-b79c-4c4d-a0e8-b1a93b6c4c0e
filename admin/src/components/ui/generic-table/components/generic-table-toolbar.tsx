import { Stack } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { PropsWithChildren, useEffect, useMemo, useState } from "react";
import ActionButton, { ActionButtonProps } from "../../action-button/action-button-enhanced";
import SearchAppBar from "../../search/enhanced-search";
import { useDebounce } from "react-use";
import { useAtom, useAtomValue, useStore } from "jotai";
import { GenericTableGlobalSearch } from "../types/generic-table-global-search";
import { GenericTableExternal } from "../types/generic-table-external";
import { CsvBuilder } from "filefy";
import { exportableLabelsAtom, exportableTableValuesAtom, externalAtom, globalSearchAtom } from "../state";

type GenericTableToolbarProps = {
  sticky?: boolean;
  loading?: boolean;
  actionButtons?: PropsWithChildren<ActionButtonProps>[];
};

const GenericTableToolbar = <M,>({
  loading = false,
  children,
  actionButtons = [],
}: PropsWithChildren<GenericTableToolbarProps>): JSX.Element => {
  const store = useStore();

  const [globalSearch, set] = useAtom<GenericTableGlobalSearch<M>>(globalSearchAtom, { store });
  const { export: ex } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store });
  const exportableLabels = useAtomValue<string[]>(exportableLabelsAtom, { store });
  const exportableTableValues = useAtomValue<string[][]>(exportableTableValuesAtom, { store });

  const [term, setTerm] = useState<string>(globalSearch?.term || "");

  const canSearch = useMemo(() => !!globalSearch && !!globalSearch?.searchKeys?.length, [globalSearch]);

  useDebounce(() => set({ ...globalSearch, term }), 300, [term]);

  useEffect(() => {
    if (ex?.setClientCanExport) ex?.setClientCanExport(!!exportableTableValues?.length);
    if (ex?.bindClientExport) {
      ex?.bindClientExport(() => {
        new CsvBuilder(typeof ex?.filename === "function" ? ex.filename() : ex.filename)
          .setColumns(exportableLabels)
          .addRows(exportableTableValues)
          .exportFile();
      });
    }

    return (): void => {
      if (ex?.setClientCanExport) ex?.setClientCanExport(false);
      if (ex?.bindClientExport) ex?.bindClientExport(null);
    };
  }, [ex, exportableLabels, exportableTableValues]);

  const handleSearch = (term: string): void => {
    setTerm(term);
    globalSearch?.onSearchTermChange?.(term);
  };

  return (
    <Maybe condition={canSearch || !!actionButtons.length || !!children}>
      <SearchAppBar filter={term} onChangeHandler={handleSearch} disableInput={loading} isDisabled={!canSearch}>
        <Stack
          position="sticky"
          direction="row"
          gap={"var(--action-button-gap)"}
          justifyContent="flex-end"
          alignItems="center"
        >
          {actionButtons.map(({ style, ...rest }, key) => (
            <ActionButton key={key} {...{ ...rest, style: { fontWeight: 600, textWrap: "nowrap", ...style } }} />
          ))}
          <Maybe condition={!!children}>{children}</Maybe>
        </Stack>
      </SearchAppBar>
    </Maybe>
  );
};

export default GenericTableToolbar;
