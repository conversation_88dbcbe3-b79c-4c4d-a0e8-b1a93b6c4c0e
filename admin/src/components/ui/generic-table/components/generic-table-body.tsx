import { <PERSON>, Stack, SxProps, TableSortLabel, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import GenericTableRow from "./generic-table-row";
import { Delay, Maybe, px } from "@rubiconcarbon/frontend-shared";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { ReactNode, useCallback, useEffect, useMemo, useRef } from "react";
import { uuid } from "@rubiconcarbon/shared-types";
import {
  attachExpandedColumnAtom,
  columnsAtom,
  externalAtom,
  hasRowsAtom,
  pagedRowsAtom,
  renderedRowsAtom,
  rowColumnsAtom,
  sortsAtom,
  updateSortsAtom,
} from "../state";
import { useAtom, useAtomValue, useSetAtom, useStore } from "jotai";
import useGenericTableRowActions from "../hooks/use-generic-table-row-actions";
import { GenericTableColumn } from "../types/generic-table-column";
import { useMeasure, useMount, useUnmount } from "react-use";
import TableBodyUnit from "./table-body-unit";
import TableRowUnit from "./table-row-unit";
import { InfoRounded } from "@mui/icons-material";
import { GenericTableSort, GenericTableSortType } from "../types/generic-table-sort";
import TableCellUnit from "./table-cell-unit";
import { GenericTableFieldSizeEnum } from "../constants/generic-table-field-size.enum";

import tableHeaderClasses from "../styles/generic-table-header.module.scss";

type DSW<M> = (value?: any, row?: GenericTableRowModel<M>) => number | GenericTableFieldSizeEnum;

type GenericTableBodyProps<M> = {
  loading: boolean;
  reloadingRow?: boolean;
  expandOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  collapseOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  eager?: {
    expand?: boolean | ((model: GenericTableRowModel<M>) => boolean);
    collapse?: boolean | ((model: GenericTableRowModel<M>) => boolean);
  };
  isSelectable?: boolean | ((model: GenericTableRowModel<M>) => boolean);
  isExpandable?: boolean | ((model: GenericTableRowModel<M>) => boolean);
  rowId?: (string | uuid) | ((model: GenericTableRowModel<M>) => string | uuid);
  headerRowClass?: string;
  headerRowStyle?: SxProps;
  bodyClass?: string | ((rows: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => string);
  bodyRowClass?: string | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => string);
  bodyStyle?: SxProps | ((rows: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => SxProps);
  bodyRowStyle?: SxProps | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => SxProps);
  renderLoadingContent?: ReactNode;
  renderNoDataContent?: ReactNode;
  renderExpandContent?: (model: GenericTableRowModel<M>) => JSX.Element | Promise<JSX.Element>;
};

const GenericTableBody = <M,>(props: GenericTableBodyProps<M>): JSX.Element => {
  const {
    loading,
    reloadingRow,
    expandOn,
    collapseOn,
    eager,
    // isSelectable, // todo: add this feature
    isExpandable,
    rowId,
    headerRowClass = "",
    headerRowStyle = {},
    bodyClass = "",
    bodyRowClass = "",
    bodyStyle = {},
    bodyRowStyle = {},
    renderLoadingContent,
    renderNoDataContent,
    renderExpandContent,
  } = props;

  const [main, { width: mainWidth }] = useMeasure();
  const header = useRef<HTMLDivElement>(null);
  const body = useRef<HTMLDivElement>(null);

  const store = useStore();

  const external = useAtomValue(externalAtom, { store });
  const rowColumns = useAtomValue<GenericTableColumn<M>[]>(rowColumnsAtom, { store });
  const sort = useAtomValue<GenericTableSort<M>>(sortsAtom, { store });

  const updateSort = useSetAtom(updateSortsAtom, { store });

  const { setAmendingRow, cancelAmendment, keepRowAfterAmendment } = useGenericTableRowActions<M>();

  const { bindAddRow, bindCancelRowAmendment, bindKeepRowAfterAmendment } = useMemo(() => external, [external]);

  useMount(() => {
    if (bindAddRow) {
      bindAddRow(() => {
        setAmendingRow("create");
      });
    }

    if (bindCancelRowAmendment) {
      bindCancelRowAmendment((row?: GenericTableRowModel<M>) => {
        cancelAmendment(row);
      });
    }

    if (bindKeepRowAfterAmendment) {
      bindKeepRowAfterAmendment(async (row: GenericTableRowModel<M>) => await keepRowAfterAmendment(row));
    }
  });

  useUnmount(() => {
    if (bindAddRow) bindAddRow(null);
    if (bindCancelRowAmendment) bindCancelRowAmendment(null);
    if (bindKeepRowAfterAmendment) bindKeepRowAfterAmendment(null);
  });

  const hasRows = useAtomValue(hasRowsAtom, { store });
  const pagedRows = useAtomValue<GenericTableRowModel<M>[]>(pagedRowsAtom, { store });
  const tableColumns = useAtomValue(columnsAtom, { store });
  const [attachedExpandedColumn, attachExpandedColumn] = useAtom<boolean>(attachExpandedColumnAtom, { store });
  const renderedRows = useAtomValue<GenericTableRowModel<M>[]>(renderedRowsAtom, {
    store,
  });

  const how = header.current?.offsetWidth;
  const hsw = header.current?.scrollWidth;
  const bow = body.current?.offsetWidth;
  const bsw = body.current?.scrollWidth;

  const headerWidth = useMemo(() => (how === hsw ? how : hsw), [how, hsw]);
  const bodyWidth = useMemo(() => (bow === bsw ? bow : bsw), [bow, bsw]);
  const resolvedWidth = useMemo(() => Math.max(headerWidth, bodyWidth), [bodyWidth, headerWidth]);

  const sorts = useMemo(() => sort?.sorts, [sort?.sorts]);

  const hrc = useMemo(() => headerRowClass, [headerRowClass]);
  const hrs = useMemo(() => headerRowStyle, [headerRowStyle]);
  const bc = useMemo(
    () => (typeof bodyClass === "function" ? bodyClass(pagedRows, tableColumns) : bodyClass),
    [bodyClass, tableColumns, pagedRows],
  );
  const bs = useMemo(
    () => (typeof bodyStyle === "function" ? bodyStyle(pagedRows, tableColumns) : bodyStyle),
    [bodyStyle, tableColumns, pagedRows],
  );

  const shouldAttachExpandColumn = useMemo(
    () => (typeof isExpandable === "boolean" ? isExpandable : renderedRows?.some((row) => isExpandable?.(row))),
    [isExpandable, renderedRows],
  );

  useEffect(() => {
    if (!attachedExpandedColumn && shouldAttachExpandColumn) attachExpandedColumn(true);
  }, [shouldAttachExpandColumn, attachedExpandedColumn, attachExpandedColumn]);

  const toggleSort = useCallback(
    (field: string): void => {
      const order = sorts?.[field] || "none";

      const newOrder = order === "asc" ? "desc" : order === "desc" ? "none" : "asc";

      updateSort({ field, order: newOrder });

      sort?.onColumSortChange?.(field, newOrder);
    },
    [sort, sorts, updateSort],
  );

  return (
    <Box ref={main} data-id="table-main" role="presentation" component={Stack} overflow="scroll">
      <TableRowUnit
        ref={header}
        className={hrc}
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: "white",
          zIndex: 1000,
          boxShadow:
            "0px -3px 20px 0px rgb(0 0 0 / 0%), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)",
          ...hrs,
          width: "100%",
          minWidth: mainWidth < resolvedWidth ? resolvedWidth : "100%",
        }}
      >
        {rowColumns.map((column, index) => {
          const {
            field,
            label,
            displayLabel,
            sortable = true,
            minWidth,
            width,
            maxWidth,
            fixedWidth,
            headerCellClass = "",
            headerCellStyle = {},
            headerTooltipContent = null,
            renderHeaderCell,
          } = column;
          const active = sortable && !!sorts?.[field as any] && sorts?.[field as any] !== "none";
          const direction = active ? (sorts?.[field] as Exclude<GenericTableSortType, "none">) : false;

          const hc = typeof headerCellClass === "function" ? headerCellClass(column) : headerCellClass;
          const hs = typeof headerCellStyle === "function" ? headerCellStyle(column) : headerCellStyle;
          const mnw = typeof minWidth === "function" ? (minWidth as DSW<M>)() : minWidth;
          const w = typeof width === "function" ? (width as DSW<M>)() : width;
          const mxw = typeof maxWidth === "function" ? (maxWidth as DSW<M>)() : maxWidth;

          return (
            <TableCellUnit
              role="columnheader"
              key={`${field}-${index}`}
              name={field}
              fixedWidth={fixedWidth}
              className={`${tableHeaderClasses.TableHeader} ${hc}`}
              sx={{
                ...hs,
                minWidth: mnw,
                width: w,
                maxWidth: mxw,
                ...px({ flexGrow: fixedWidth && 0, flexShrink: fixedWidth && 0 }, [undefined, null]),
              }}
            >
              <TableSortLabel
                active={active}
                hideSortIcon={!sortable}
                {...px({ direction }, [false])}
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  minWidth: "inherit",
                  width: "inherit",
                  maxWidth: "inherit",
                }}
                onClick={() => sortable && toggleSort?.(field)}
              >
                <Stack direction="row" alignItems="center" gap={1} width="90%">
                  <Maybe condition={!!renderHeaderCell}>{renderHeaderCell?.(column)}</Maybe>
                  <Maybe condition={!renderHeaderCell}>
                    <Typography component="span" className={tableHeaderClasses.TableHeaderText} width="100%">
                      {displayLabel || label}
                    </Typography>
                  </Maybe>
                  <Maybe condition={!!headerTooltipContent}>
                    <Tooltip title={headerTooltipContent as ReactNode}>
                      <InfoRounded className={tableHeaderClasses.TableHeaderInfo} />
                    </Tooltip>
                  </Maybe>
                </Stack>
              </TableSortLabel>
            </TableCellUnit>
          );
        })}
      </TableRowUnit>
      <Maybe condition={loading}>{renderLoadingContent}</Maybe>
      <Delay ms={500} fallback={<Maybe condition={!loading}>{renderLoadingContent}</Maybe>}>
        <Maybe condition={!loading && !hasRows}>{renderNoDataContent}</Maybe>
        <TableBodyUnit
          ref={body}
          className={bc}
          sx={{
            ...bs,
            width: "100%",
            minWidth: mainWidth < resolvedWidth ? resolvedWidth : "100%",
          }}
        >
          <Maybe condition={!loading && hasRows}>
            {renderedRows?.map((row, index) => (
              <GenericTableRow
                key={rowId ? (typeof rowId === "function" ? rowId(row as any) : rowId) : row?.id || index}
                row={row}
                reloadingRow={reloadingRow}
                expandOn={expandOn}
                collapseOn={collapseOn}
                eager={eager}
                isExpandable={isExpandable}
                bodyRowClass={bodyRowClass}
                bodyRowStyle={bodyRowStyle}
                renderExpandContent={renderExpandContent}
              />
            ))}
          </Maybe>
        </TableBodyUnit>
      </Delay>
    </Box>
  );
};

export default GenericTableBody;
