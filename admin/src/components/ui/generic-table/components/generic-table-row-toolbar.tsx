import { useMemo } from "react";
import { GenericTableRowToolBarOptions } from "../types/generic-table-row-toolbar-options";
import useGenericTableRowActions from "../hooks/use-generic-table-row-actions";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { useAtomValue, useStore } from "jotai";
import { Collapse, Divider, Grid, Stack } from "@mui/material";
import { LoadingButton } from "@mui/lab";
import { Maybe, px } from "@rubiconcarbon/frontend-shared";
import { GenericTableExternal } from "../types/generic-table-external";
import useGenericTableRowState from "../hooks/use-generic-table-row-state";
import { activeRowsAtom, externalAtom } from "../state";

import classes from "../styles/generic-table-row-toolbar.module.scss";

const AmendmentActions = <M,>({
  amendmentActions,
  demarcateAmendmentActions = true,
}: Pick<GenericTableRowToolBarOptions<M>, "amendmentActions" | "demarcateAmendmentActions">): JSX.Element => {
  const store = useStore();

  const activeRows = useAtomValue<GenericTableRowModel<M>[]>(activeRowsAtom, { store });
  const { dirtyFields } = useGenericTableRowState<M>(activeRows?.at(0)); // todo: make this work with multi edits
  const { cancelAmendment } = useGenericTableRowActions();

  // todo: make this work with multi edits
  const dirty = Object.values(dirtyFields?.amends?.at(0) || {}).some((value: boolean) => value);

  return (
    <Stack direction="row">
      {amendmentActions?.map(({ label, kind, ...rest }, index) => (
        <Stack key={index} direction="row">
          <Maybe condition={index > 0 && demarcateAmendmentActions}>
            <Divider className={classes.Divider} sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
          </Maybe>
          <LoadingButton
            className={classes.AmendmentActionButton}
            type={kind === "commit" ? "submit" : "button"}
            {...px(
              {
                onClick: kind === "cancel" && ((): void => cancelAmendment()),
                disabled: kind === "commit" && !dirty,
              },
              [null, undefined, false],
            )}
            {...rest}
          >
            {typeof label === "function" ? label(activeRows?.at(0)) : label}
          </LoadingButton>
        </Stack>
      ))}
    </Stack>
  );
};

const GenericTableRowToolBar = <M,>(): JSX.Element => {
  const store = useStore();

  const activeRows = useAtomValue<GenericTableRowModel<M>[]>(activeRowsAtom, { store });
  const { rowToolbarOptions } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store }) || {};

  const { className, style, amendmentActions = [], demarcateAmendmentActions = true } = rowToolbarOptions || {};

  const amending = useMemo(() => activeRows?.some(({ creating, editing }) => creating || editing), [activeRows]);
  // checks will grow as we enhance and add more options to "GenericTableRowToolBarOptions"
  const visible = useMemo(() => amending && !!amendmentActions?.length, [amending, amendmentActions?.length]);

  return (
    <Collapse in={visible} unmountOnExit>
      <Grid
        container
        direction="column"
        justifyContent="center"
        className={`${classes.Toolbar}${className ? className : ""}`}
        style={style}
      >
        <Grid item container justifyContent="end">
          <AmendmentActions amendmentActions={amendmentActions} demarcateAmendmentActions={demarcateAmendmentActions} />
        </Grid>
      </Grid>
    </Collapse>
  );
};

export default GenericTableRowToolBar;
