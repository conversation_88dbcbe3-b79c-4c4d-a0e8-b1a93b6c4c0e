import { useAtomValue, useStore } from "jotai";
import { GenericTableExternal } from "@components/ui/generic-table/types/generic-table-external";
import { MouseEvent, useMemo } from "react";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { externalAtom } from "@components/ui/generic-table/state";
import { Badge, IconButton } from "@mui/material";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { classcat, NO_OP, toNumber } from "@rubiconcarbon/frontend-shared";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import {
  GenericTableDocumentsButtonExtensions,
  GenericTableDocumentsButtonOptionalRowProps,
} from "../types/generic-table-documents-button";

import classes from "../styles/generic-table-documents-button.module.scss";

export type GenericTableDocumentsButtonProps = {
  row: GenericTableRowModel<GenericTableDocumentsButtonOptionalRowProps>;
};

const GenericTableDocumentsButton = ({ row }: GenericTableDocumentsButtonProps): JSX.Element => {
  const store = useStore();
  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    GenericTableDocumentsButtonOptionalRowProps,
    GenericTableDocumentsButtonExtensions
  >;
  const { amending, isRowActive } = useGenericTableRowState<GenericTableDocumentsButtonOptionalRowProps>(row);

  const disabled = useMemo(() => amending && !isRowActive, [amending, isRowActive]);

  const {
    amendingNestedRows = NO_OP,
    toggleDocumentsModal,
    setViewingRow,
  } = useMemo(() => (typeof extensions === "function" ? extensions(row) : extensions), [extensions, row]);

  const inActive = amendingNestedRows() || isRowActive || disabled;

  const openModal = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    event?.stopPropagation();

    if (!isRowActive) setViewingRow(row);
    toggleDocumentsModal();
  };

  return (
    <Badge classes={{ dot: classes.Dot }} badgeContent={toNumber(row?.docsCount)} variant="dot">
      <IconButton className={classes.IconButton} disabled={inActive} onClick={openModal}>
        <MatIcon
          className={classcat([
            classes.Icon,
            { [classes.DisabledIcon]: inActive },
            { [classes.EnabledIcon]: !inActive },
            { [classes.HasDocsIcon]: row?.alwaysEnabled || !!toNumber(row?.docsCount) },
          ])}
          value="upload_file"
          variant="outlined"
          size={28}
        />
      </IconButton>
    </Badge>
  );
};

export default GenericTableDocumentsButton;
