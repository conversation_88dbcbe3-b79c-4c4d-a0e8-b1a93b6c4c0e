import { Box, BoxProps, Stack } from "@mui/material";
import { isNothing, omitFromRecord } from "@rubiconcarbon/frontend-shared";
import { useAtom, useStore } from "jotai";
import { forwardRef, PropsWithChildren, RefObject, useMemo } from "react";
import { useEnsuredForwardedRef, useMeasure } from "react-use";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { observedColumnWidthAtomFamily } from "../state";

type TableCellUnitProps = {
  name?: string; // column name
  fixedWidth?: boolean;
} & BoxProps;

const TableCellUnit = forwardRef(
  (
    { children, name = "", fixedWidth = false, sx, ...rest }: PropsWithChildren<TableCellUnitProps>,
    ref: RefObject<HTMLDivElement>,
  ): JSX.Element => {
    const store = useStore();
    const [observedWidth, set] = useAtom(observedColumnWidthAtomFamily(name), { store });

    const guranteedRef = useEnsuredForwardedRef(ref);
    const [measureRef, { width }] = useMeasure();

    const combinedRef = (node: HTMLDivElement): void => {
      if (node) measureRef(node);
      guranteedRef.current = node;
    };

    const trueWidth = useMemo(() => Math.max(sx?.["width"], observedWidth), [observedWidth, sx]);

    usePerformantEffect(() => {
      set(width);
    }, [width]);

    return (
      <Box
        data-id="table-cell"
        role="cell"
        component={Stack}
        ref={combinedRef}
        padding={1}
        sx={{
          boxSizing: "content-box",
          justifyContent: "center",
          ...omitFromRecord(sx as any, ["minWidth", "width", "maxWidth"] as any),
        }}
        flexGrow={1}
        flexShrink={1}
        flexBasis={fixedWidth ? sx?.["width"] : !isNothing(trueWidth) ? trueWidth : "auto"}
        minWidth={sx?.["minWidth"] || sx?.["width"] || "none"}
        maxWidth={sx?.["maxWidth"] || "none"}
        {...rest}
      >
        {children}
      </Box>
    );
  },
);

TableCellUnit.displayName = "TableCellUnit";

export default TableCellUnit;
