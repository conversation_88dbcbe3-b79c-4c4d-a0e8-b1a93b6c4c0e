import { Maybe, px } from "@rubiconcarbon/frontend-shared";
import { Path } from "react-hook-form";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import GenericTableEditableField from "./generic-table-editable-field";
import GenericTableViewField from "./generic-table-view-field";
import { Box, FormControl, FormLabel, Grid } from "@mui/material";
import { CSSProperties, PropsWithChildren, useMemo } from "react";
import Awaited from "../../await/components/awaited";
import { Atom, useAtomValue, useStore } from "jotai";
import { GenericTableColumn } from "../types/generic-table-column";
import { baseColumnsAtom } from "../state";

type Size = number | "auto";

type LabelProps<M> = {
  field: Path<M>;
  className?: string;
  style?: CSSProperties;
};

type FieldProps<M> = {
  id: Path<M>;
  editing?: boolean;
  row: GenericTableRowModel<M>;
};

type GenericTableFieldColumnsGridProps = {
  spacing?: number;
  columns?: number;
};

type GenericTableFieldProps<M> = {
  field: Path<M>;
  editing?: boolean;
  loading?: boolean;
  inRow?: boolean;
  size?: Size;
  negativeSpacing?: number;
  layout?: "row" | "column";
  gap?: number;
  justifyContent?: "start" | "center" | "end" | "space-between" | "space-around" | "space-evenly";
  alignItems?: "baseline" | "start" | "center" | "end";
  row: GenericTableRowModel<M>;
  labelClass?: string;
  labelStyle?: CSSProperties;
};

const Label = <M,>({ field, className = "", style = {}, children }: PropsWithChildren<LabelProps<M>>): JSX.Element => (
  <FormLabel
    className={className}
    id={field}
    htmlFor={field}
    style={{
      maxWidth: 110,
      width: "100%",
      fontSize: 14,
      fontWeight: 600,
      color: "#121212",
      ...style,
    }}
  >
    {children}
  </FormLabel>
);

const Field = <M,>({ id, editing, row }: FieldProps<M>): JSX.Element => (
  <>
    <Maybe condition={editing}>
      <GenericTableEditableField field={id} row={row} />
    </Maybe>
    <Maybe condition={!editing}>
      <GenericTableViewField field={id} row={row} />
    </Maybe>
  </>
);

export const GenericTableFieldColumnsGrid = ({
  spacing = 2,
  columns = 12,
  children,
}: PropsWithChildren<GenericTableFieldColumnsGridProps>): JSX.Element => (
  <Box flexGrow={1}>
    <Grid container spacing={spacing} columns={columns}>
      {children}
    </Grid>
  </Box>
);

const GenericTableField = <M,>({
  field,
  editing = false,
  loading = false,
  inRow = true,
  size = "auto",
  negativeSpacing = 0,
  layout = "row",
  gap = 2,
  justifyContent = "start",
  alignItems = "baseline",
  row,
  labelClass = "",
  labelStyle = {},
}: GenericTableFieldProps<M>): JSX.Element => {
  const store = useStore();

  const baseColumns = useAtomValue<GenericTableColumn<M>[]>(baseColumnsAtom as Atom<GenericTableColumn<M>[]>, {
    store,
  });

  const {
    label = null,
    labelPosition = "before",
    displayLabel = null,
    collapsedLabel = null,
  } = useMemo(() => baseColumns?.find((column) => field === column.field), [baseColumns, field]) || {};

  const resolvedSimpleLabel = collapsedLabel
    ? typeof collapsedLabel === "function"
      ? collapsedLabel(row)
      : collapsedLabel
    : label;

  return (
    <>
      <Maybe condition={!!negativeSpacing}>
        <Grid item xs={negativeSpacing} />
      </Maybe>
      <Grid item {...px({ xs: !inRow ? size : null, width: inRow ? "inherit" : null }, [null])}>
        <Maybe condition={inRow}>
          <Field id={field} editing={editing} row={row} />
        </Maybe>
        <Maybe condition={!inRow}>
          <FormControl
            sx={{
              flexDirection: layout,
              gap,
              justifyContent: layout === "row" ? justifyContent : alignItems,
              alignItems: layout === "row" ? alignItems : justifyContent,
              width: "100%",
            }}
          >
            <Maybe condition={!!(displayLabel || resolvedSimpleLabel) && labelPosition === "before"}>
              <Label field={field} className={labelClass} style={labelStyle}>
                {displayLabel || resolvedSimpleLabel}:{" "}
              </Label>
            </Maybe>

            <Maybe condition={loading}>
              <Awaited
                variant="rounded"
                itemSx={{
                  height: 25,
                  minWidth: 200,
                  width: "100%",
                  maxWidth: 400,
                }}
              />
            </Maybe>
            <Maybe condition={!loading}>
              <Field id={field} editing={editing} row={row} />
            </Maybe>

            <Maybe condition={!!(displayLabel || resolvedSimpleLabel) && labelPosition === "after"}>
              <Label field={field} className={labelClass} style={labelStyle}>
                : {displayLabel || resolvedSimpleLabel}
              </Label>
            </Maybe>
          </FormControl>
        </Maybe>
      </Grid>
    </>
  );
};

export default GenericTableField;
