import {
  <PERSON>,
  Button,
  ClickAwayListener,
  Fade,
  Paper,
  Popper,
  <PERSON>ack,
  SxProps,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  <PERSON>K<PERSON><PERSON>,
  Maybe,
  isNothing,
  pickFromRecord,
  px,
  useIsInView,
  usePopperState,
} from "@rubiconcarbon/frontend-shared";
import { Path } from "react-hook-form";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { MouseEvent, useMemo, useRef } from "react";
import { formattedColumnData } from "../utilities/format-column-data";
import { GenericTableColumn } from "../types/generic-table-column";
import { ArrowDropDown, InfoRounded } from "@mui/icons-material";
import { MISSING_DATA } from "@constants/constants";
import { useStore, useAtomValue, Atom } from "jotai";
import { baseColumnsAtom } from "../state";

import classes from "../styles/generic-table-view-field.module.scss";

type DSS<M> = (value?: any, row?: GenericTableRowModel<M>) => SxProps;

type GenericTableViewFieldProps<M> = {
  field: Path<M>;
  row: GenericTableRowModel<M>;
};

const GenericTableViewField = <M,>({ field, row }: GenericTableViewFieldProps<M>): JSX.Element => {
  const fieldRef = useRef<HTMLSpanElement>();
  const store = useStore();
  const baseColumns = useAtomValue<GenericTableColumn<M>[]>(baseColumnsAtom as Atom<GenericTableColumn<M>[]>, {
    store,
  });

  const {
    ref: popperRef,
    popperId,
    popout,
    close: closePopper,
    toggle: togglePopper,
  } = usePopperState<HTMLButtonElement>({ id: field });
  const popperTriggerIsInView = useIsInView(popperRef, { threshold: 1, rootMargin: "-50px" });

  const canPopout = useMemo(
    () => !isNothing(fieldRef?.current) && fieldRef?.current.scrollWidth > fieldRef?.current.clientWidth,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [fieldRef?.current],
  );

  const column =
    useMemo(() => baseColumns?.find(({ field: f }) => f === field), [baseColumns, field]) ||
    ({} as GenericTableColumn<M>);
  const { dataCellClass, dataCellStyle, dataTooltipContent, tooltip, renderDataCell = null } = column;

  const path = field as AllKeys<M>;
  const baseValue = pickFromRecord(row, [path], true)?.[path as string];

  const definitionTT = typeof dataTooltipContent === "function" ? dataTooltipContent(row) : dataTooltipContent;

  const tt = typeof tooltip === "function" ? tooltip(row) : tooltip;

  const fieldValue = formattedColumnData<M>(baseValue, row, column);

  const dc =
    typeof dataCellClass === "function"
      ? dataCellClass(pickFromRecord(row, [field as AllKeys<M>], true)?.[field as string], row)
      : dataCellClass;

  const ds =
    typeof dataCellStyle === "function"
      ? (dataCellStyle as DSS<M>)(pickFromRecord(row, [field as AllKeys<M>], true)?.[field as string], row)
      : dataCellStyle;

  const stopDefaultEventBehavior = (event: any): void => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
  };

  return (
    <>
      <Maybe condition={!!renderDataCell}>{renderDataCell?.(row)}</Maybe>
      <Maybe condition={!renderDataCell}>
        <Maybe condition={column?.type !== "text-popper"}>
          <Tooltip title={tt}>
            <Stack direction="row" gap={1} alignItems="center">
              <Typography className={classes.CellText}>
                <>{fieldValue}</>
              </Typography>
              <Maybe condition={!!definitionTT && !!fieldValue && fieldValue !== MISSING_DATA}>
                <Tooltip title={definitionTT}>
                  <InfoRounded className={classes.Info} />
                </Tooltip>
              </Maybe>
            </Stack>
          </Tooltip>
        </Maybe>
        <Maybe condition={column?.type === "text-popper"}>
          <ClickAwayListener onClickAway={closePopper}>
            <Box>
              <Tooltip title={canPopout && !popout && tt}>
                <Stack gap={0.5}>
                  <Button
                    ref={popperRef}
                    className={classes.DropDownButton}
                    variant="outlined"
                    {...px({
                      endIcon: canPopout && (
                        <>
                          <ArrowDropDown fontSize="large" sx={{ rotate: popout ? "180deg" : "0deg" }} />
                          <Maybe condition={!!definitionTT}>
                            <Tooltip title={definitionTT}>
                              <InfoRounded className={classes.Info} />
                            </Tooltip>
                          </Maybe>
                        </>
                      ),
                    })}
                    style={{
                      borderColor: "transparent",
                      paddingLeft: 0,
                    }}
                    onClick={(event: MouseEvent<HTMLButtonElement>) => {
                      stopDefaultEventBehavior(event);
                      togglePopper();
                    }}
                    disabled={!canPopout}
                  >
                    <Typography ref={fieldRef} className={classes.CellText}>
                      <>{fieldValue}</>
                    </Typography>
                  </Button>
                </Stack>
              </Tooltip>
              <Popper
                id={popperId}
                transition
                open={canPopout && popout && popperTriggerIsInView}
                anchorEl={popperRef?.current}
                sx={{
                  overflow: "visible",
                  filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                  zIndex: 10000,
                }}
              >
                {({ TransitionProps }) => (
                  <Fade {...TransitionProps} timeout={100}>
                    <Paper onClick={stopDefaultEventBehavior}>
                      <Box
                        className={dc}
                        sx={{
                          padding: "25px 15px 15px 15px",
                          ...ds,
                        }}
                      >
                        <Typography className={classes.CellText}>
                          <>{fieldValue}</>
                        </Typography>
                      </Box>
                    </Paper>
                  </Fade>
                )}
              </Popper>
            </Box>
          </ClickAwayListener>
        </Maybe>
      </Maybe>
    </>
  );
};

export default GenericTableViewField;
