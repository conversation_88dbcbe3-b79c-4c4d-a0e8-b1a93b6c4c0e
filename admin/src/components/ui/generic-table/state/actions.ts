import { Atom, atom } from "jotai";
import { GenericTableSort, GenericTableSortType } from "../types/generic-table-sort";
import {
  pick<PERSON>romR<PERSON>ord,
  AllKeys,
  isNothing,
  NonEmptyArray,
  searchByKeys,
  compareDateTime,
} from "@rubiconcarbon/frontend-shared";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { GenericTableColumn } from "../types/generic-table-column";
import { rowsAtom, columnsAtom, serverActionsAtom, baseColumnsAtom, externalAtom, renderedRowsAtom } from "./structure";
import { GenericTableGlobalSearch } from "../types/generic-table-global-search";
import { formattedColumnData } from "../utilities/format-column-data";

// ATOMS
export const sortsAtom = atom<GenericTableSort<any>>({
  sorts: {},
});
export const globalSearchAtom = atom<GenericTableGlobalSearch<any>>({ term: "", searchKeys: [] as any });
export const expandedRowsAtom = atom<Record<string, boolean>>({});

// DERIVED ATOMS
export const hasRowsAtom = atom((get) => get(renderedRowsAtom)?.length > 0);

export const filteredRowsAtom = atom((get) => {
  const rows = get(rowsAtom);
  const serverActions = get(serverActionsAtom);

  if (serverActions?.search) return rows;

  const search = get(globalSearchAtom);

  if (isNothing(search?.term, ["string"])) return rows;

  return searchRows(rows, search);
});

export const sortedRowsAtom = atom((get) => {
  const rows = get(filteredRowsAtom);
  const serverActions = get(serverActionsAtom);

  if (serverActions?.sort) return rows;

  const columns = get(columnsAtom);
  const sorts = get(sortsAtom)?.sorts || {};

  const sortableColumns = columns.filter(({ sortable }) => isNothing(sortable) || sortable !== false);

  return sortRows(rows, sorts, sortableColumns);
});

export const exportableColumnsAtom = atom((get) => {
  const baseColumns = get(baseColumnsAtom as Atom<GenericTableColumn<any>[]>);
  const rows = get(rowsAtom);
  const { nested } = get(externalAtom)?.export || {};

  const firstLevelColumns = baseColumns.filter((column) => shouldIncludeColumn(column, rows, nested));
  const nestedLevelColumns = getNestedColumns(baseColumns, rows, nested);

  return [...firstLevelColumns, nestedLevelColumns];
});

export const exportableLabelsAtom = atom((get) => {
  const exportableColumns = get(exportableColumnsAtom);
  const { nested } = get(externalAtom)?.export || {};

  const firstLevelLabels = exportableColumns
    .filter((column): column is GenericTableColumn<any> => !!column.field)
    .map((column) => column.label);

  const nestedLevelLabels = exportableColumns
    .filter((column): column is Partial<Record<string, GenericTableColumn<any>[]>> => !column.field)
    .flatMap((record) =>
      Object.entries(record).flatMap(([key, columns]) => {
        const [, field] = key.split(">>");
        const uniquenessLabel = (nested?.[field]?.uniquenessLabel || "").trim();
        const space = uniquenessLabel ? " " : "";

        return columns.map(({ label }) =>
          firstLevelLabels.includes(label) ? `${uniquenessLabel}${space}${label}` : label,
        );
      }),
    );

  return [...firstLevelLabels, ...nestedLevelLabels];
});

export const exportableTableValuesAtom = atom((get) => {
  const rows = get(sortedRowsAtom);
  const columns = get(exportableColumnsAtom);

  return rows.flatMap((row) => {
    const firstLevelValues = columns
      .filter((column) => column.field)
      .map((column: GenericTableColumn<any>) => {
        const path = column.field as AllKeys<any>;
        const baseValue = pickFromRecord(row, [path], true)?.[path as string];
        return formattedColumnData(baseValue, row, column) as string;
      });

    const nestedLevelValues = columns
      .filter((column) => !column.field)
      .flatMap((record) =>
        Object.entries(record).flatMap(([key, nestedColumns]) => {
          const [, parentField] = key.split(">>");
          const nestedRows = row[parentField] || [];

          return nestedRows.map((nestedRow: GenericTableRowModel<any>) =>
            nestedColumns.map((column: GenericTableColumn<any>) => {
              const path = column.field as AllKeys<any>;
              const baseValue = pickFromRecord(nestedRow, [path], true)?.[path as string];
              return formattedColumnData(baseValue, nestedRow, column) as string;
            }),
          );
        }),
      );

    return nestedLevelValues.length
      ? nestedLevelValues.map((entry) => [...firstLevelValues, ...entry])
      : [firstLevelValues];
  });
});

// WRITABLE ATOMS
export const updateExpandedRowsAtom = atom(null, (get, set, { id }: { id: string }) => {
  const expanded = get(expandedRowsAtom)?.[id];

  set(expandedRowsAtom, { ...get(expandedRowsAtom), [id]: !expanded });
});

export const updateSortsAtom = atom(
  null,
  (get, set, { field, order }: { field: string; order: GenericTableSortType }) => {
    const currentSorts = get(sortsAtom).sorts || {};
    const newSorts = { ...currentSorts, [field]: order };

    const onColumSortChange = get(sortsAtom).onColumSortChange;

    if (onColumSortChange) onColumSortChange(field, order);

    set(sortsAtom, { ...get(sortsAtom), sorts: newSorts });
  },
);

// UTILITIES
const searchRows = <T>(rows: T[], search: GenericTableGlobalSearch<any>): T[] => {
  return searchByKeys(search?.term, rows, search.searchKeys as NonEmptyArray<AllKeys<T>>);
};

const sortRows = <T>(
  rows: T[],
  sorts: Partial<Record<keyof T, GenericTableSortType>>,
  sortableColumns: GenericTableColumn<any>[],
): T[] => {
  let sortedRows = [...rows];

  for (const [key, sort] of Object.entries(sorts)) {
    const orderNumber = sort === "asc" ? -1 : sort === "desc" ? 1 : 0;
    const sortColumn = sortableColumns.find(({ field }) => field === key);

    if (sortColumn) {
      const getter = sortColumn.missingDataValue
        ? typeof sortColumn.missingDataValue !== "function"
          ? (): any => sortColumn.missingDataValue
          : (value: any): any => (sortColumn.missingDataValue as (row: GenericTableRowModel<any>) => any)(value)
        : sortColumn.deriveDataValue || sortColumn.transformDataValue || ((value: any): any => value);

      sortedRows = sortedRows.sort((a, b) => {
        const [first, second] = sortColumn.deriveDataValue
          ? [b, a]
          : [
              pickFromRecord(b, [sortColumn?.field as AllKeys<T>], true)?.[sortColumn?.field as string],
              pickFromRecord(a, [sortColumn?.field as AllKeys<T>], true)?.[sortColumn?.field as string],
            ];

        const firstValue = getter(first);
        const secondValue = getter(second);

        if (firstValue === undefined || secondValue === null) return 1 * orderNumber;
        if (secondValue === undefined || firstValue === null) return -1 * orderNumber;

        return (
          (["date", "datetime"].includes(sortColumn.type)
            ? compareDateTime(firstValue, secondValue)
            : String(firstValue || "")?.localeCompare(String(secondValue || ""), undefined, {
                numeric: true,
                usage: "sort",
              })) * orderNumber
        );
      });
    }
  }

  return sortedRows;
};

const shouldIncludeColumn = <T>(
  column: GenericTableColumn<any>,
  rows: T[],
  nested: Partial<
    Record<
      string,
      {
        uniquenessLabel?: string;
        columns: GenericTableColumn<any>[];
      }
    >
  >,
): boolean => {
  const { field, type = "freetext", hide, exportable } = column;
  return (
    !nested?.[field] &&
    type !== "action" &&
    (isNothing(exportable)
      ? !isNothing(hide)
        ? typeof hide === "function"
          ? !hide(rows)
          : !hide
        : true
      : !!exportable)
  );
};

const getNestedColumns = <T>(
  baseColumns: GenericTableColumn<any>[],
  rows: T[],
  nested: Partial<
    Record<
      string,
      {
        uniquenessLabel?: string;
        columns: GenericTableColumn<any>[];
      }
    >
  >,
): Record<string, GenericTableColumn<any>[]> => {
  return baseColumns.reduce(
    (record, { field }) => {
      if (nested?.[field]) {
        const allNestedRows = rows?.flatMap((row) => row?.[field]);
        return {
          ...record,
          [`>>${field}`]: nested?.[field]?.columns?.filter(
            ({ type = "freetext", hide, exportable }) =>
              type !== "action" &&
              (isNothing(exportable)
                ? !isNothing(hide)
                  ? typeof hide === "function"
                    ? !hide(allNestedRows)
                    : !hide
                  : true
                : !!exportable),
          ),
        };
      }
      return record;
    },
    {} as Record<string, GenericTableColumn<any>[]>,
  );
};
