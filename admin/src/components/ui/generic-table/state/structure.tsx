import { Atom, atom, WritableAtom } from "jotai";
import { GenericTableColumn } from "../types/generic-table-column";
import { GenericTablePagination } from "../types/generic-table-pagination";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { GenericTableServerActions } from "../types/generic-table-server-actions";
import { GenericTableExternal } from "../types/generic-table-external";
import ExpandIconButton from "./components/expand-icon-button";
import { isNothing } from "@rubiconcarbon/frontend-shared";
import { sortedRowsAtom } from "./actions";
import { atomFamily } from "jotai/utils";
import { GenericTableForm } from "../types/generic-table-form";
import { uuid } from "@rubiconcarbon/shared-types";

export type MIUDKey = string | uuid | number;

export type MIUDAction<T> =
  | { type: "move"; from: number | MIUDKey; to: number }
  | { type: "insert"; at?: number; item: T }
  | { type: "update"; at: number | MIUDKey; item: Partial<T> }
  | { type: "delete"; at: number | MIUDKey };

export type MIUDResult = {
  success: boolean;
  error?: string;
};

export const DEFAULT_PAGE_LIMIT = 25;
export const ROWS_PER_PAGE_OPTIONS = [25, 50, 75, 100];

// ATOM LEVEL UTILITIES
/**
 * The `miudAtom` function creates a writable atom for managing CRUD operations on an array of items
 * with specified actions like move, insert, update, and delete.
 * @param baseAtom - The `baseAtom` parameter in the `miudAtom` function is a writable atom that stores
 * an array of items of type `T`. It is used as the base atom for performing insert, update, and delete
 * operations on the array.
 * @param keyExtractor - The `keyExtractor` parameter in the `miudAtom` function is a function that
 * extracts a unique key from each item in the array. By default, it assumes that each item has a
 * property named `id` that serves as the unique key. However, you can provide a custom `key
 */
const miudAtom = <T,>(
  baseAtom: WritableAtom<T[], [T[]], void>,
  keyExtractor: (item: T) => MIUDKey = (item: T & { id: MIUDKey }): MIUDKey => item?.id,
): WritableAtom<T[], [action: MIUDAction<T>], MIUDResult> =>
  atom(
    (get) => get(baseAtom),
    (get, set, action: MIUDAction<T>): MIUDResult => {
      const array = get(baseAtom);
      const copy = [...array];
      const result: MIUDResult = { success: true };

      const findIndex = (at: number | MIUDKey): number =>
        typeof at === "number" ? at : copy.findIndex((item) => item[keyExtractor(item)] === at);

      const isValidIndex = (index: number): boolean => index >= 0 && index < copy.length;

      switch (action.type) {
        case "move": {
          const fromIndex = findIndex(action.from);
          const toIndex = action.to;
          if (!isValidIndex(fromIndex) || !isValidIndex(toIndex)) {
            return { success: false, error: "Invalid index for move operation" };
          }
          const [moving] = copy.splice(fromIndex, 1);
          copy.splice(toIndex, 0, moving);
          break;
        }

        case "insert": {
          const index = action.at ?? copy.length;
          if (index < 0 || index > copy.length) {
            return { success: false, error: "Invalid index for insert operation" };
          }
          copy.splice(index, 0, action.item);
          break;
        }

        case "update": {
          const index = findIndex(action.at);
          if (!isValidIndex(index)) {
            return { success: false, error: "Invalid index or key for update operation" };
          }
          copy[index] = { ...copy[index], ...action.item };
          break;
        }

        case "delete": {
          const index = findIndex(action.at);
          if (!isValidIndex(index)) {
            return { success: false, error: "Invalid index or key for delete operation" };
          }
          copy.splice(index, 1);
          break;
        }

        default:
          return { success: false, error: "Invalid operation type" };
      }

      set(baseAtom, copy);
      return result;
    },
  );

// ATOMS
export const rowsAtom = atom<GenericTableRowModel<any>[]>([]);
export const columnsAtom = atom<GenericTableColumn<any>[]>([]);
export const paginationAtom = atom<GenericTablePagination>({
  limit: DEFAULT_PAGE_LIMIT,
  offset: 0,
  size: 0,
  rowsPerPageOptions: ROWS_PER_PAGE_OPTIONS,
});
export const serverActionsAtom = atom<GenericTableServerActions>({});
export const externalAtom = atom<GenericTableExternal<any>>({});
export const attachExpandedColumnAtom = atom(false);
export const activeRowsAtom = atom<GenericTableRowModel<any>[]>([]);
export const observedColumnWidthAtom = atom<Record<string, number>>({});
export const formAtom = atom<Partial<GenericTableForm<any>>>({});

// DERIVED ATOMS
export const totalPagesAtom = atom((get) => {
  const { totalCount, limit } = get(paginationAtom);
  return totalCount && limit ? Math.ceil(totalCount / limit) : 0;
});

export const baseColumnsAtom = atom((get) => {
  const columns = get(columnsAtom);
  const shouldAttach = get(attachExpandedColumnAtom);

  if (shouldAttach && columns[0]?.field !== "_expand_") {
    return [
      {
        type: "action",
        field: "_expand_" as any,
        creatable: false,
        editable: false,
        exportable: false,
        sortable: false,
        hide: false,
        width: 40,
        fixedWidth: true,
        renderDataCell: (row: GenericTableRowModel<any>): JSX.Element => <ExpandIconButton row={row} />,
      } as GenericTableColumn<any>,
      ...columns,
    ];
  }

  return columns;
});

export const rowColumnsAtom = atom((get) => {
  const baseColumns = get(baseColumnsAtom as Atom<GenericTableColumn<any>[]>);
  const rows = get(rowsAtom);
  const activeRows = get(activeRowsAtom);

  if (!activeRows) return getRowColumns(baseColumns, rows);

  const isCreating = activeRows?.every((row: GenericTableRowModel<any>) => row?.creating);
  const isEditing = activeRows?.every((row: GenericTableRowModel<any>) => row?.editing);
  const amendingIds = isEditing ? activeRows?.map((row: GenericTableRowModel<any>) => row?.id) : [];

  const allRows = [
    ...(isCreating ? activeRows : []),
    ...(isEditing ? getAmendingRows(rows, activeRows, amendingIds) : rows),
  ];

  return getRowColumns(baseColumns, allRows);
});

export const pagedRowsAtom = atom((get) => {
  const rows = get(sortedRowsAtom);
  const serverActions = get(serverActionsAtom);
  const pagination = get(paginationAtom);

  if (serverActions?.paginate) return rows;

  const start = pagination.offset ?? 0;
  const end = start + (pagination.limit ?? DEFAULT_PAGE_LIMIT);

  return rows.slice(start, end);
});

export const renderedRowsAtom = atom((get) => {
  const rows = get(pagedRowsAtom);
  const activeRows = get(activeRowsAtom);
  const { appendNewRows } = get(externalAtom);

  const isCreating = activeRows?.every((row) => row?.creating);

  if (!isCreating) return rows;

  const creatingRows = activeRows.filter((row) => row.creating);
  const nonCreatingRows = rows.filter((row) => !row.creating);

  return appendNewRows ? [...nonCreatingRows, ...creatingRows] : [...creatingRows, ...nonCreatingRows];
});

export const observedColumnWidthAtomFamily = atomFamily((name: string) =>
  atom(
    (get) => get(observedColumnWidthAtom)[name],
    (get, set, newValue: number) => {
      const current = get(observedColumnWidthAtom);
      if (current[name] !== Math.max(current?.[name] || 0, newValue))
        set(observedColumnWidthAtom, { ...current, [name]: Math.max(current?.[name] || 0, newValue) });
    },
  ),
);

export const rowAtomsAtom = miudAtom(rowsAtom);

// WRITABLE ATOMS
export const updatePageNumberAtom = atom(null, (get, set, pageNumber: number) => {
  const pagination = get(paginationAtom);

  if (pagination.onPageNumberChange) pagination.onPageNumberChange(pageNumber);

  set(paginationAtom, {
    ...pagination,
    offset: pageNumber * (pagination.limit ?? DEFAULT_PAGE_LIMIT),
  });
});

export const updateRowsPerPageAtom = atom(null, (get, set, rowsPerPage: number) => {
  const pagination = get(paginationAtom);

  if (pagination.onRowsPerPageChange) pagination.onRowsPerPageChange(rowsPerPage);

  set(paginationAtom, {
    ...pagination,
    limit: rowsPerPage,
    offset: 0,
  });
});

// UTILITIES
const getAmendingRows = <M,>(
  rows: GenericTableRowModel<M>[],
  activeRows: GenericTableRowModel<M>[],
  amendingIds: any[],
): GenericTableRowModel<M>[] => {
  return rows
    .map((row) => (amendingIds.includes(row?.id) ? activeRows.find(({ id }) => row.id === id) : row))
    .filter((row) => !!row);
};

const getRowColumns = <M,>(
  columns: GenericTableColumn<M>[],
  rows: GenericTableRowModel<M>[],
): GenericTableColumn<M>[] => {
  const rowColumns = columns.filter(({ collapse, hide }) => {
    const collapsed = !isNothing(collapse) ? (typeof collapse === "function" ? collapse(rows) : collapse) : false;
    if (collapsed) return false;

    const hidden = !isNothing(hide) ? (typeof hide === "function" ? hide(rows) : hide) : false;
    if (hidden) return false;

    return true;
  });

  return rowColumns;
};
