import React from "react";
import { <PERSON><PERSON><PERSON>, TableCell, TableHead, TableRow, TableSortLabel, Stack, SxProps } from "@mui/material";
import COLORS from "../../theme/colors";
import { ColDef, SortOrder } from "../enhanced-table";
import { Maybe } from "@rubiconcarbon/frontend-shared";

const defaultHeaderStyle = {
  align: "left",
};

interface TableHeaderProps {
  isExpandable: boolean;
  columnsDef: ColDef[];
  orderDirection: SortOrder;
  valueToOrderBy: string;
  headerStyle?: SxProps;
  sortHandler: (columnName: string) => void;
}

export default function TableHeader({
  columnsDef,
  isExpandable,
  orderDirection = SortOrder.DESC,
  valueToOrderBy,
  sortHandler,
}: TableHeaderProps): JSX.Element {
  const onSortHandler = (columnName: string): void => {
    sortHandler(columnName);
  };

  return (
    <TableHead sx={{ backgroundColor: COLORS.white }}>
      <TableRow>
        {isExpandable && <TableCell key={`expand`}></TableCell>}
        {columnsDef.map((columnDef, index) => (
          <Maybe condition={!columnDef.hide} key={`${columnDef.columnName}-${index}`}>
            <TableCell sx={{ ...defaultHeaderStyle, ...(columnDef?.headerStyle || {}) }}>
              {columnDef.sortable === false ? (
                <Stack direction="row" gap={1}>
                  <Maybe condition={!!columnDef.displayComponent}>{columnDef.displayComponent}</Maybe>
                  <Typography variant="body2" component="h4" fontWeight="700">
                    {columnDef.displayName}
                  </Typography>
                </Stack>
              ) : (
                <TableSortLabel
                  active={valueToOrderBy === columnDef.columnName}
                  direction={valueToOrderBy === columnDef.columnName ? orderDirection : SortOrder.ASC}
                  onClick={() => onSortHandler(columnDef.columnName)}
                >
                  <Stack direction="row" gap={1}>
                    <Maybe condition={!!columnDef.displayComponent}>{columnDef.displayComponent}</Maybe>
                    <Typography variant="body2" component="h4" fontWeight="700">
                      {columnDef.displayName}
                    </Typography>
                  </Stack>
                </TableSortLabel>
              )}
            </TableCell>
          </Maybe>
        ))}
      </TableRow>
    </TableHead>
  );
}
