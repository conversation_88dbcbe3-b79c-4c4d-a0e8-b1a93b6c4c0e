import { Grid, Typography, Box } from "@mui/material";
import React from "react";
import { styled, SxProps } from "@mui/material/styles";
import { isEmpty } from "lodash";
import { MISSING_DATA } from "@constants/constants";

type ItemDetailsProps = {
  label: string;
  value: any;
  spacing?: {
    label?: {
      xs?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };
    value?: {
      xs?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };
  };
  sx?: SxProps;
};

const ItemLabel = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(1),
  textAlign: "left",
  color: theme.palette.text.primary,
  margin: 0,
  fontWeight: 800,
}));

const ItemValue = styled(Typography)(({ theme }) => ({
  padding: theme.spacing(1),
  textAlign: "left",
  color: theme.palette.text.secondary,
  margin: 0,
}));

export default function ItemDetails({ label, value, spacing, sx = {} }: ItemDetailsProps): JSX.Element {
  const isElement = React.isValidElement(value);
  return (
    <Grid container spacing={0} sx={sx}>
      <Grid
        item
        xs={spacing?.label?.xs ? spacing.label.xs : 4}
        sm={spacing?.label?.sm ? spacing.label.sm : spacing?.label?.xs ? spacing.label.xs : 4}
        md={spacing?.label?.md ? spacing.label.md : spacing?.label?.xs ? spacing.label.xs : 4}
        lg={spacing?.label?.lg ? spacing.label.lg : spacing?.label?.xs ? spacing.label.xs : 4}
      >
        <ItemLabel>
          <Typography variant="body2" component="span">
            {label}
          </Typography>
        </ItemLabel>
      </Grid>
      <Grid
        item
        xs={spacing?.value?.xs ? spacing.value.xs : 8}
        sm={spacing?.value?.sm ? spacing.value.sm : spacing?.value?.xs ? spacing.value.xs : 8}
        md={spacing?.value?.md ? spacing.value.md : spacing?.value?.xs ? spacing.value.xs : 8}
        lg={spacing?.value?.lg ? spacing.value.lg : spacing?.value?.xs ? spacing.value.xs : 8}
        xl={spacing?.value?.xl ? spacing.value.xl : spacing?.value?.xs ? spacing.value.xs : 8}
      >
        {isElement ? (
          <Box mt={0.5} ml={0.5}>
            {value}
          </Box>
        ) : (
          <ItemValue>
            <Typography variant="body2" component="span" ml={0} sx={{ fontWeight: 500, whiteSpace: "pre-line" }}>
              {isEmpty(value) ? MISSING_DATA : value}
            </Typography>
          </ItemValue>
        )}
      </Grid>
    </Grid>
  );
}
