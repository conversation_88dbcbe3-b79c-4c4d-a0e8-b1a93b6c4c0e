import { OrganizationUserRole } from "@rubiconcarbon/shared-types";
import { FormControlLabel, Typography, FormLabel, Radio, RadioGroup, Box } from "@mui/material";

const radioLabelStyle = {
  marginTop: 1,
  marginBottom: 1,
};

interface RolesGroupProps {
  selectedRole?: OrganizationUserRole;
  roleChangeHandler: (event: React.ChangeEvent<HTMLInputElement>, role: OrganizationUserRole) => void;
}

export default function RolesGroup({ selectedRole, roleChangeHandler }: RolesGroupProps): JSX.Element {
  return (
    <Box>
      <FormLabel component="p" sx={{ fontWeight: 600, lineHeight: "175%" }}>
        Roles
      </FormLabel>
      <FormLabel id="user-role-group">Select one role below to set user permissions.</FormLabel>
      <RadioGroup
        aria-labelledby="user-role-group"
        name="controlled-radio-buttons-group"
        onChange={roleChangeHandler.bind(selectedRole)}
      >
        <FormControlLabel
          sx={radioLabelStyle}
          disabled
          value={OrganizationUserRole.MANAGER}
          control={<Radio required />}
          label={
            <div>
              <Typography>Manager</Typography>
              <Typography variant="caption">
                Can submit transactions and manage permissions for organization users.
              </Typography>
            </div>
          }
        />
        <FormControlLabel
          sx={radioLabelStyle}
          value={OrganizationUserRole.VIEWER}
          control={<Radio required />}
          label={
            <div>
              <Typography>Viewer</Typography>
              <Typography variant="caption">Can view account information and request quotes.</Typography>
            </div>
          }
        />
        <FormControlLabel
          sx={radioLabelStyle}
          value={OrganizationUserRole.TRANSACTOR}
          control={<Radio required />}
          label={
            <div>
              <Typography>Transaction Manager</Typography>
              <Typography variant="caption">
                Can view account information, request quotes, and submit retirement requests.
              </Typography>
            </div>
          }
        />
      </RadioGroup>
    </Box>
  );
}
