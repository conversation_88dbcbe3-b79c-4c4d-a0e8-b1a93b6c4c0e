// should this be done on CSS instead?

const COLORS = {
  rubiconGreen: "#094436",
  rubiconGreenMedium: "#9AB79A",
  rubiconGreenLight: "#D1E1CB",
  rubiconGreenPale: "#C3E2C3",
  yellow: "#FDFF50",
  black: "#121212",
  darkGrey: "#767676",
  mediumGrey: "#B8B8B8",
  lightGrey: "#DCDCDC",
  whiteGrey: "#F0F0F0",
  white: "#FFFFFF",
  blue: "#0053BF",
  green: "#007C45",
  orange: "#CC4E00",
  red: "#C90005",
  lightTeal: "#81CDCB",
  tableHeader: "#F0F0F0",
  modalMargins: "#F0F0F0",
  modalConfirm: "#CC4E00",
  lightBlue: "#C9D9EF",
  paleBlue: "#0288D10A",
  paleGreen: "#9AB79A0A",
  normalGray: "#F5F5F5",
  pureBlack: "#000000",
  paleGray: "#00000014",
  paleBlack: "#000000DE",
  chartBlue: "#03A9F4",
  chartGreen: "#4CAF50",
  chartOrange: "#FF9800",
  chartsYellow: "#FFD54F",
  chartPaleGreen: "#9AB79A",
  chartsPaleBlue: "#0288D1",
  chartsPurple: "#3F51B5",
  infoBlue: "#5470C6",
  warning: "#ED6C02",
  lightGreen: "#007c4514",
};

export default COLORS;
