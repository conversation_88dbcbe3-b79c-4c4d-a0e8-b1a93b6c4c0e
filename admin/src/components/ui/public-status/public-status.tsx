import PublicOutlinedIcon from "@mui/icons-material/PublicOutlined";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import Chip from "@mui/material/Chip";
import { RetirementVisibility } from "@models/retirement";

interface PublicStatusProps {
  isPublic: boolean;
}

export default function PublicStatus({ isPublic }: PublicStatusProps): JSX.Element {
  if (isPublic)
    return (
      <Chip
        sx={{ height: 25 }}
        icon={<PublicOutlinedIcon sx={{ fontSize: 17 }} />}
        label={RetirementVisibility.PUBLIC}
      />
    );

  return (
    <Chip sx={{ height: 25 }} icon={<LockOutlinedIcon sx={{ fontSize: 17 }} />} label={RetirementVisibility.PRIVATE} />
  );
}
