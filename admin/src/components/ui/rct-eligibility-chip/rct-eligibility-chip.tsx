import { Box, Stack, SxProps, Tooltip, Typography } from "@mui/material";
import { TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";
import { projectIsRCTEligible } from "@utils/helpers/project/project-is-rct-eligible";
import { isNothing, Maybe, px, toDecimal } from "@rubiconcarbon/frontend-shared";
import MatIcon from "../mat-icon/mat-icon";
import { useMemo } from "react";

type RCTEligibilityChipProps = {
  iconOnly?:
    | {
        eligible?: boolean;
        ineligible?: boolean;
      }
    | boolean;
  vintage: TrimmedProjectVintageResponse | PrimitiveTypeVintage;
  iconSx?: SxProps;
  typeSx?: SxProps;
};

const ELIGIBILITY_TITLE = (vintage: TrimmedProjectVintageResponse | PrimitiveTypeVintage): string => {
  const { riskBufferPercentage } = vintage || {};
  const { rctStandard, isScienceTeamApproved, suspended } = vintage?.project || {};

  const eligible = projectIsRCTEligible(vintage);
  const riskBuffered =
    isNothing(riskBufferPercentage) ||
    (!isNothing(riskBufferPercentage) && toDecimal(riskBufferPercentage).lessThanOrEqualTo(1));

  return `RCT Eligibility: This vintage ${!eligible ? "does not " : ""}qualif${eligible ? "ies" : "y"} for the RCT portfolio${!rctStandard ? "; does not" : ","} meet${!rctStandard ? "" : "ing"} the RCT standard${!riskBuffered ? ", without" : " with"} a Buffer of less than 100%, ${!isScienceTeamApproved ? "is not " : ""}website ready, and the project is currently ${suspended ? "not " : ""}active.`;
};

export default function RCTEligibilityChip({
  iconOnly = true,
  vintage,
  iconSx = {},
  typeSx = {},
}: RCTEligibilityChipProps): JSX.Element {
  const eligible = projectIsRCTEligible(vintage);
  const displayOnlyIcon = useMemo(
    () =>
      typeof iconOnly === "boolean"
        ? iconOnly
        : (eligible && iconOnly?.eligible) || (!eligible && iconOnly?.ineligible),
    [eligible, iconOnly],
  );

  return (
    <Stack
      component="span"
      direction="row"
      gap={0.5}
      {...px(
        {
          sx: !displayOnlyIcon && {
            padding: "0.5px 5px",
            borderRadius: 3,
            backgroundColor: eligible ? "rgba(237, 247, 237, 1)" : "rgba(253, 237, 237, 1)",
          },
        },
        [false],
      )}
      alignItems="center"
    >
      <Tooltip title={ELIGIBILITY_TITLE(vintage)}>
        <Box display="flex">
          <MatIcon
            value={eligible ? "verified" : "new_releases"}
            variant="round"
            size={18}
            sx={{ color: eligible ? "rgba(7, 125, 85, 1)" : "rgba(211, 47, 47, 1)", ...iconSx }}
          />
        </Box>
      </Tooltip>
      <Maybe condition={!displayOnlyIcon}>
        <Typography
          fontSize="0.8125rem"
          sx={{ color: eligible ? "rgba(7, 125, 85, 1)" : "rgba(211, 47, 47, 1)", ...typeSx }}
        >
          {!eligible ? "Not" : ""} RCT Eligible
        </Typography>
      </Maybe>
    </Stack>
  );
}
