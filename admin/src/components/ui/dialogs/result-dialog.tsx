import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import { BaseDialogProps, DialogBackdrop } from "../../../models/dialogs";
import COLORS from "@components/ui/theme/colors";
import { HttpStatusLabelsType } from "@/types/http";
import { HttpStatusLabels } from "@/constants/http";

interface PurchaseResultDialogProps extends BaseDialogProps {
  message: string;
  title: string;
  requestInFlight: boolean;
  responseType: HttpStatusLabelsType;
}

const getTitleColorByResponseType = (responseType: HttpStatusLabelsType): string => {
  switch (responseType) {
    case HttpStatusLabels.ERROR:
      return COLORS.red;
    case HttpStatusLabels.SUCCESS:
      return COLORS.rubiconGreen;
    default:
      return COLORS.lightGrey;
  }
};

export default function ResultDialog({
  isOpen,
  message,
  title,
  onClose,
  requestInFlight,
  responseType,
}: PurchaseResultDialogProps): JSX.Element | null {
  if (requestInFlight) return <DialogBackdrop requestInFlight={requestInFlight} />;

  let responseTitle = "";
  if (responseType === HttpStatusLabels.ERROR) responseTitle = `${title} Execution Failed!`;
  else responseTitle = `${title} Execution Succeeded!`;

  const handleClose = (event: object, reason: "backdropClick" | "escapeKeyDown"): void => {
    if (reason !== "backdropClick") {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onClose={handleClose}>
      <DialogTitle
        sx={{
          color: "white",
          backgroundColor: getTitleColorByResponseType(responseType),
          minWidth: 500,
        }}
      >
        {responseTitle}
      </DialogTitle>
      <DialogContent
        sx={{
          borderTop: "1px solid #B2B6BB",
          borderBottom: "1px solid #B2B6BB",
        }}
      >
        <Typography variant="body2" sx={{ fontSize: 18 }}>
          {message}
        </Typography>
      </DialogContent>
      <DialogActions sx={{ height: 60, backgroundColor: COLORS.modalMargins }}>
        <Button onClick={onClose} disabled={requestInFlight} variant="contained" sx={{ px: 3.5, color: "#FFFFFF" }}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
