import { externalAtom } from "@components/ui/generic-table/state";
import { GenericTableExternal } from "@components/ui/generic-table/types/generic-table-external";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { AuthContext } from "@providers/auth-provider";
import { Chip } from "@mui/material";
import { AllKeys, pickFromRecord, classcat, Maybe, NO_OP } from "@rubiconcarbon/frontend-shared";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { useStore, useAtomValue } from "jotai";
import { MouseEvent, useContext, useMemo } from "react";
import { UnifiedOrganizationModel } from "@models/organization";
import { OrganizationTableExtensions } from "@/types/organization-table-extensions";

import classes from "./style.module.scss";

type UnifiedOrgStatusChipProps = {
  interactive?: boolean;
  row: GenericTableRowModel<UnifiedOrganizationModel>;
  path: {
    value: AllKeys<UnifiedOrganizationModel>;
    label: AllKeys<UnifiedOrganizationModel>;
  };
};

export const UnifiedOrgStatusChip = ({ interactive = true, row, path }: UnifiedOrgStatusChipProps): JSX.Element => {
  const { user } = useContext(AuthContext);
  const store = useStore();
  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    UnifiedOrganizationModel,
    OrganizationTableExtensions
  >;

  const {
    toggleStatusModal = NO_OP,
    toggleOnboardModal = NO_OP,
    setViewingRow = NO_OP,
  } = useMemo(
    () => (typeof extensions === "function" ? extensions(row) : extensions) || ({} as any),
    [extensions, row],
  );

  const disable = !interactive || !user?.hasPermission(PermissionEnum.ORGANIZATIONS_UPDATE);

  const openModal = (event: MouseEvent<HTMLDivElement>): void => {
    event?.preventDefault();
    event?.stopPropagation();

    if (!disable) {
      setViewingRow(row);
      if (path.value === "isOnboarded") {
        toggleOnboardModal();
      } else {
        toggleStatusModal();
      }
    }
  };

  const value = pickFromRecord(row, [path.value], true)?.[path.value];
  const label = pickFromRecord(row, [path.label], true)?.[path.label];

  return (
    <Chip
      classes={{
        root: classes.Chip,
        label: classcat({ [classes.LabelWithIcon]: path.value === "isOnboarded" }),
      }}
      sx={{
        color: value ? "rgba(7, 125, 85, 1)" : "rgba(211, 47, 47, 1)",
        backgroundColor: value ? "rgba(237, 247, 237, 1)" : "rgba(253, 237, 237, 1)",
        cursor: disable ? "initial" : "pointer",
      }}
      icon={
        <Maybe condition={path.value === "isOnboarded"}>
          <MatIcon
            value={value ? "check" : "clear"}
            size={20}
            sx={{
              color: value ? "rgba(7, 125, 85, 1) !important" : "rgba(211, 47, 47, 1) !important",
              padding: "0 2px 0 5px",
              boxSizing: "content-box",
            }}
          />
        </Maybe>
      }
      label={label}
      clickable={disable}
      onClick={openModal}
    />
  );
};
