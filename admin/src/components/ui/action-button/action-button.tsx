import { PropsWithChildren } from "react";
import { Button, Box, Tooltip, SxProps } from "@mui/material";
import COLORS from "../theme/colors";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { AuthContext } from "@providers/auth-provider";
import { useContext } from "react";

const buttonSX = {
  height: 35,
  color: COLORS.rubiconGreen,
  borderColor: COLORS.lightGrey,
  backgroundColor: "white",
  "&:hover": {
    backgroundColor: "rgb(209, 225, 203)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
  },
};

interface ActionButtonProps {
  onClickHandler: (event: object) => void;
  startIcon?: React.ReactNode;
  isDisabled?: boolean;
  requiredPermission?: PermissionEnum;
  tooltip?: string;
  style?: SxProps;
}

export default function ActionButton({
  children,
  onClickHandler,
  startIcon,
  isDisabled,
  requiredPermission,
  tooltip = "",
  style,
}: PropsWithChildren<ActionButtonProps>): JSX.Element {
  const { user: loginUser } = useContext(AuthContext);
  const tooltipText =
    requiredPermission && !loginUser.hasPermission(requiredPermission) ? (
      <div>
        Insufficient permissions.
        <br />
        <NAME_EMAIL>.
      </div>
    ) : (
      tooltip
    );
  return (
    <Tooltip title={tooltipText} placement="bottom">
      <Box mr={1}>
        <Button
          sx={{ ...buttonSX, ...style }}
          onClick={onClickHandler}
          variant="outlined"
          startIcon={startIcon}
          disabled={isDisabled || (requiredPermission ? !loginUser.hasPermission(requiredPermission) : false)}
        >
          {children}
        </Button>
      </Box>
    </Tooltip>
  );
}
