import { PropsWithChildren } from "react";
import { But<PERSON>, Box, Tooltip, SxProps } from "@mui/material";
import COLORS from "../theme/colors";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { AuthContext } from "@providers/auth-provider";
import { useContext } from "react";

const buttonSX = {
  height: 35,
  color: "rgba(255, 255, 255, 1)",
  borderColor: COLORS.lightGrey,
  borderRadius: "5px",
  backgroundColor: "rgba(9, 68, 54, 1)",
  "&.Mui-disabled": {
    color: "gray !important",
  },
  "&:hover": {
    backgroundColor: "rgba(9, 68, 54, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
  },
};

export interface ActionButtonProps {
  onClickHandler?: (event: object) => void;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  isDisabled?: boolean;
  requiredPermission?: PermissionEnum;
  tooltip?: string;
  form?: string | undefined;
  style?: SxProps;
  type?: "submit" | "reset" | "button" | undefined;
  variant?: "text" | "outlined" | "contained";
}

export default function ActionButton({
  children,
  onClickHandler,
  startIcon,
  endIcon,
  isDisabled,
  requiredPermission,
  tooltip = "",
  style,
  type,
  form,
  variant = "outlined",
}: PropsWithChildren<ActionButtonProps>): JSX.Element {
  const { user: loginUser } = useContext(AuthContext);
  const tooltipText =
    requiredPermission && !loginUser.hasPermission(requiredPermission) ? (
      <div>
        Insufficient permissions.
        <br />
        <NAME_EMAIL>.
      </div>
    ) : (
      tooltip
    );
  return (
    <Tooltip title={tooltipText} placement="bottom">
      <Box>
        <Button
          data-event-name="action-button-event"
          sx={{ ...buttonSX, ...style }}
          onClick={onClickHandler}
          type={type}
          form={form}
          variant={variant}
          startIcon={startIcon}
          endIcon={endIcon}
          disabled={isDisabled || (requiredPermission ? !loginUser.hasPermission(requiredPermission) : false)}
        >
          {children}
        </Button>
      </Box>
    </Tooltip>
  );
}
