import { Button, SxProps } from "@mui/material";
import { PropsWithChildren } from "react";
import { useAppRouter } from "@hooks/router-hooks";
import useNavigation from "@/hooks/use-navigation";

const buttonBaseStyle = {
  px: 2.5,
  color: "#FFFFFF",
  height: "30px",
};

type BackButtonProps = {
  style?: SxProps;
  popInstead?: boolean;
};

export default function BackButton(props: PropsWithChildren<BackButtonProps>): JSX.Element {
  const { popFromPath } = useNavigation();
  const router = useAppRouter();
  return (
    <Button
      onClick={() => (props?.popInstead ? popFromPath(1) : router.back())}
      variant="contained"
      sx={{ ...buttonBaseStyle, ...(props?.style || {}) }}
    >
      {props.children ?? "BACK"}
    </Button>
  );
}
