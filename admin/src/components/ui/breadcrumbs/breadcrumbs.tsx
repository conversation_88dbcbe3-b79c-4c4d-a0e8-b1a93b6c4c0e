import React from "react";
import Typography from "@mui/material/Typography";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Link from "next/link";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { useMediaQuery } from "@mui/material";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import COLORS from "../theme/colors";

import classes from "./breadcrumbs.module.scss";

export default function BreadCrumbs(): JSX.Element {
  const isSmall = useMediaQuery("(max-width: 1200px)");
  const { breadcrumbs: items = [] } = useBreadcrumbs();

  const breadcrumbs = items.map(({ name, path = "" }, index) => {
    if (index < items.length - 1)
      return (
        <Link key={name} className={`${classes.BreadcrumbItem} ${classes.Navigation}`} href={path}>
          <Typography className={classes.NavigationText} color={COLORS.rubiconGreen} variant="body2">
            {name}
          </Typography>
        </Link>
      );

    if (index === items.length - 1)
      return (
        <Typography className={classes.BreadcrumbItem} key={name} color={COLORS.rubiconGreen} variant="body2">
          {name}
        </Typography>
      );
  });

  return (
    <Maybe condition={breadcrumbs.length > 1}>
      <Breadcrumbs
        className={classes.Breadcrumb}
        separator={<NavigateNextIcon fontSize="small" />}
        maxItems={isSmall ? 5 : 10}
        aria-label="breadcrumb"
      >
        {breadcrumbs}
      </Breadcrumbs>
    </Maybe>
  );
}
