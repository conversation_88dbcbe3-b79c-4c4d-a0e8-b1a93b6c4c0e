import { Typography } from "@mui/material";
import { TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import ProjectWithVintage from "../project-with-vintage/project-with-vintage";
import { CSSProperties } from "react";
import { AssetOrder } from "@models/transaction";

type ProductNameProps = {
  assets: AssetOrder[];
  addTags?: {
    rct?: boolean;
    suspended?: boolean;
  };
  style?: CSSProperties;
};

const ProjectName = ({ assets, addTags, style = {} }: ProductNameProps): JSX.Element => {
  const isMultiple = assets?.length > 1;
  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <ProjectWithVintage
        vintage={assets?.at?.(0)?.projectVintage as unknown as TrimmedProjectVintageResponse}
        addTags={addTags}
        sx={{ fontSize, fontWeight, ...rest }}
      />
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

export default ProjectName;
