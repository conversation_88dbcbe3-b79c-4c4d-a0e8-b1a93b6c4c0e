// Will move this to shared lib when implementation is done

import { Box, Button, ButtonBase, IconButton, ListItemButtonProps, Stack } from "@mui/material";
import { isFunction, Maybe } from "@rubiconcarbon/frontend-shared";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import MatIcon from "../mat-icon/mat-icon";
import { AnimatePresence, motion, PanInfo } from "framer-motion";

export type GenericCarouseNavigationDirection = "left" | "right" | "up" | "down";

export type GenericCarouselKey = number | string;

export type GenericCarouselOrientation = "horizontal" | "vertical";

export type GenericCarouselClass = string | ((data?: any) => string);

export type GenericCarouselSwipeable =
  | {
      threshold: number;
      velocity?: number;
    }
  | boolean;

export type GenericCarouselAnimation = {
  stiffness?: number;
  damping?: number;
  mass?: number;
  velocity?: number;
  duration?: number;
};

export type GenericCarouselItem<K = GenericCarouselKey, D = any> = {
  key: K;
  data: D;
  render?: (data: D) => JSX.Element;
} & Omit<ListItemButtonProps, "className" | "onClick" | "children">;

export type GenericCarouselProps = {
  auto?: boolean | number; // if set, will automatically cycle through slides by the set time or 3000ms interval if value is true
  swipeable?: GenericCarouselSwipeable; // if set, will allow the slide to be swipeable
  affordance?: boolean; // if set, will show the edge of the previous and next slide as long as the current slide dimension allows it
  pauseable?: boolean; // if set, will show a play/pause icon on rendered slide. it pauses/resumes to automatic sliding behavior. this works only when "auto" is set
  navigate?: boolean; // if set, will show the default navigation controls. by default, it is set to true
  indicate?: boolean; // if set, will show the default indicator controls. by default, it os set to true
  infiniteSlide?: boolean; // if set, will enable the slide behavior to loop over when it gets to the end of the carousel in one direction or the other
  slides: GenericCarouselItem[];
  value: GenericCarouselKey;
  orientation?: GenericCarouselOrientation;
  animation?: GenericCarouselAnimation;
  classes?: {
    root?: string;
    sliderRoot?: string;
    slider?: GenericCarouselClass;
    activeSlider?: GenericCarouselClass;
    playPause?: string;
    navigatorRoot?: string;
    navigator?: string;
    indicatorRoot?: string;
    indicator?: GenericCarouselClass;
    activeIndicator?: GenericCarouselClass;
  };
  renderSlide: (data: any, key?: GenericCarouselKey) => JSX.Element;
  renderNavigation?: (
    data?: any,
    key?: GenericCarouselKey,
    direction?: GenericCarouseNavigationDirection,
  ) => JSX.Element; // if set, will override the default navigation controls
  renderIndicator?: (data: any, key?: GenericCarouselKey) => JSX.Element; // if set, will override the default indicator controls
  onSlideChange?: (key: GenericCarouselKey) => void;
};

export type SliderProps = {
  current: number;
  direction: number;
  pauseable: boolean;
  affordance: boolean;
  paused: boolean;
  animation: GenericCarouselAnimation;
  swipeable: GenericCarouselSwipeable;
  slides: GenericCarouselItem[];
  orientation: GenericCarouselOrientation;
  sliderClass: GenericCarouselClass;
  activeSliderClass: GenericCarouselClass;
  playPauseClass: string;
  togglePause: () => void;
  renderSlide: (data: any, key?: GenericCarouselKey) => JSX.Element;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onDragEnd: (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => void;
};

export type NavigationProps = {
  current: number;
  infiniteSlide?: boolean;
  slides: GenericCarouselItem[];
  orientation: GenericCarouselOrientation;
  navigatorRootClass?: string;
  navigatorClass?: string;
  currentSlide: GenericCarouselKey;
  renderNavigation?: (
    data?: any,
    key?: GenericCarouselKey,
    direction?: GenericCarouseNavigationDirection,
  ) => JSX.Element;
  prevSlide: () => void;
  nextSlide: () => void;
};

export type IndicationProps = {
  current: number;
  slides: GenericCarouselItem[];
  indicatorRootClass?: string;
  indicatorClass?: GenericCarouselClass;
  activeIndicatorClass?: GenericCarouselClass;
  renderIndicator?: (data: any, key?: GenericCarouselKey) => JSX.Element;
  goToSlide: (index: number) => void;
};

const getClass = (value: any, data?: any): string => (isFunction(value) ? value(data) : value);

const Slider = ({
  current,
  direction,
  pauseable,
  // affordance = false, // to be implemented
  paused,
  animation,
  swipeable,
  slides,
  orientation,
  sliderClass = "",
  activeSliderClass = "",
  playPauseClass = "",
  togglePause,
  renderSlide,
  onMouseEnter,
  onMouseLeave,
  onDragEnd,
}: SliderProps): JSX.Element => {
  const { stiffness = 200, damping = 30, mass = 1, velocity = 1, duration = 0.2 } = animation || {};

  const [hoverPaused, setHoverPaused] = useState(false);

  const slideVariants = {
    enter: (direction: number): Record<string, any> => {
      return {
        x: orientation === "horizontal" ? direction * 100 + "%" : 0,
        y: orientation === "vertical" ? direction * 100 + "%" : 0,
        opacity: 0,
      };
    },
    center: {
      x: 0,
      y: 0,
      opacity: 1,
    },
    exit: (direction: number): Record<string, any> => {
      return {
        x: orientation === "horizontal" ? direction * -100 + "%" : 0,
        y: orientation === "vertical" ? direction * -100 + "%" : 0,
        opacity: 0,
      };
    },
  };

  const renderSlideContent = (slide: GenericCarouselItem): JSX.Element => {
    if (slide.render) {
      return slide.render(slide.data);
    }
    return renderSlide(slide.data, slide.key);
  };

  return (
    <>
      <AnimatePresence initial={false} custom={direction}>
        <motion.div
          key={current}
          custom={direction}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: { type: "spring", stiffness, damping, mass, velocity, duration },
            y: { type: "spring", stiffness, damping, mass, velocity, duration },
            opacity: { duration },
          }}
          className={`${getClass(sliderClass, slides[current].data)} ${getClass(activeSliderClass, slides[current].data)}`}
          style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
          drag={swipeable ? (orientation === "horizontal" ? "x" : "y") : false}
          dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
          dragElastic={1}
          onDragEnd={onDragEnd}
        >
          <Box
            onMouseEnter={() => {
              if (!paused) {
                setHoverPaused(true);
                onMouseEnter();
              }
            }}
            onMouseLeave={() => {
              if (hoverPaused) {
                setHoverPaused(false);
                onMouseLeave();
              }
            }}
          >
            {renderSlideContent(slides[current])}
          </Box>
        </motion.div>
      </AnimatePresence>
      <Maybe condition={pauseable}>
        <IconButton
          className={playPauseClass}
          sx={{
            position: "absolute",
            bottom: 10,
            right: 10,
            zIndex: 2,
            backgroundColor: "rgba(0,0,0,0.6)",
          }}
          onClick={togglePause}
        >
          <MatIcon value={paused ? "play_arrow" : "pause"} sx={{ color: "white" }} />
        </IconButton>
      </Maybe>
    </>
  );
};

const Navigation = ({
  current,
  infiniteSlide,
  slides,
  orientation,
  navigatorRootClass = "",
  navigatorClass = "",
  currentSlide,
  renderNavigation,
  prevSlide,
  nextSlide,
}: NavigationProps): JSX.Element => {
  const buttonStyle = {
    position: "absolute",
    top: orientation === "horizontal" ? "50%" : "unset",
    transform: "translateY(-50%)",
    zIndex: 1,
  };

  const renderNavigationButton = (direction: GenericCarouseNavigationDirection): JSX.Element => {
    const isPrev = direction === "left" || direction === "up";
    const disabled = !infiniteSlide && ((isPrev && current === 0) || (!isPrev && current === slides.length - 1));

    const renderNavigationContent = (): JSX.Element => {
      const isHorizontal = orientation === "horizontal";

      if (renderNavigation) {
        return renderNavigation(slides[current].data, currentSlide, direction);
      }

      if (isHorizontal) {
        return isPrev ? (
          <MatIcon value="navigate_before" variant="round" size={50} sx={{ color: "white" }} />
        ) : (
          <MatIcon value="navigate_next" variant="round" size={50} sx={{ color: "white" }} />
        );
      } else {
        return isPrev ? (
          <MatIcon value="expand_less" variant="round" size={50} sx={{ color: "white" }} />
        ) : (
          <MatIcon value="expand_more" variant="round" size={50} sx={{ color: "white" }} />
        );
      }
    };

    return (
      <Maybe condition={!disabled}>
        <Button
          className={navigatorClass}
          onClick={isPrev ? prevSlide : nextSlide}
          sx={{
            ...buttonStyle,
            width: orientation === "horizontal" ? "initial" : "100%",
            height: orientation === "horizontal" ? "100%" : "initial",
            borderRadius: 1,
            right: !isPrev && orientation === "horizontal" ? 0 : "initial",
            top: isPrev && orientation === "vertical" ? 30 : buttonStyle.top,
          }}
        >
          {renderNavigationContent()}
        </Button>
      </Maybe>
    );
  };

  return (
    <Box className={navigatorRootClass}>
      {renderNavigationButton(orientation === "horizontal" ? "left" : "up")}
      {renderNavigationButton(orientation === "horizontal" ? "right" : "down")}
    </Box>
  );
};

const Indication = ({
  current,
  slides,
  indicatorRootClass = "",
  indicatorClass = "",
  activeIndicatorClass = "",
  renderIndicator,
  goToSlide,
}: IndicationProps): JSX.Element => {
  return (
    <Stack className={indicatorRootClass} direction="row" gap={0.2} justifyContent="center" alignItems="center">
      {slides.map((slide, index) => (
        // Indicator
        <Box
          key={slide.key}
          component={ButtonBase}
          className={`${getClass(indicatorClass, slide.data)}${index === current ? `${getClass(activeIndicatorClass, slide.data)}` : ""}`}
          sx={{
            borderRadius: 1,
            cursor: "pointer",
          }}
          onClick={() => goToSlide(index)}
        >
          <Maybe condition={!!renderIndicator}>{renderIndicator?.(slide.data, slide.key)}</Maybe>
          <Maybe condition={!renderIndicator}>
            <MatIcon value="circle" variant="round" sx={{ color: index === current ? "white" : "darkgray" }} />
          </Maybe>
        </Box>
      ))}
    </Stack>
  );
};

const GenericCarousel = ({
  auto,
  swipeable,
  affordance,
  pauseable,
  navigate = true,
  indicate = true,
  infiniteSlide = !!auto || false,
  slides,
  value,
  orientation = "horizontal",
  animation,
  classes = {},
  renderSlide,
  renderNavigation,
  renderIndicator,
  onSlideChange,
}: GenericCarouselProps): JSX.Element => {
  const {
    root: r = "",
    sliderRoot: src = "",
    slider: sc = "",
    activeSlider: asc = "",
    playPause: pp = "",
    navigatorRoot: nr = "",
    navigator: n = "",
    indicatorRoot: ir = "",
    indicator: i = "",
    activeIndicator: ai = "",
  } = classes;
  const carouselRef = useRef<HTMLDivElement>(null);

  const [direction, setDirection] = useState(1);
  const [isPaused, setIsPaused] = useState(false);
  const [currentSlide, setCurrentSlide] = useState<GenericCarouselKey>(value);

  const current = useMemo(() => slides.findIndex((slide) => slide.key === currentSlide), [currentSlide, slides]);

  const togglePause = (): void => setIsPaused((prev) => !prev);

  const goToSlide = useCallback(
    (index: number) => {
      const newIndex = infiniteSlide
        ? (index + slides.length) % slides.length
        : Math.max(0, Math.min(index, slides.length - 1));

      setDirection(current > newIndex ? -1 : 1);

      const newSlide = slides[newIndex].key;
      setCurrentSlide(newSlide);
      onSlideChange?.(newSlide);
    },
    [current, infiniteSlide, onSlideChange, slides],
  );

  const prevSlide = useCallback(() => goToSlide(current - 1), [current, goToSlide]);
  const nextSlide = useCallback(() => goToSlide(current + 1), [current, goToSlide]);

  useEffect(() => {
    if (!auto || slides.length <= 1 || isPaused) return;

    const intervalTime = typeof auto === "number" ? auto : 3000;
    const intervalId = setInterval(() => nextSlide(), intervalTime);

    return (): void => clearInterval(intervalId);
  }, [auto, slides.length, isPaused, nextSlide]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      if (
        (event.key === "ArrowLeft" && orientation === "horizontal") ||
        (event.key === "ArrowUp" && orientation === "vertical")
      ) {
        prevSlide();
      } else if (
        (event.key === "ArrowRight" && orientation === "horizontal") ||
        (event.key === "ArrowDown" && orientation === "vertical")
      ) {
        nextSlide();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return (): void => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [nextSlide, orientation, prevSlide]);

  const handleMouseEnter = (): void => setIsPaused(true);
  const handleMouseLeave = (): void => setIsPaused(false);
  const handleDragEnd = (_: MouseEvent | TouchEvent | PointerEvent, info: PanInfo): void => {
    const threshold = typeof swipeable === "boolean" ? 50 : swipeable?.threshold;
    const velocity = typeof swipeable === "boolean" || !swipeable?.velocity ? 0.5 : swipeable?.velocity;

    if (orientation === "horizontal") {
      if (info.offset.x > threshold || info.velocity.x > velocity) prevSlide();
      else if (info.offset.x < -threshold || info.velocity.x < -velocity) nextSlide();
    } else {
      if (info.offset.y > threshold || info.velocity.y > velocity) prevSlide();
      else if (info.offset.y < -threshold || info.velocity.y < -velocity) nextSlide();
    }
  };

  return (
    <Stack ref={carouselRef} position="relative" overflow="hidden" width="100%" height="100%" className={r}>
      <motion.div
        custom={direction}
        className={src}
        drag={swipeable ? (orientation === "horizontal" ? "x" : "y") : false}
        dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
        dragElastic={1}
        onDragEnd={handleDragEnd}
        style={{ position: "relative", width: "100%", height: "100%" }}
      >
        <Slider
          current={current}
          direction={direction}
          animation={animation}
          pauseable={pauseable}
          paused={isPaused}
          affordance={affordance}
          swipeable={swipeable}
          slides={slides}
          orientation={orientation}
          sliderClass={sc}
          activeSliderClass={asc}
          playPauseClass={pp}
          togglePause={togglePause}
          renderSlide={renderSlide}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onDragEnd={handleDragEnd}
        />
      </motion.div>

      <Maybe condition={navigate && slides?.length > 1}>
        <Navigation
          current={current}
          infiniteSlide={infiniteSlide}
          slides={slides}
          orientation={orientation}
          navigatorRootClass={nr}
          navigatorClass={n}
          currentSlide={currentSlide}
          renderNavigation={renderNavigation}
          prevSlide={prevSlide}
          nextSlide={nextSlide}
        />
      </Maybe>

      <Maybe condition={indicate && orientation === "horizontal"}>
        <Indication
          current={current}
          slides={slides}
          indicatorRootClass={ir}
          indicatorClass={i}
          activeIndicatorClass={ai}
          renderIndicator={renderIndicator}
          goToSlide={goToSlide}
        />
      </Maybe>
    </Stack>
  );
};

export default GenericCarousel;
