import decimalFormat from "@/utils/formatters/decimal-format";
import Slider from "@mui/material/Slider";

interface RangeSliderProps {
  value: number;
  min?: number;
  max: number;
  disabled?: boolean;
}

export default function RangeSlider({ value, min = 0, max, disabled = false }: RangeSliderProps): JSX.Element {
  const marks = [
    {
      value: min,
      label: `${decimalFormat(min)}%`,
    },
    {
      value: max,
      label: `${decimalFormat(max)}%`,
    },
  ];

  return (
    <Slider
      value={value}
      min={min}
      max={max}
      disabled={disabled}
      marks={marks}
      valueLabelDisplay="on"
      valueLabelFormat={(value) => <div>{decimalFormat(value)}%</div>}
      sx={{
        width: "95%",
        color: "success.main",
        "&.Mui-disabled .MuiSlider-track": {
          color: "green",
        },
        "&.Mui-disabled .MuiSlider-rail": {
          color: "green",
        },
        "&.Mui-disabled .MuiSlider-thumb": {
          color: "green",
          height: "15px",
          width: "15px",
        },
        "&.Mui-disabled .MuiSlider-markLabelActive": {
          fontSize: "14px",
        },
        "&.Mui-disabled .MuiSlider-markLabel": {
          fontSize: "14px",
        },
        "&.Mui-disabled .MuiSlider-valueLabel": {
          fontSize: "13px",
          height: "21px",
          width: "auto",
          backgroundColor: "gray",
          textAlign: "center",
          paddingRight: "10px",
        },
        "&.MuiSlider-mark": {
          backgroundColor: "green",
          height: 8,
          width: 2,
          "&.MuiSlider-markActive": {
            opacity: 1,
            backgroundColor: "green",
          },
        },
      }}
    />
  );
}
