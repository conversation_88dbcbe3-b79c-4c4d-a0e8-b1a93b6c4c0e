import Link from "next/link";
import { I<PERSON><PERSON><PERSON>on, Tooltip } from "@mui/material";
import DetailsIcon from "@mui/icons-material/Info";
import useNavigation from "@/hooks/use-navigation";
import { SyntheticEvent } from "react";

interface InfoButtonProps {
  id: string;
}

export default function InfoButton(props: InfoButtonProps): JSX.Element {
  const { extendPathWithSegments } = useNavigation();
  const { id } = props;

  return (
    <Link
      onClick={(event: SyntheticEvent) => {
        event?.stopPropagation();
      }}
      href={extendPathWithSegments(id)}
    >
      <Tooltip title="View Details" enterDelay={750} enterNextDelay={250}>
        <IconButton aria-label="expand row" size="small">
          <DetailsIcon />
        </IconButton>
      </Tooltip>
    </Link>
  );
}
