import { Button, Dialog, <PERSON>alogA<PERSON>, <PERSON>alogContent, <PERSON>alogT<PERSON>le, IconButton, Stack } from "@mui/material";
import { ElementType, FormEventHandler, MouseEvent, PropsWithChildren, ReactNode, useEffect, useMemo } from "react";
import { LoadingButton, LoadingButtonProps } from "@mui/lab";
import { isNothing, Maybe, Nullable, px } from "@rubiconcarbon/frontend-shared";

import classes from "./styles/generic-dialog.module.scss";

type GenericDialogActionProps = {
  buttonText: string;
  actionType?: "positive" | "negative";
} & Omit<LoadingButtonProps, "children">;

/**
 * **open** | _boolean_: Signifies if the dialog should be opened or not
 *
 * **fullscreen** | _boolean_: Signifies if the dialog should take the whole screen or not
 *
 * **dividers** | _boolean_: Signifies if the dialog should have a thick divider separating the content section and the title and the actions
 *
 * **dismissable** | _boolean_: Signifies if the dialog can be dismissed by either just clicking the backdrop or hitting ESC on your keyboard
 *
 * **mountInParent** | _boolean_: Signifies if the dialog will be mounted in the dialog presentation container or not
 *
 * **title** | _ReactNode_: Title content
 *
 * **dismissText** | _ReactNode_: Text content for close button in the header section
 *
 * **dismissIcon** | _ReactNode_: Icon content for close button in the header section
 *
 * **positiveAction** | _boolean_ or _GenericDialogActionProps_: Positive action button in the actions section
 * > Can either be a boolean or a GenericDialogActionProps.
 *
 * > If it is a **boolean**, it resolves to text content of **"OK"**.
 * If **onPositiveClick** is defined, it will use that as the handler when clicked.
 *
 * > If it is a **GenericDialogActionProps**, it extends the **ButtonProps** so you can define anything you would define on a Button **_expect the children prop_**.
 * It has a **buttonText** prop to set the content of the button.
 *
 * **negativeAction** | _boolean_ or _GenericDialogActionProps_: Negative action button in the actions section
 * > Can either be a boolean or a GenericDialogActionProps.
 *
 * > If it is a **boolean**, it resolves to text content of **"CANCEL"**.
 * If **onNegativeClick** is defined, it will use that as the handler when clicked.
 *
 * > If it is a **GenericDialogActionProps**, it extends the **ButtonProps** so you can define anything you would define on a Button **_expect the children prop_**.
 * It has a **buttonText** prop to set the content of the button.
 *
 * **customActions** | _GenericDialogActionProps[]_: Array of custom actions. This **_cannnot_** be used if upi have already set either **positiveAction** or **negativeAction** or both.
 *
 * > It extends the **ButtonProps** so you can define anything you would define on a Button **_expect the children prop_**.
 * It has a **buttonText** prop to set the content of the button.
 *
 * > It will be set in the order it appears in the array.
 *
 * **actionAutoFocusIndex** | _boolean_: Used to auto focus on an action button based on index. The index set will also have the first tabbable index before any other button in the dialog.
 * > There is an issue where it does not work on some browsers so set the autoFocus property on the _GenericDialogActionProps_ if you want it to work 100% of the time.
 *
 * **classes** | _object_: Object for setting different classNames to be used on different section of the dialog
 *
 * **onSubmit** | _(event: object) => void_: Callback to submit dialog if it is a form
 *
 * **onClose** | _(event: object, reason: string) => void_: Callback to dismiss dialog
 *
 * **onPositiveClick** | _() => void_: Callback to trigger a positive action. If defined, it will be used when **positiveAction** is defined either as a __boolean__ or a __GenericDialogActionProps__.
 *
 * **onNegativeClick** | _() => void_: Callback to trigger a negative action. If defined, it will be used when **negativeAction** is defined either as a __boolean__ or a __GenericDialogActionProps__.
 */
type GenericDialogProps = {
  open: boolean;
  fullscreen?: boolean;
  id?: string;
  component?: ElementType<any>;
  dividers?: boolean;
  dismissable?: boolean;
  mountInParent?: boolean;
  title?: ReactNode;
  dismissText?: ReactNode;
  dismissIcon?: ReactNode;
  positiveAction?: GenericDialogActionProps | boolean;
  negativeAction?: GenericDialogActionProps | boolean;
  customActions?: GenericDialogActionProps[] | ReactNode;
  actionAutoFocusIndex?: number;
  classes?: {
    root?: string;
    title?: string;
    dividers?: string;
    content?: string;
    actions?: string;
  };
  onSubmit?: FormEventHandler<HTMLFormElement>;
  onClose: (event: object, reason: string) => void;
  onPositiveClick?: () => void;
  onNegativeClick?: () => void;
};

const GenericDialogActionButton = ({
  autoFocus = false,
  buttonText,
  startIcon,
  endIcon,
  ...rest
}: GenericDialogActionProps): JSX.Element => {
  return (
    <LoadingButton
      {...{
        ...(startIcon ? { startIcon } : {}),
        ...(endIcon ? { endIcon } : {}),
        tabIndex: autoFocus ? 1 : 2,
      }}
      {...rest}
    >
      {buttonText}
    </LoadingButton>
  );
};

const GenericDialog = (props: PropsWithChildren<GenericDialogProps>): JSX.Element => {
  const {
    open,
    fullscreen: fullScreen,
    id,
    component = "div",
    dividers,
    dismissable = true,
    mountInParent = false,
    title,
    dismissText,
    dismissIcon,
    children,
    positiveAction,
    negativeAction,
    customActions,
    actionAutoFocusIndex,
    classes: classNames,
    onSubmit,
    onClose,
    onPositiveClick,
    onNegativeClick,
  } = props;
  const { root = "", title: titleClass = "", dividers: dividersClass, content = "", actions = "" } = classNames || {};

  const invalidDialogProps = useMemo(
    () => (positiveAction || negativeAction) && !!customActions,
    [customActions, negativeAction, positiveAction],
  );
  const hasHeading = useMemo(() => !!title || !!dismissText || !!dismissIcon, [dismissIcon, dismissText, title]);
  const hasAction = useMemo(
    () =>
      !!positiveAction ||
      !!negativeAction ||
      (Array.isArray(customActions) && !!customActions?.length) ||
      !isNothing(customActions),
    [customActions, negativeAction, positiveAction],
  );

  const negativeActionData = useMemo((): Nullable<GenericDialogActionProps> => {
    const className = classes.Negative;
    const autoFocus = typeof actionAutoFocusIndex === "number" && !!positiveAction ? actionAutoFocusIndex === 0 : true;

    if (negativeAction) {
      if (typeof negativeAction === "boolean") {
        const data: GenericDialogActionProps = {
          buttonText: "CANCEL",
          className,
          autoFocus,
          onClick: onNegativeClick || ((): void => {}),
        };

        return data;
      } else {
        const { className: negativeClass, onClick: userOnClick, ...rest } = negativeAction;
        const data: GenericDialogActionProps = {
          ...rest,
          className: `${className}${negativeClass ? ` ${negativeClass}` : ""}`,
          autoFocus,
          onClick: (event: MouseEvent<HTMLButtonElement>) => {
            if (userOnClick) {
              userOnClick(event);
            }
            if (onNegativeClick) {
              onNegativeClick();
            }
          },
        };

        return data;
      }
    }
    return null;
  }, [actionAutoFocusIndex, negativeAction, onNegativeClick, positiveAction]);

  const positiveActionData = useMemo((): Nullable<GenericDialogActionProps> => {
    const className = classes.Positive;
    const autoFocus = typeof actionAutoFocusIndex === "number" && !!negativeAction ? actionAutoFocusIndex === 0 : true;

    if (positiveAction) {
      if (typeof positiveAction === "boolean") {
        const data: GenericDialogActionProps = {
          buttonText: "OK",
          className,
          autoFocus,
          onClick: onPositiveClick || ((): void => {}),
        };

        return data;
      } else {
        const { className: positiveClass, onClick: userOnClick, ...rest } = positiveAction;
        const data: GenericDialogActionProps = {
          ...rest,
          className: `${className}${positiveClass ? ` ${positiveClass}` : ""}`,
          autoFocus,
          onClick: (event: MouseEvent<HTMLButtonElement>) => {
            if (userOnClick) {
              userOnClick(event);
            }
            if (onPositiveClick) {
              onPositiveClick();
            }
          },
        };

        return data;
      }
    }
    return null;
  }, [actionAutoFocusIndex, negativeAction, onPositiveClick, positiveAction]);

  useEffect(() => {
    if (invalidDialogProps)
      console.error(
        `'GenericDialog' cannot have both ('postiveAction' and/or 'negativeAction') and 'customActions'.\n Either use ('postiveAction' and/or 'negativeAction') or 'customActions'.`,
      );
  }, [invalidDialogProps]);

  const handleClose = (event: object, reason: string): void => {
    if (!dismissable && reason === "backdropClick") return;
    onClose(event, reason);
  };

  return (
    <Maybe condition={!invalidDialogProps && open}>
      <Dialog
        open={open}
        fullScreen={fullScreen}
        id={id}
        component={component}
        disableEscapeKeyDown={!dismissable}
        disablePortal={mountInParent}
        onClose={handleClose}
        classes={{
          paper: `${classes.Dialog} ${root}`,
        }}
        {...(px({
          onSubmit,
        }),
        [undefined, null])}
      >
        <Maybe condition={hasHeading}>
          <Stack
            className={`${classes.Title} ${titleClass}`}
            direction="row"
            justifyContent={title ? "space-between" : "flex-end"}
            alignContent="center"
          >
            <Maybe condition={!!title}>
              <DialogTitle
                classes={{
                  root: classes.MuiTitle,
                }}
              >
                {title}
              </DialogTitle>
            </Maybe>
            <Maybe condition={!!dismissText && !!dismissIcon}>
              <Button
                startIcon={dismissIcon}
                variant="text"
                onClick={(event: MouseEvent<HTMLButtonElement>) => handleClose(event, "closeButtonClick")}
              >
                {dismissText}
              </Button>
            </Maybe>
            <Maybe condition={!(!!dismissText && !!dismissIcon) && (!!dismissText || !!dismissIcon)}>
              <Maybe condition={!!dismissText}>
                <Button onClick={(event: MouseEvent<HTMLButtonElement>) => handleClose(event, "closeButtonClick")}>
                  {dismissText}
                </Button>
              </Maybe>
              <Maybe condition={!!dismissIcon}>
                <IconButton onClick={(event: MouseEvent<HTMLButtonElement>) => handleClose(event, "closeButtonClick")}>
                  {dismissIcon}
                </IconButton>
              </Maybe>
            </Maybe>
          </Stack>
        </Maybe>
        <DialogContent
          dividers={dividers}
          classes={{
            root: `${classes.Content} ${content}`,
            dividers: `${classes.Divider} ${dividersClass}`,
          }}
        >
          {children}
        </DialogContent>
        <Maybe condition={hasAction}>
          <DialogActions
            classes={{
              root: `${classes.Actions} ${actions}`,
            }}
          >
            <Maybe condition={!!positiveAction || !!negativeAction}>
              <Stack sx={{ width: "100%" }}>
                <Stack direction="row" sx={{ justifyContent: "space-between" }} alignContent="center" gap={0.5}>
                  <Maybe condition={!!negativeAction}>
                    <GenericDialogActionButton {...negativeActionData!} />
                  </Maybe>
                  <Maybe condition={!!positiveAction}>
                    <GenericDialogActionButton {...positiveActionData!} />
                  </Maybe>
                </Stack>
              </Stack>
            </Maybe>
            <Maybe condition={!isNothing(customActions) && !Array.isArray(customActions)}>
              {() => customActions as ReactNode}
            </Maybe>
            <Maybe condition={!isNothing(customActions) && Array.isArray(customActions) && !!customActions?.length}>
              {() => (
                <Stack direction="row" alignContent="center" gap={0.5}>
                  {(customActions as GenericDialogActionProps[])?.map(
                    ({ actionType, className, ...restAction }, index) => (
                      <GenericDialogActionButton
                        key={index}
                        {...{
                          ...restAction,
                          ...(actionType === "positive"
                            ? { className: `${classes.Positive}${className ? ` ${className}` : ""}` }
                            : actionType === "negative"
                              ? { className: `${classes.Negative}${className ? ` ${className}` : ""}` }
                              : {}),
                          autoFocus: typeof actionAutoFocusIndex === "number" && actionAutoFocusIndex === index,
                        }}
                      />
                    ),
                  )}
                </Stack>
              )}
            </Maybe>
          </DialogActions>
        </Maybe>
      </Dialog>
    </Maybe>
  );
};

export default GenericDialog;
