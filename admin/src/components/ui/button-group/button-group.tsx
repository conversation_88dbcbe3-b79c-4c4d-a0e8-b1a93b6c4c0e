import { Box, Link, Tooltip, Typography } from "@mui/material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { AuthContext } from "@providers/auth-provider";
import { useContext } from "react";

const disabledButtonStyle = {
  fontWeight: 600,
  color: "gray",
  textTransform: "uppercase",
  padding: ".5rem",
  whiteSpace: "nowrap",
  fontSize: "12px",
};

const buttonNormalStyle = {
  fontWeight: 600,
  color: "#094436",
  textTransform: "uppercase",
  padding: ".5rem",
  whiteSpace: "nowrap",
  fontSize: "12px",
};

export interface buttonItem {
  id: string;
  name: string;
  tooltip: string;
  isDisabled?: boolean;
  requiredPermission?: PermissionEnum;
  handler: () => void;
}

interface ButtonGroupProps {
  buttons: buttonItem[];
}

export default function ButtonGroup({ buttons }: ButtonGroupProps): JSX.Element {
  const { user: loginUser } = useContext(AuthContext);
  return (
    <Box display="flex" justifyContent="left" sx={{ width: "100%" }}>
      {buttons.map((button, idx) => {
        return (
          <Box
            key={button.id + idx}
            height="36"
            sx={{
              borderColor: "rgba(9, 68, 54, 0.5)",
              borderLeft: idx === 0 ? 0 : 1,
            }}
          >
            {button.isDisabled || !loginUser.hasPermission(button.requiredPermission) ? (
              <Tooltip
                key={button.name + idx}
                title={
                  <div>
                    Insufficient permissions.
                    <br />
                    <NAME_EMAIL>.
                  </div>
                }
                placement="bottom"
              >
                <Typography variant="body2" component="span" ml={0} sx={disabledButtonStyle}>
                  {button.name}
                </Typography>
              </Tooltip>
            ) : (
              <Tooltip key={button.name + idx} title={button.tooltip} placement="bottom">
                <Link
                  component="button"
                  variant="body2"
                  onClick={button.handler}
                  sx={{
                    textDecoration: "none",
                    "&:hover": {
                      cursor: "pointer",
                      boxShadow: 1,
                    },
                  }}
                >
                  <Typography variant="body2" component="span" ml={0} sx={buttonNormalStyle}>
                    {button.name}
                  </Typography>
                </Link>
              </Tooltip>
            )}
          </Box>
        );
      })}
    </Box>
  );
}
