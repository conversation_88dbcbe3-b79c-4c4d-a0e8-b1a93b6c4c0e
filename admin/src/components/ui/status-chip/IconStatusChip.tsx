import Chip from "@mui/material/Chip";
import CheckIcon from "@mui/icons-material/Check";
import ClearIcon from "@mui/icons-material/Clear";
import { Maybe } from "@rubiconcarbon/frontend-shared";

const successStyle = {
  backgroundColor: "rgba(237, 247, 237, 1)",
  padding: "10px",
  height: "32px",
  color: "rgba(7, 125, 85, 1)",
  fontWeight: 400,
};

const unSuccessfullStyle = {
  backgroundColor: "rgba(253, 237, 237, 1)",
  padding: "10px",
  height: "32px",
  color: "rgba(211, 47, 47, 1)",
  fontWeight: 400,
};

interface StatusChipProps {
  isSuccess: string | boolean;
  isShowIcon?: boolean;
  label: string;
}

export default function IconStatusChip({ isSuccess, label, isShowIcon = true }: StatusChipProps): JSX.Element {
  return (
    <Chip
      label={label}
      sx={isSuccess ? successStyle : unSuccessfullStyle}
      icon={
        <Maybe condition={isShowIcon}>
          {isSuccess ? (
            <CheckIcon sx={{ fontSize: 20, color: "rgba(7, 125, 85, 1) !important" }} />
          ) : (
            <ClearIcon sx={{ fontSize: 20, color: "rgba(211, 47, 47, 1) !important" }} />
          )}
        </Maybe>
      }
    />
  );
}
