import Chip from "@mui/material/Chip";
import { RetirementStatus, UserStatus } from "@rubiconcarbon/shared-types";
import StatusChipColors from "./statusChipColors";
import { CSSProperties } from "react";
import { RetirementStatusToLabel } from "@/mappers/transaction-status-mapper";
import COLORS from "@components/ui/theme/colors";
import { GeneralStatusMapper } from "@/mappers/general-status-mapper";
import { TransactionStatus, TransactionStatusToLabel } from "@constants/transaction-status";

interface StatusChipProps {
  status: string | boolean;
}

const colorMap: Record<string, StatusChipColors> = {
  [RetirementStatus.COMPLETED]: StatusChipColors.SUCCESS,
  [RetirementStatus.CANCELED]: StatusChipColors.ERROR,
  [RetirementStatus.PROCESSING]: StatusChipColors.WARNING,
  [RetirementStatus.ADMIN_REVIEW]: StatusChipColors.INFO,
  [TransactionStatus.FIRM]: StatusChipColors.INFO,
  [TransactionStatus.INDICATIVE]: StatusChipColors.WARNING,
  [TransactionStatus.BINDING]: StatusChipColors.WARNING,
  [TransactionStatus.SETTLED]: StatusChipColors.SUCCESS,
  [UserStatus.ENABLED]: StatusChipColors.SUCCESS,
  [UserStatus.DISABLED]: StatusChipColors.ERROR,
  true: StatusChipColors.SUCCESS,
  false: StatusChipColors.ERROR,
};

const styleMap: Record<string, CSSProperties> = {
  [RetirementStatus.PORTFOLIO_MANAGER_REVIEW]: { backgroundColor: COLORS.lightBlue },
  [TransactionStatus.EXECUTED]: { backgroundColor: COLORS.lightBlue },
  [TransactionStatus.PAID]: { backgroundColor: COLORS.lightBlue },
  [TransactionStatus.DELIVERED]: { backgroundColor: COLORS.lightBlue },
};

const generateColorFromStatus = (status: string): StatusChipColors => colorMap[status] || StatusChipColors.DEFAULT;

const generateStyleFromStatus = (status: string): CSSProperties => styleMap[status] || {};

const generateLabelFromStatus = (status: string | boolean): string => {
  return (
    TransactionStatusToLabel[status?.toString()] ??
    RetirementStatusToLabel[status?.toString()] ??
    GeneralStatusMapper[status?.toString()] ??
    status
  );
};

export default function StatusChip({ status }: StatusChipProps): JSX.Element {
  return (
    <Chip
      role="status"
      label={generateLabelFromStatus(status)}
      color={generateColorFromStatus(status.toString())}
      style={generateStyleFromStatus(status.toString())}
      size="small"
    />
  );
}
