import React from "react";
import { Stack, Typography, Tooltip, Box, SxProps } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import DetailsIcon from "@mui/icons-material/Info";

import classes from "./styles.module.scss";

export type item = {
  label: string;
  value: JSX.Element | string;
  supportingLabel?: string;
  class?: string;
  tooltip?: string;
};

export type groupStyle = {
  containerStyle?: SxProps;
  titleStyle?: SxProps;
  itemStyle?: SxProps;
  valueStyle?: SxProps;
  supportingValueStyle?: SxProps;
};

export type itemGroup = {
  groupTitle?: string;
  actions?: JSX.Element;
  items: item[];
  groupStyle?: groupStyle;
};

const defaultTitleStyle = {
  fontWeight: "400",
  fontSize: "24px",
  color: "rgba(0, 0, 0, 0.87)",
};

const defaultContainerStyle = {
  padding: "20px",
  backgroundColor: "white",
};

export default function SummaryGroup(props: { group: itemGroup }): JSX.Element {
  const { group } = props;
  const { titleStyle, itemStyle, valueStyle, containerStyle, supportingValueStyle } = group.groupStyle || {};

  return (
    <Stack direction="column">
      <Box sx={{ ...defaultContainerStyle, ...containerStyle }}>
        <Stack direction="row" gap={2}>
          <Typography variant="body1" component="h5" sx={{ ...defaultTitleStyle, ...titleStyle }}>
            {group.groupTitle}
          </Typography>
          {group.actions}
        </Stack>
        <Stack direction="row" gap={10} mt={group?.groupTitle ? 3 : 0}>
          {group?.items?.map((item) => (
            <Box key={item.label}>
              <Stack direction="column" gap={"8px"}>
                <Stack direction="row">
                  <Typography variant="body1" className={classes.summaryCompTitle} sx={itemStyle}>
                    {item.label}
                  </Typography>
                  <Maybe condition={!!item.tooltip}>
                    <Tooltip placement="top" title={item.tooltip}>
                      <DetailsIcon sx={{ height: "18px", marginLeft: "5px" }} />
                    </Tooltip>
                  </Maybe>
                </Stack>
                <Typography
                  variant="body1"
                  component="div"
                  className={`${classes.summaryCompValue} ${classes[item.class]}`}
                  sx={valueStyle}
                >
                  {item.value}
                </Typography>
                <Maybe condition={!!item?.supportingLabel}>
                  <Typography variant="body2" sx={supportingValueStyle}>
                    {item.supportingLabel}
                  </Typography>
                </Maybe>
              </Stack>
            </Box>
          ))}
        </Stack>
      </Box>
    </Stack>
  );
}
