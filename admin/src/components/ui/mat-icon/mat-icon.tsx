import { Icon, SxProps } from "@mui/material";
import { classcat } from "@rubiconcarbon/frontend-shared";

export type MatIconVariant = "outlined" | "round" | "sharp" | "two-tone";
export type MatIconColor =
  | "disabled"
  | "action"
  | "primary"
  | "inherit"
  | "secondary"
  | "error"
  | "info"
  | "success"
  | "warning";

type MatIconProps = {
  value: string;
  variant?: MatIconVariant;
  color?: MatIconColor;
  size?: number;
  sx?: SxProps;
  className?: string;
};

const MatIcon = ({
  value,
  variant,
  color = "primary",
  size = 30,
  sx = {},
  className = "",
}: MatIconProps): JSX.Element => (
  <Icon
    className={classcat([`material-icons${variant ? `-${variant}` : ""}`, { [className]: !!className }])}
    color={color}
    sx={{ fontSize: `${size}px !important`, ...sx }}
  >
    {value}
  </Icon>
);

export default MatIcon;
