import { PropsWithChildren } from "react";
import { Button, Tooltip, SxProps, Box } from "@mui/material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { AuthContext } from "@providers/auth-provider";
import { useContext } from "react";
import { MISSING_PERMISSIONS } from "@constants/constants";
import COLORS from "../theme/colors";

export type Variant = "contained" | "text" | "outlined";
export type ButtonColor = "inherit" | "error" | "primary" | "secondary" | "success" | "info" | "warning";

interface CustomButtonProps {
  variant?: Variant;
  onClickHandler: (event: object) => void;
  isDisabled?: boolean;
  requiredPermission?: PermissionEnum;
  tooltip?: string;
  style?: SxProps;
  color?: ButtonColor;
}

const buttonBaseStyle = {
  px: 2.5,
  color: COLORS.white,
  padding: "16px",
};

export default function CustomButton({
  children,
  tooltip,
  isDisabled,
  requiredPermission,
  variant,
  onClickHandler,
  style,
  color,
}: PropsWithChildren<CustomButtonProps>): JSX.Element {
  const { user: loginUser } = useContext(AuthContext);
  const tooltipText =
    requiredPermission && !loginUser.hasPermission(requiredPermission) ? MISSING_PERMISSIONS : tooltip;
  return (
    <Tooltip title={tooltipText} placement="top">
      <Box sx={{ display: "inline-block" }}>
        <Button
          disabled={isDisabled || (requiredPermission ? !loginUser.hasPermission(requiredPermission) : false)}
          variant={variant ?? "contained"}
          onClick={onClickHandler}
          sx={{ ...buttonBaseStyle, ...style }}
          color={color}
        >
          {children}
        </Button>
      </Box>
    </Tooltip>
  );
}
