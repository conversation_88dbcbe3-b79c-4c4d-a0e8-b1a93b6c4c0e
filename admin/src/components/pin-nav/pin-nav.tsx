import { IconButton } from "@mui/material";
import MatIcon from "../ui/mat-icon/mat-icon";
import { useStoreProvider } from "@providers/store-provider";
import { classcat } from "@rubiconcarbon/frontend-shared";

import classes from "./styles.module.scss";

const PinNav = (): JSX.Element => {
  const { localState, updateLocalState } = useStoreProvider();
  const { navigation } = localState;
  const { pinned = false, forceCollapse = false } = navigation || {};

  const togglePinned = (): void => {
    updateLocalState("navigation.pinned", !pinned, {
      sideEffect: ({ local }) => {
        updateLocalState("navigation.forceCollapse", !local.navigation?.pinned);

        if (local.navigation.pinned) updateLocalState("navigation.expanded", false);
      },
    });
  };

  return (
    <IconButton
      className={classcat([classes.PinNav, { [classes.Pinned]: !forceCollapse && pinned }])}
      onClick={togglePinned}
    >
      <MatIcon className={classes.Icon} value="push_pin" variant="outlined" size={16} />
    </IconButton>
  );
};

export default PinNav;
