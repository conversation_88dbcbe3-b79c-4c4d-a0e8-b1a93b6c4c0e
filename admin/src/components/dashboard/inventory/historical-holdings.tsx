import { EChart } from "@hcorta/react-echarts";

import useS<PERSON> from "swr";
import Card from "@components/dashboard/card";
import { useContext, useMemo } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { Data } from "@components/dashboard/interfaces";
import { MaterialReactTable, type MRT_ColumnDef } from "material-react-table";

interface HistoricalHoldingsType {
  timestamp: number;
  purchased: number;
  retired: number;
}

export default function HoldingsOverTime(): JSX.Element {
  const { reportingFetcher } = useContext(AxiosContext);
  const {
    data: response,
    error,
    isLoading,
  } = useSWR<Data<HistoricalHoldingsType[]>>("holdings-history", {
    fetcher: reportingFetcher as any,
    revalidateOnFocus: false,
  });

  const columns = useMemo<MRT_ColumnDef<HistoricalHoldingsType>[]>(
    () => [
      {
        accessorKey: "timestamp",
        header: "Date",
        Cell: ({ cell }): JSX.Element => <>{new Date(cell.getValue<number>()).toDateString()}</>,
      },
      { accessorKey: "purchased", header: "Amount Purchased to Date" },
      { accessorKey: "retired", header: "Amount Retired to Date" },
      { accessorFn: (row): number => row.purchased - row.retired, header: "Net Amount" },
    ],
    [],
  );

  return (
    <Card
      title="Holdings Over Time"
      loading={isLoading}
      error={error}
      height={"550px"}
      chart={
        <EChart
          style={{ height: "100%" }}
          tooltip={{
            trigger: "axis",
            axisPointer: {
              type: "cross",
              label: {
                backgroundColor: "#6a7985",
              },
            },
          }}
          legend={{
            data: ["Purchased", "Retired", "Available"],
            right: "5%",
          }}
          grid={{
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          }}
          animation={true}
          xAxis={[
            {
              type: "time",
            },
          ]}
          yAxis={[
            {
              type: "value",
            },
          ]}
          dataset={{
            source: response?.data.map((x: HistoricalHoldingsType) => ({
              ...x,
              retired: -x.retired,
              net: x.purchased - x.retired,
            })),
          }}
          series={[
            {
              name: "Purchased",
              type: "line",
              areaStyle: {},
              emphasis: {
                focus: "series",
              },
              encode: {
                x: "timestamp",
                y: "purchased",
              },
            },
            {
              name: "Retired",
              type: "line",
              areaStyle: {},
              emphasis: {
                focus: "series",
              },
              encode: {
                x: "timestamp",
                y: "retired",
              },
            },
            {
              name: "Available",
              type: "line",
              encode: {
                x: "timestamp",
                y: "net",
              },
            },
          ]}
        />
      }
      table={
        <MaterialReactTable
          columns={columns}
          data={response?.data}
          enableColumnActions={false}
          enableColumnFilters={false}
          enablePagination={true}
          enableSorting={true}
          enableBottomToolbar={true}
          enableTopToolbar={false}
          muiTableContainerProps={{ sx: { boxShadow: "none", maxHeight: "340px" } }}
          muiTableProps={{
            sx: {
              padding: "10px",
              boxShadow: "none",
              border: "0px",
              width: "100%",
            },
          }}
          muiTablePaperProps={{
            sx: {
              boxShadow: "none",
              width: "100%",
              height: "100%",
              padding: "0 5px",

              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
            },
          }}
          muiTableHeadRowProps={{ sx: { boxShadow: "none", border: "0px" } }}
        ></MaterialReactTable>
      }
      csv={() => ({
        columns: columns.map((x) => x.header),
        data: response?.data.map((x) =>
          columns.map((col) => (col.accessorKey ? x[col.accessorKey] : col.accessorFn(x))),
        ),
        filename: `sales-purchases-${Date.now()}`,
      })}
      help={<>Purchases and Sales</>}
      metadata={response?.metadata}
    />
  );
}
