import { TransactionModel, AllTransactionType, AssetOrder, TradeOnly, TradeCounterparty } from "@models/transaction";

export const getNewTradeModel = (): TransactionModel => {
  const model = new TransactionModel();

  model.trade = new TradeOnly();
  model.trade.counterparties = [new TradeCounterparty()];
  model.trade.counterparties.at(0).isPrimary = true;

  model.assets = [new AssetOrder()];
  model.assets.at(0).isASale = true;

  return model;
};

export const getNewPurchaseModel = (isTraderPreFill?: boolean): TransactionModel => {
  const model = new TransactionModel(isTraderPreFill);
  model.type = AllTransactionType.PURCHASE;

  model.assets = [new AssetOrder()];
  model.assets.at(0).isASale = true;
  model.assets.at(0).isAPurchase = true;

  return model;
};

export const getNewRetirementModel = (): TransactionModel => {
  const model = new TransactionModel();
  model.type = AllTransactionType.RETIREMENT;
  model.status = undefined;
  return model;
};

export const getNewTransferOutflowModel = (): TransactionModel => {
  const model = new TransactionModel();
  model.type = AllTransactionType.TRANSFER_OUTFLOW;
  model.status = undefined;
  return model;
};
