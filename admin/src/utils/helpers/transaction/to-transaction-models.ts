import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { TransactionStatus } from "@constants/transaction-status";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import dateFormatterEST from "@utils/formatters/est-date-formatter";
import { getProductName } from "@utils/helpers/product/get-product-name";
import { getProjectName } from "@utils/helpers/project/get-project-name";
import { calculator, compareDateTime, px } from "@rubiconcarbon/frontend-shared";
import {
  AssetType,
  PurchaseResponse,
  RetirementResponse,
  RetirementType,
  TradeResponse,
  TransactionResponse,
  TransactionType,
} from "@rubiconcarbon/shared-types";
import { TrimmedTransactionModel, TransactionModel, AssetOrder, AllTransactionType } from "@models/transaction";
import { TIFEnum } from "@constants/tif.enum";
import { TransactionTypeLabel } from "@constants/transaction-type";

export const toTrimmedTransactionModel = (row: TransactionResponse): TrimmedTransactionModel => {
  const isMultiple = row?.assetFlows?.length > 1;
  const isPortfolio = row?.assetFlows?.some((flow) => flow?.asset?.type === AssetType.RCT);
  const firstAsset = row?.assetFlows?.at?.(0)?.asset;

  return {
    ...row,
    typeF: [TransactionType.TRADE, TransactionType.FORWARD_LINE_ITEM].includes(row?.type)
      ? TransactionTypeLabel[`${row?.type}-${row?.subtype}`]
      : TransactionTypeLabel[row?.type],
    productF: isMultiple
      ? "Multiple Products"
      : isPortfolio
        ? firstAsset?.name
        : `${firstAsset?.name} - ${firstAsset?.registryProjectId} - ${firstAsset?.label}`,
    updatedAtF: dateFormatterEST(row?.updatedAt?.toLocaleString()),
  };
};

export const toPurchaseModel = (row: PurchaseResponse): GenericTableRowModel<TransactionModel> => {
  return {
    id: row?.id,
    uiKey: row?.uiKey,
    amount: row?.amount,
    type: TransactionType.PURCHASE as unknown as AllTransactionType,
    status: row?.status as unknown as TransactionStatus,
    currentStatus: row?.status as unknown as TransactionStatus,
    isPaid: row?.isPaid,
    isDelivered: row?.isDelivered,
    updatableStatusOrder: row?.updatableStatusOrder,
    memo: row?.memo,
    assets: row?.assets?.map((asset) => ({
      ...asset,
      isASale: true,
      isAPurchase: true,
      unitPrice: calculator(asset?.rawPrice).divide(asset?.amount).calculate().toString(),
      feeTotal: calculator(asset?.serviceFee).add(asset?.otherFee).calculate().toString(),
      grandTotal: calculator(asset?.rawPrice).add(asset?.serviceFee).add(asset?.otherFee).calculate().toString(),
    })) as unknown as AssetOrder[],
    product: getProductName(row?.assets),
    updatedAt: dateFormatterEST(row?.updatedAt?.toLocaleString()),
    dateStarted: dateFormatterEST(row?.dateStarted?.toLocaleString(), ["date"]),
    dateFinished: dateFormatterEST(row?.dateFinished?.toLocaleString(), ["date"]),
    docsCount: row?.docsCount,
    sale: {
      totalValue: row?.assets?.reduce(
        (sum, asset) =>
          calculator(sum).add(asset?.rawPrice).add(asset?.otherFee).add(asset?.serviceFee).calculate().toNumber(),
        0,
      ),
      customerPortfolio: row?.customerPortfolio,
      flowType: row?.flowType,
      needsRiskAdjustment: row?.needsRiskAdjustment,
      paymentDueDate: dateFormatterEST(row?.paymentDueDate?.toLocaleString(), ["date"]),
    },
  } as GenericTableRowModel<TransactionModel>;
};

export const toRetirementModel = (row: RetirementResponse): GenericTableRowModel<TransactionModel> => {
  return {
    id: row?.id,
    uiKey: row?.uiKey,
    amount: row?.amount,
    type: row?.type as unknown as AllTransactionType,
    status: row?.status as unknown as TransactionStatus,
    currentStatus: row?.status as unknown as TransactionStatus,
    memo: row?.memo,
    assets: row?.assets as unknown as AssetOrder[],
    product: getProductName(row?.assets),
    updatedAt: dateFormatterEST(row?.updatedAt?.toLocaleString()),
    dateStarted: dateFormatterEST(row?.dateStarted?.toLocaleString(), ["date"]),
    dateFinished: dateFormatterEST(row?.dateFinished?.toLocaleString(), ["date"]),
    docsCount: row?.docsCount,
    ...px(
      {
        retirement: row?.type === RetirementType.RETIREMENT && {
          customerPortfolio: row?.customerPortfolio,
          isPublic: row?.isPublic,
          beneficiary: row?.beneficiary,
        },
      },
      [false, null, undefined],
    ),
    ...px(
      {
        transfer: row?.type === RetirementType.TRANSFER_OUTFLOW && {
          customerPortfolio: row?.customerPortfolio,
          registryAccount: row?.registryAccount,
        },
      },
      [false, null, undefined],
    ),
  } as GenericTableRowModel<TransactionModel>;
};

export const toTradeModel = (row: TradeResponse): GenericTableRowModel<TransactionModel> => {
  const counterparties = row?.counterparties
    ?.sort((a, b) => {
      if (a?.isPrimary !== b?.isPrimary) return b.isPrimary ? 1 : -1;
      return compareDateTime(a?.counterparty?.createdAt?.toString(), b?.counterparty?.createdAt?.toString());
    })
    ?.map((party) => ({
      counterparty: party?.counterparty,
      role: party?.role,
      isPrimary: party?.isPrimary,
      comments: party?.comments || undefined,
    }));

  return {
    id: row?.id,
    uiKey: row?.uiKey,
    amount: row?.amount,
    type: row?.type as unknown as AllTransactionType,
    status: row?.status as unknown as TransactionStatus,
    currentStatus: row?.status as unknown as TransactionStatus,
    updatableStatusOrder: row?.updatableStatusOrder,
    isPaid: row?.isPaid,
    isDelivered: row?.isDelivered,
    memo: row?.memo,
    assets: row?.projectVintages?.map((asset) => ({
      ...asset,
      isASale: true,
      source: row?.book as unknown as PrimitiveTypeBook,
      unitPrice: calculator(asset?.rawPrice).divide(asset?.amount).calculate().toString(),
      feeTotal: calculator(asset?.serviceFee).add(asset?.otherFee).calculate().toString(),
      grandTotal: calculator(asset?.rawPrice)
        ?.[(row.type as any) === AllTransactionType.SELL ? "subtract" : "add"](asset?.serviceFee)
        ?.[(row.type as any) === AllTransactionType.SELL ? "subtract" : "add"](asset?.otherFee)
        ?.calculate()
        ?.toString(),
    })) as unknown as AssetOrder[],
    product: getProjectName(row?.projectVintages),
    updatedAt: dateFormatterEST(row?.updatedAt?.toLocaleString()),
    dateStarted: dateFormatterEST(row?.createdAt?.toLocaleString(), ["date"]),
    dateFinished: dateFormatterEST(row?.settledAt?.toLocaleString(), ["date"]),
    docsCount: row?.docsCount,
    trade: {
      counterparties,
      totalValue: row?.projectVintages?.reduce(
        (sum, asset) =>
          calculator(sum)
            ?.[(row.type as any) === AllTransactionType.SELL ? "subtract" : "add"](asset?.serviceFee)
            ?.[(row.type as any) === AllTransactionType.SELL ? "subtract" : "add"](asset?.otherFee)
            ?.calculate()
            .toNumber(),
        0,
      ),
      tif: row?.goodUntilDate ? TIFEnum.EOD : TIFEnum.GTC,
      poid: row?.poid,
      goodUntilDate: dateFormatterEST(row?.goodUntilDate?.toLocaleString()),
    },
  } as GenericTableRowModel<TransactionModel>;
};
