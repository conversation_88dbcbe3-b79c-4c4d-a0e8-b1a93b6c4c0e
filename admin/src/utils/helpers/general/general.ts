import { isEmpty } from "lodash";

export function searchStringInArray(str: string, strArray: string[]): boolean {
  if (isEmpty(strArray)) return false;
  for (let j = 0; j < strArray.length; j++) {
    if (strArray[j].toUpperCase().includes(str)) return true;
  }
  return false;
}

export function convertStringToNumber(input: string): number {
  return +input.replaceAll(",", "").replaceAll("$", "").replaceAll("%", "");
}

export function convertPriceToNumber(price: string): number {
  return +price.replaceAll(",", "").replaceAll("$", "");
}

export function convertPercentToNumber(price: string): number {
  return +price.replaceAll(",", "").replaceAll("%", "");
}

export function convertAmountToNumber(amount: string): number {
  return +amount.replaceAll(",", "");
}

export function getAverageByKey<T extends { [key: string]: any }>(data: T[], valueKey: string, amountKey: string): number | null {
  if (data) {
    return (
      data.reduce((acc, e) => acc + e[valueKey] * e[amountKey], 0) / data.reduce((acc, e) => acc + e[amountKey], 0)
    );
  }

  return null;
}

export function getAverageIfAllExist<T extends { [key: string]: any }>(data: T[], valueKey: string, amountKey: string): number | null {
  if (data && data.length > 0 && !data.some((e) => e[valueKey] === null)) {
    return (
      data.reduce((acc, e) => acc + e[valueKey] * e[amountKey], 0) / data.reduce((acc, e) => acc + e[amountKey], 0)
    );
  }

  return null;
}

export const elapsedTime = (
  date: Date,
): {
  format: "sec" | "min" | "hour" | "hours" | "day" | "days" | "month" | "months" | "year" | "years";
  value: number;
} => {
  const now = new Date();
  const utcDate = new Date(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate(),
    now.getUTCHours(),
    now.getUTCMinutes(),
    now.getUTCSeconds(),
    now.getUTCMilliseconds(),
  );
  const elapsedMilliseconds = utcDate.getTime() - date.getTime();

  if (elapsedMilliseconds < 60000) {
    // Less than 1 minute
    const value = Math.floor(elapsedMilliseconds / 1000);
    return {
      format: "sec",
      value,
    };
  } else if (elapsedMilliseconds < 3600000) {
    // Less than 1 hour
    const value = Math.floor(elapsedMilliseconds / 60000);
    return {
      format: "min",
      value,
    };
  } else if (elapsedMilliseconds < 86400000) {
    // Less than 1 day
    const value = Math.floor(elapsedMilliseconds / 3600000);
    return {
      format: value === 1 ? "hour" : "hours",
      value,
    };
  } else if (elapsedMilliseconds < 2592000000) {
    // Less than 30 days (approx 1 month)
    const value = Math.floor(elapsedMilliseconds / 86400000);
    return {
      format: value === 1 ? "day" : "days",
      value,
    };
  } else if (elapsedMilliseconds < 31536000000) {
    // Less than 365 days (approx 1 year)
    const value = Math.floor(elapsedMilliseconds / 2592000000);
    return {
      format: value === 1 ? "month" : "months",
      value,
    };
  } else {
    const value = Math.floor(elapsedMilliseconds / 31536000000);
    return {
      format: value === 1 ? "year" : "years",
      value,
    };
  }
};