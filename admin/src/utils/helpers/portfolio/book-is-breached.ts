import Decimal from "decimal.js";
import { calculator, isNothing, toDecimal } from "@rubiconcarbon/frontend-shared";
import { AdminBookResponse, AssetType } from "@rubiconcarbon/shared-types";
import { PrimitiveTypeBook } from "@models/primitive-type-book";

export const bookIsBreached = (book: AdminBookResponse | PrimitiveTypeBook, price: string | Decimal): boolean => {
  const { limit = null, ownerAllocationsByAssetType } = book || {};
  const bookLimit = toDecimal(limit?.holdingPriceMax);
  const currentBookLimit = bookLimit?.isZero() ? null : bookLimit;

  const bookVintagePrices = ownerAllocationsByAssetType?.find(
    ({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE,
  )?.groupedPrices;

  const currentLimit = calculator(
    calculator(bookVintagePrices?.totalPriceAllocated)
      .add(bookVintagePrices?.totalPricePendingBuy)
      .subtract(bookVintagePrices?.totalPricePendingSell)
      .calculate(),
  )
    .add(price)
    .calculate();

  return (
    !isNothing(currentBookLimit) &&
    calculator(currentLimit, { treatNothingAsNaN: true }).isGreaterThan(currentBookLimit)
  );
};
