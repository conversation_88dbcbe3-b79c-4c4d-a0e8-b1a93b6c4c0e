import { NON_RCT_TYPES } from "@constants/constants";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { calculator, isEmptyRecord, isNothing, pickFromRecord } from "@rubiconcarbon/frontend-shared";
import { AdminBookResponse, BookType } from "@rubiconcarbon/shared-types";

// @deprecated | kofi
// todo : @kofi to clean this up. can this use grouping parents? or is this needed if you just throw errors where .ownerAllocations and .ownerAllocations.grouped prices are null?
export const getTransformedBookResponse = (
  books: AdminBookResponse[] = [],
  treatNothingAsNaN: boolean = false,
): PrimitiveTypeBook[] => {
  const nonRCTs = (books.filter((book) => NON_RCT_TYPES.includes(book?.type)) as unknown as PrimitiveTypeBook[]) || [];
  const RCT = books
    .filter((book: AdminBookResponse) => !NON_RCT_TYPES.includes(book?.type))
    .reduce((rct: PrimitiveTypeBook, book) => {
      if (book.type === BookType.PORTFOLIO_DEFAULT)
        rct = {
          ...rct,
          ...pickFromRecord(book, ["id", "name", "type", "isEnabled", "description", "allowedActions"]),
        } as PrimitiveTypeBook;
      return {
        ...rct,
        limit: {
          ...(rct?.limit || {}),
          holdingPriceMax: calculator(rct?.limit?.holdingPriceMax, { treatNothingAsNaN })
            .add(book.limit.holdingPriceMax)
            .calculate()
            .toString(),
          holdingPriceMin: calculator(rct?.limit?.holdingPriceMin, { treatNothingAsNaN })
            .add(book.limit.holdingPriceMin)
            .calculate()
            .toString(),
        },
        priceVintagesAllocated: calculator(rct?.priceVintagesAllocated)
          .add(book?.ownerAllocations?.groupedPrices?.totalPriceAllocated)
          .calculate()
          .toString(),
        priceVintagesPendingBuy: calculator(rct?.priceVintagesPendingBuy)
          .add(book?.ownerAllocations?.groupedPrices?.totalPricePendingBuy)
          .calculate()
          .toString(),
        priceVintagesPendingSell: calculator(rct?.priceVintagesPendingSell)
          .add(book?.ownerAllocations?.groupedPrices?.totalPricePendingSell)
          .calculate()
          .toString(),
        amountVintagesAllocated: calculator(rct?.amountVintagesAllocated)
          .add(book?.ownerAllocations?.totalAmountAllocated)
          .calculate()
          .toNumber(),
        amountVintagesPendingBuy: calculator(rct?.amountVintagesPendingBuy)
          .add(book?.ownerAllocations?.totalAmountPendingBuy)
          .calculate()
          .toNumber(),
        amountVintagesPendingSell: calculator(rct?.amountVintagesPendingSell)
          .add(book?.ownerAllocations?.totalAmountPendingSell)
          .calculate()
          .toNumber(),
      } as PrimitiveTypeBook;
    }, {} as PrimitiveTypeBook);

  return [...nonRCTs, RCT].filter((book) => !isNothing(book) && !isEmptyRecord(book));
};
