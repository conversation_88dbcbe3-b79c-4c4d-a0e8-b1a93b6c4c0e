import { AllocationResponse, uuid, BookType, BookAction } from "@rubiconcarbon/shared-types";

// @deprecated | kofi
export const getVintageToBookTypes = (bookAllocation: AllocationResponse[]): Record<uuid, BookType[]> => {
  return Object.entries(
    (bookAllocation || []).reduce(
      (record, allocation) => ({
        ...record,
        [allocation.detailedAsset.id]: Array.from(
          new Set(
            [
              ...(record?.[allocation.detailedAsset.id] || []),
              allocation?.owner?.allowedActions?.some((action) => [BookAction.BUY, BookAction.SELL].includes(action))
                ? allocation?.owner?.type
                : null,
            ].filter((type) => !!type),
          ),
        ),
      }),
      {} as Record<uuid, BookType[]>,
    ),
  ).reduce(
    (record, [id, types]) => ({
      ...record,
      ...(types?.length
        ? {
            [id]: types,
          }
        : {}),
    }),
    {},
  );
};
