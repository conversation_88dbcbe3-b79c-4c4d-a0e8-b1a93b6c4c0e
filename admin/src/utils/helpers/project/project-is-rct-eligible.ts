import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";
import { isNothing, toDecimal } from "@rubiconcarbon/frontend-shared";
import { AdminProjectVintageResponse } from "@rubiconcarbon/shared-types";

export const projectIsRCTEligible = (vintage: AdminProjectVintageResponse | PrimitiveTypeVintage): boolean => {
  const { riskBufferPercentage, project } = vintage || {};
  const { rctStandard = false, suspended = false, isScienceTeamApproved = false } = project || {};

  return (
    rctStandard &&
    !suspended &&
    isScienceTeamApproved &&
    (isNothing(riskBufferPercentage) ||
      (!isNothing(riskBufferPercentage) && toDecimal(riskBufferPercentage).lessThanOrEqualTo(1)))
  );
};
