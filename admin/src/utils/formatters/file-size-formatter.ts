const SizeUnits = ["B", "KB", "MB", "GB", "TB"] as const;

const fileSizeFormatter = (size: number, unit: (typeof SizeUnits)[number], precision = 2): string => {
  let exponent = SizeUnits.findIndex((_unit) => _unit === unit);

  let converted = size / Math.pow(1024, exponent);

  while (exponent > 0 && converted < 1) {
    exponent--;
    converted = size / Math.pow(1024, exponent);
  }

  return `${converted.toFixed(precision)}${SizeUnits[exponent]}`;
};

export default fileSizeFormatter;
