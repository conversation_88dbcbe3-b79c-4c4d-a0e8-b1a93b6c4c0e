import { Nullable } from "@rubiconcarbon/frontend-shared";
import { format as formatter } from "date-fns";
import { enUS } from "date-fns/locale";

const dateFormatter = (value: number | string | Date, format = "MMM dd yyyy"): Nullable<string> => {
  if (value === null || value === undefined) {
    return null;
  }

  return formatter(new Date(value), format, { locale: enUS });
};
export default dateFormatter;
