import { Nullable } from "@rubiconcarbon/frontend-shared";
import { parseISO } from "date-fns";
import { utcToZonedTime, format } from "date-fns-tz";

const dateFormatterUTC = (value: string): Nullable<string> => {
  if (value === null || value === undefined) {
    return null;
  }

  const parsedTime = parseISO(value);
  const formatInTimeZone = (date: string | number | Date, fmt: string, tz: string): string =>
    format(utcToZonedTime(date, tz), fmt, { timeZone: tz });

  return formatInTimeZone(parsedTime, "d MMM yyyy K:mm aaaa zzz", "UTC");
};
export default dateFormatterUTC;
