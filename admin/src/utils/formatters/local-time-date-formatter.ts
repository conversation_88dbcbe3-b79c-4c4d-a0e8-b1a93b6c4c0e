import { Nullable } from "@rubiconcarbon/frontend-shared";
import { parseISO } from "date-fns";

const dateFormatterLocalTime = (value: string, parts: ("date" | "time")[] = ["date", "time"]): Nullable<string> => {
  if (value === null || value === undefined) {
    return null;
  }

  const date = parseISO(value);

  if (parts?.length === 1) {
    if (parts?.at(0) === "date") return date.toLocaleDateString();
    else return date.toLocaleTimeString();
  } else return date.toLocaleString();
};
export default dateFormatterLocalTime;
