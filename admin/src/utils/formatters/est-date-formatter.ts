import { formatInTimeZone } from "date-fns-tz";
import { EST_TIME_ZONE, DATE_TIME_FORMAT, DATE_FORMAT, TIME_FORMAT } from "@constants/constants";
import { Nullable } from "@rubiconcarbon/frontend-shared";

const dateFormatterEST = (value: string, parts: ("date" | "time")[] = ["date", "time"]): Nullable<string> => {
  if (value === null || value === undefined) {
    return null;
  }

  const date = new Date(value);

  if (parts?.length === 1) {
    if (parts?.at(0) === "date") return formatInTimeZone(date, EST_TIME_ZONE, DATE_FORMAT);
    else return formatInTimeZone(date, EST_TIME_ZONE, TIME_FORMAT);
  } else return formatInTimeZone(date, EST_TIME_ZONE, DATE_TIME_FORMAT);
};
export default dateFormatterEST;
