import { formatInTimeZone } from "date-fns-tz";
import { UTC_TIME_ZONE, DATE_REANGE_FORMAT } from "@constants/constants";
import { MaybeNothing } from "@rubiconcarbon/frontend-shared";

const dateRangeFormatter = (value: MaybeNothing<string>): string => {
  if (value === null || value === undefined) {
    return "";
  }

  try {
    const dates = value.split(" - ");
    return `${formatInTimeZone(
      dates[0],
      UTC_TIME_ZONE,
      DATE_REANGE_FORMAT,
    )} - ${formatInTimeZone(dates[1], UTC_TIME_ZONE, DATE_REANGE_FORMAT)}`;
  } catch (e) {
    console.error("Failed to format date range value.", e);
    return "";
  }
};

export default dateRangeFormatter;
