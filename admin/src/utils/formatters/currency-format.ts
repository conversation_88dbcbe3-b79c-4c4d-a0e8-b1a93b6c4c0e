import { Locale } from "@/constants/locale";

const currencyFormat = (value: number | null | undefined, minimumFractionDigits: number = 2): string | null => {
  if (value === null || value === undefined) {
    return null;
  }

  const formatter = new Intl.NumberFormat(Locale.DEFAULT_REGION, {
    style: "currency",
    currency: "USD",
    minimumFractionDigits,
  });

  return formatter.format(value);
};

export default currencyFormat;
