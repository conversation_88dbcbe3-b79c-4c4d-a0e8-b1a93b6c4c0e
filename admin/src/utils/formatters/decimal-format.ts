import { Locale } from "@/constants/locale";

const decimalFormat = (
  value: number | null | undefined,
  minimumFractionDigits: number = 2,
  maximumFractionDigits: number = 2,
): string | null => {
  if (value === null || value === undefined) {
    return null;
  }

  const formatter = new Intl.NumberFormat(Locale.DEFAULT_REGION, {
    minimumFractionDigits,
    maximumFractionDigits,
  });

  return formatter.format(value);
};

export default decimalFormat;
