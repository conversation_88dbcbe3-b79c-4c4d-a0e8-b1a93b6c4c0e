/**
 * Reflect Metadata Utility
 *
 * This file ensures that reflect-metadata is properly initialized for both server and client components.
 * It's used by class-validator decorators and other libraries that rely on the Reflect API.
 *
 * Import this file in:
 * 1. The root layout file (for server components)
 * 2. The root providers file (for client components)
 */

// Import reflect-metadata to enable class-validator decorators
import "reflect-metadata";

// This function doesn't do anything, it's just to ensure the import is included
export function ensureReflectMetadata(): boolean {
  // This is just to ensure the import is not tree-shaken
  return !!Reflect;
}

// Initialize reflect-metadata
ensureReflectMetadata();
