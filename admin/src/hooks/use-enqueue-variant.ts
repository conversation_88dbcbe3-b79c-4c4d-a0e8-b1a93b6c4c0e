import { OptionsObject, Snackbar<PERSON>ey, useSnackbar } from "notistack";
import { ReactNode, useMemo } from "react";

type UseSnackbarVariantsReturn = {
  enqueueWarning: (message: string | ReactNode, options?: OptionsObject<"warning">) => SnackbarKey;
  enqueueSuccess: (message: string | ReactNode, options?: OptionsObject<"success">) => SnackbarKey;
  enqueueError: (message: string | ReactNode, options?: OptionsObject<"error">) => SnackbarKey;
  enqueueInfo: (message: string | ReactNode, options?: OptionsObject<"info">) => SnackbarKey;
  closeSnackbar: (key?: SnackbarKey) => void;
};

export default function useSnackbarVariants(): UseSnackbarVariantsReturn {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  return useMemo(
    () => ({
      enqueueWarning: (message: string | ReactNode, options = {}) =>
        enqueueSnackbar(message, { variant: "warning", ...options }),
      enqueueSuccess: (message: string | ReactNode, options = {}) =>
        enqueueSnackbar(message, { variant: "success", ...options }),
      enqueueError: (message: string | ReactNode, options = {}) =>
        enqueueSnackbar(message, { variant: "error", ...options }),
      enqueueInfo: (message: string | ReactNode, options = {}) =>
        enqueueSnackbar(message, { variant: "info", ...options }),
      closeSnackbar,
    }),
    [enqueueSnackbar, closeSnackbar],
  );
}
