import {
  DocumentQuery,
  DocumentQueryResponse,
  DocumentResponse,
  DocumentUpdateRequest,
  DocumentUploadUrlRequest,
  DocumentUploadUrlResponse,
} from "@rubiconcarbon/shared-types";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { TriggerWithOptionsArgs } from "swr/mutation";
import { NO_OP, px } from "@rubiconcarbon/frontend-shared";
import { useMemo } from "react";

type UseDocumentsApiProps = {
  id?: string;
  query?: DocumentQuery;
  uploadLinkPayload?: DocumentUploadUrlRequest;
  updatePayload?: DocumentUpdateRequest;

  onUploadLinkRetrievalSuccess?: (data: DocumentUploadUrlResponse) => void;
  onUploadLinkRetrievalError?: (error: any) => void;
  onFetchSuccess?: (data: DocumentResponse) => void;
  onFetchError?: (error: any) => void;
  onUpdateSuccess?: (data: DocumentResponse) => void;
  onUpdateError?: (error: any) => void;
  onRemoveSuccess?: () => void;
  onRemoveError?: (error: any) => void;
};

type UseDocumentsApiReturn = {
  uploadLink: DocumentUploadUrlResponse;
  documents: DocumentResponse[];

  retrievingUploadLink: boolean;
  fetching: boolean;
  updating: boolean;
  removing: boolean;

  retrieveUploadLinkError: any;
  fetchError: any;
  updateError: any;
  removeError: any;

  retrieveUploadLink: TriggerWithOptionsArgs<DocumentUploadUrlResponse, any, any>;
  fetch: TriggerWithOptionsArgs<any, any, any>;
  update: TriggerWithOptionsArgs<any, any, any>;
  remove: TriggerWithOptionsArgs<any, any, any>;
};

const useDocumentsApi = ({
  id,
  query: queryParams,
  uploadLinkPayload,
  updatePayload,

  onUploadLinkRetrievalSuccess = NO_OP,
  onUploadLinkRetrievalError = NO_OP,
  onFetchSuccess = NO_OP,
  onFetchError = NO_OP,
  onUpdateSuccess = NO_OP,
  onUpdateError = NO_OP,
  onRemoveSuccess = NO_OP,
  onRemoveError = NO_OP,
}: UseDocumentsApiProps): UseDocumentsApiReturn => {
  const {
    data: uploadLink,
    isMutating: retrievingUploadLink,
    error: retrieveUploadLinkError,
    trigger: retrieveUploadLink,
  } = useTriggerRequest<DocumentUploadUrlResponse, DocumentUploadUrlRequest, null, null>({
    url: "admin/documents",
    method: "post",
    requestBody: uploadLinkPayload,
    swrOptions: {
      onSuccess: onUploadLinkRetrievalSuccess,
      onError: onUploadLinkRetrievalError,
    },
  });

  const {
    data: response,
    isMutating: fetching,
    error: fetchError,
    trigger: fetch,
  } = useTriggerRequest<DocumentResponse | DocumentQueryResponse, null, null, DocumentQuery>({
    url: `admin/documents${id ? "/{id}" : ""}`,
    ...px({ pathParams: !!id && { id } }, [null, undefined, false]),
    queryParams,
    swrOptions: {
      onSuccess: onFetchSuccess,
      onError: onFetchError,
    },
  });

  const {
    isMutating: updating,
    error: updateError,
    trigger: update,
  } = useTriggerRequest<DocumentResponse, DocumentUpdateRequest, null, null>({
    url: "admin/documents",
    method: "put",
    requestBody: updatePayload,
    swrOptions: {
      onSuccess: onUpdateSuccess,
      onError: onUpdateError,
    },
  });

  const {
    isMutating: removing,
    error: removeError,
    trigger: remove,
  } = useTriggerRequest({
    url: "admin/documents/{id}",
    pathParams: {
      id,
    },
    method: "delete",
    swrOptions: {
      onSuccess: onRemoveSuccess,
      onError: onRemoveError,
    },
  });

  const documents = useMemo(
    () => (response ? (response as DocumentQueryResponse)?.data || [response] : []),
    [response],
  ) as DocumentResponse[];

  return {
    uploadLink,
    documents: documents.sort((a, b) => (a.createdAt > b.createdAt ? -1 : 1)),

    retrievingUploadLink,
    fetching,
    updating,
    removing,

    retrieveUploadLinkError,
    fetchError,
    updateError,
    removeError,

    retrieveUploadLink,
    fetch,
    update,
    remove,
  };
};

export default useDocumentsApi;
