import { RefObject, useEffect, useState } from "react";

type ElementDimensionSide = "right" | "left" | "up" | "down";

type ScrollData = {
  canScrollRight: boolean;
  canScrollLeft: boolean;
  canScrollUp: boolean;
  canScrollDown: boolean;
  scrollByAmount: (direction: ElementDimensionSide, amount: number) => void;
  scrollToEnd: (direction: ElementDimensionSide) => void;
  scrollIntoView: (elementRef: RefObject<HTMLElement>) => void;
  updateScrollData: (element: HTMLElement) => void;
};

/**
 * useScroll
 *
 * This hook takes in a reference element container that is suppose to have scrollable content as it's children.
 * It then returens a set of properties and functions that help with programmatic scrolling the child contents in the page.
 *
 * @param ref | ref of an element
 * @returns | an object that contains properties and function that can use to implement programmatic scrolling of child contents.
 */
const useScroll = <T extends HTMLElement>(ref: RefObject<T>): ScrollData => {
  const [scrollData, setScrollData] = useState<
    Omit<ScrollData, "scrollByAmount" | "scrollToEnd" | "scrollIntoView" | "updateScrollData">
  >({
    canScrollRight: false,
    canScrollLeft: false,
    canScrollUp: false,
    canScrollDown: false,
  });

  useEffect(() => {
    const element = ref.current;

    if (element) {
      handleScroll(element);

      element.addEventListener("scroll", () => handleScroll(element));
      window.addEventListener("resize", () => handleScroll(element));

      return (): void => {
        element.removeEventListener("scroll", () => handleScroll(element));
        window.removeEventListener("resize", () => handleScroll(element));
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref.current]);

  const scrollByAmount = (direction: ElementDimensionSide, amount: number): void => {
    const element = ref.current;

    if (element) {
      if (direction === "right") {
        element.scrollTo({ left: element.scrollLeft + amount, behavior: "smooth" });
      } else if (direction === "left") {
        element.scrollTo({ left: element.scrollLeft - amount, behavior: "smooth" });
      } else if (direction === "up") {
        element.scrollTo({ top: element.scrollTop - amount, behavior: "smooth" });
      } else if (direction === "down") {
        element.scrollTo({ top: element.scrollTop + amount, behavior: "smooth" });
      }
    }
  };

  const scrollToEnd = (direction: ElementDimensionSide): void => {
    const element = ref.current;

    if (element) {
      if (direction === "right") {
        element.scrollTo({ left: element.scrollWidth - element.clientWidth, behavior: "smooth" });
      } else if (direction === "left") {
        element.scrollTo({ left: 0, behavior: "smooth" });
      } else if (direction === "up") {
        element.scrollTo({ top: 0, behavior: "smooth" });
      } else if (direction === "down") {
        element.scrollTo({ top: element.scrollHeight - element.clientHeight, behavior: "smooth" });
      }
    }
  };

  const scrollIntoView = (elementRef: RefObject<HTMLElement>): void => {
    const container = ref.current;
    const element = elementRef.current;

    if (container && element) {
      element.scrollIntoView({ behavior: "smooth", block: "start", inline: "nearest" });
    }
  };

  const handleScroll = (element: HTMLElement): void => {
    setTimeout(() => {
      const { scrollLeft, scrollWidth, clientWidth, scrollTop, scrollHeight, clientHeight } = element;

      setScrollData({
        canScrollRight: scrollLeft < scrollWidth - clientWidth,
        canScrollLeft: scrollLeft > 0,
        canScrollUp: scrollTop > 0,
        canScrollDown: scrollTop < scrollHeight - clientHeight,
      });
    }, 50);
  };

  return {
    ...scrollData,
    scrollByAmount,
    scrollToEnd,
    scrollIntoView,
    updateScrollData: handleScroll,
  };
};

export default useScroll;
