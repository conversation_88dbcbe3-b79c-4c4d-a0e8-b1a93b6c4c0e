import { deepEqual } from "@rubiconcarbon/frontend-shared";
import { useCallback } from "react";
import { FieldValues, Path, PathValue, UseFormProps, UseFormReturn, useForm } from "react-hook-form";

export type UseEnhancedFormReturn<TFieldValues extends FieldValues = FieldValues> = UseFormReturn<TFieldValues> & {
  smartSetValue: <TFieldName extends Path<TFieldValues>>(
    name: TFieldName,
    value: PathValue<TFieldValues, TFieldName>,
    options?: {
      shouldValidate?: boolean;
      shouldDirty?: boolean;
      shouldTouch?: boolean;
    },
  ) => void;
};

/**
 * A hook that wraps useForm and provides a smart setValue
 * that prevents redundant updates when used inside form components.
 */
export function useEnhancedForm<TFieldValues extends FieldValues = FieldValues>(
  props?: UseFormProps<TFieldValues>,
): UseEnhancedFormReturn<TFieldValues> {
  // Get the original form methods
  const methods = useForm<TFieldValues>(props);
  const { setValue, getValues } = methods;

  // Create smart setValue that avoids unnecessary updates
  const smartSetValue = useCallback(
    <TFieldName extends Path<TFieldValues>>(
      name: TFieldName,
      value: PathValue<TFieldValues, TFieldName>,
      options?: {
        shouldValidate?: boolean;
        shouldDirty?: boolean;
        shouldTouch?: boolean;
      },
    ): void => {
      // Get current value at this path
      const currentValue = getValues(name);

      // Only update if the value is different
      if (!deepEqual(currentValue, value)) {
        setValue(name, value, options);
      }
    },
    [setValue, getValues],
  );

  // Return all original methods plus our smart setValue one
  return {
    ...methods,
    smartSetValue,
  };
}
