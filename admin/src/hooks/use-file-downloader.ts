import { AxiosContext } from "@providers/axios-provider";
import { useContext } from "react";
import { useLogger } from "@providers/logging";

export interface DownloadResult {
  isSuccess: boolean;
  message?: string;
  error?: any;
}

export default function useFileDownloader(): any {
  const { api } = useContext(AxiosContext);
  const { logger } = useLogger();

  const download = async (fileName: string, requestUrl: string): Promise<DownloadResult> => {
    try {
      const response = await api.get<any>(requestUrl, { responseType: "blob" });
      const pdfBlob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(pdfBlob);
      const tempLink = document.createElement("a");
      tempLink.href = url;
      tempLink.setAttribute("download", `${fileName}.pdf`);
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
      window.URL.revokeObjectURL(url);
      return {
        isSuccess: true,
      };
    } catch (error: any) {
      logger.error(`Failed to download file. Error: ${error?.message} `, {});
      return {
        isSuccess: false,
        message: "Failed to download file",
        error,
      };
    }
  };

  return { download };
}
