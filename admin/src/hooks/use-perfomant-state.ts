import { Undefinable } from "@rubiconcarbon/frontend-shared";
import { deepEqual } from "fast-equals";
import { useCallback, useState } from "react";

/**
 * The function `usePerformantState` returns a state value and a function to update the state in a
 * performant way by checking for deep equality before updating.
 * @param {T} initial - The `initial` parameter is the initial value that will be used to initialize
 * the state.
 * @returns The `usePerformantState` function returns a tuple containing two elements:
 * 1. The current state value of type `T`.
 * 2. A function that can be used to update the state value in a performant way by only updating if the
 * new value is different from the current value.
 */
const usePerformantState = <T>(initial?: T): [Undefinable<T>, (updated: T) => void] => {
  const [value, setValue] = useState<Undefinable<T>>(initial ?? undefined);

  const setPerformantValue = useCallback((updated: T) => {
    setValue((current) => {
      if (deepEqual(current, updated)) return current;
      return updated;
    });
  }, []);

  return [value, setPerformantValue];
};

export default usePerformantState;
