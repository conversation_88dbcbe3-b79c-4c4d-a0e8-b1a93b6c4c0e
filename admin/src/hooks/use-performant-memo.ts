import { DependencyList, useRef } from "react";
import { deepEqual } from "fast-equals";

/**
 * The `usePerformantMemo` hook returns a memoized value that only updates when its dependencies change,
 * using deep equality to compare the dependencies.
 * @param {() => T} factory - The `factory` parameter is a function that returns the value to be memoized.
 * @param {DependencyList} deps - The `deps` parameter is an array of dependencies that the memoized
 * value depends on.
 * @returns {T} - The memoized value.
 */
const usePerformantMemo = <T>(factory: () => T, deps: DependencyList): T => {
  const prevDepsRef = useRef<DependencyList>();
  const memoizedValueRef = useRef<T>();

  if (!prevDepsRef.current || !deepEqual(prevDepsRef.current, deps)) {
    prevDepsRef.current = deps;
    memoizedValueRef.current = factory();
  }

  return memoizedValueRef.current as T;
};

export default usePerformantMemo;
