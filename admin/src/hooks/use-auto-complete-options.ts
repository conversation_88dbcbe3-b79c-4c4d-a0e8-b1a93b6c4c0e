import { AllKeys, NonEmptyArray, pickFromArrayOfRecords, px } from "@rubiconcarbon/frontend-shared";
import { ReactNode, useMemo } from "react";

type UseAutoCompleteOptions<PreTransformed, Value, Transformed> = {
  data: PreTransformed[];
  keys: NonEmptyArray<AllKeys<Transformed>>;
  disabled?: (entry: Partial<Transformed>) => boolean;
  label: (entry: Partial<Transformed>) => string;
  displayLabel?: (entry: Partial<Transformed>) => ReactNode;
  value: (entry: Partial<Transformed>) => Value;
  preTransform?: (data: PreTransformed[]) => Transformed[];
  postTransform?: (data: UseAutoCompleteOptionsReturnEntry<Value>[]) => UseAutoCompleteOptionsReturnEntry<Value>[];
};

export type UseAutoCompleteOptionsReturnEntry<V = any> = {
  internal?: boolean;
  label: string;
  displayLabel?: ReactNode;
  value: V;
  disabled?: boolean;
};

const useAutoCompleteOptions = <T, V = any, Pr = T>({
  data,
  keys,
  disabled = (): boolean => false,
  label,
  displayLabel,
  value,
  preTransform = (data: T[]): Pr[] => data as unknown as Pr[],
  postTransform = (data: UseAutoCompleteOptionsReturnEntry[]): UseAutoCompleteOptionsReturnEntry[] => data,
}: UseAutoCompleteOptions<T, V, Pr>): UseAutoCompleteOptionsReturnEntry<V>[] =>
  useMemo(
    () =>
      postTransform(
        pickFromArrayOfRecords<Pr>(preTransform(data), keys).map((entry) => ({
          disabled: disabled(entry),
          label: label(entry),
          ...{ ...px({ displayLabel: !!displayLabel && displayLabel(entry) }, [false, null, undefined]) },
          value: value(entry),
        })),
      ),
    [data, disabled, displayLabel, keys, label, postTransform, preTransform, value],
  );

export default useAutoCompleteOptions;
