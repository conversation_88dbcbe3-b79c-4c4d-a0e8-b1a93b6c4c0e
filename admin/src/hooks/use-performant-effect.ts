import { deepEqual } from "fast-equals";
import { Dependency<PERSON>ist, Effect<PERSON>allback, useEffect, useRef } from "react";

/**
 * The function `usePerformantEffect` triggers a callback function only when its dependencies change,
 * using deep equality to compare the dependencies.
 * @param {EffectCallback} effect - The `effect` parameter is a function that will be executed when the
 * dependencies change.
 * @param {DependencyList} deps - The `deps` parameter is an array of dependencies that the effect depends on.
 */
const usePerformantEffect = (effect: EffectCallback, deps: DependencyList): void => {
  const previousDependencyRef = useRef<DependencyList>();

  useEffect(() => {
    if (!previousDependencyRef.current || !deepEqual(previousDependencyRef.current, deps)) {
      previousDependencyRef.current = deps;
      return effect();
    }
  }, [deps, effect]);
};

export default usePerformantEffect;
