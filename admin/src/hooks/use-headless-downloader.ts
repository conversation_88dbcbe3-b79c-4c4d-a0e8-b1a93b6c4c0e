import { AxiosContext } from "@providers/axios-provider";
import { AxiosInstance } from "axios";
import { Dispatch, SetStateAction, useCallback, useContext, useMemo, useState } from "react";
import useSWR from "swr";
import { Bare<PERSON>etcher, KeyedMutator, PublicConfiguration } from "swr/_internal";
import { DownloadResult } from "@/types/headless-downloader";
import { Method } from "@rubiconcarbon/frontend-shared";

export type DownloadQueryData = {
  url: string;
  method: Method;
  data?: any;
};

export type DownloadQuery = Record<string, DownloadQueryData>;

export type FileDownloadResponse = { data: Blob };

export type SettledFileDownloadResponse = PromiseSettledResult<Awaited<FileDownloadResponse>>[];

type UseHeadlessDownloadProps = {
  enable: boolean;
  query: DownloadQuery;
  fileName: string | ((key: string) => string);
  swrOptions?: Partial<PublicConfiguration<SettledFileDownloadResponse, any, BareFetcher<SettledFileDownloadResponse>>>;
  onFileDownloadSuccess?: (data: Blob, key: string, fileName: string) => void;
  onFileDownloadError?: (error: any, key: string, fileName: string) => void;
};

type UseHeadlessDownloadReturnData = {
  downloading: boolean;
  progresses: Record<string, boolean>;
  results: Record<string, DownloadResult>;
  apiResults: SettledFileDownloadResponse;
  apiError: any;
  mutate: KeyedMutator<SettledFileDownloadResponse>;
  reset: () => void;
};

const downloadProgrammatically = (
  data: Blob,
  key: string,
  fileName: string,
  onSuccessCallback?: (data: Blob, key: string, fileName: string) => void,
  onErrorCallback?: (error: any, key: string, fileName: string) => void,
): void => {
  try {
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(data as Blob);
    link.download = fileName;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    if (onSuccessCallback) onSuccessCallback(data, key, fileName);
  } catch (error: any) {
    if (onErrorCallback) onErrorCallback(error, key, fileName);
  }
};

const runFileApiDownload = async (
  apiData: DownloadQueryData,
  key: string,
  fileName: string,
  axiosInstance: AxiosInstance,
  updateProgresses: Dispatch<SetStateAction<Record<string, boolean>>>,
  updateResults: Dispatch<SetStateAction<Record<string, DownloadResult>>>,
  download: (data: Blob, key: string) => void,
  onErrorCallback?: (error: any, key: string, fileName: string) => void,
): Promise<FileDownloadResponse> => {
  const { url, method, data } = apiData;

  try {
    updateProgresses((previous) => ({ ...previous, [key]: true }));

    const response = await axiosInstance({
      method,
      url,
      data,
      responseType: "blob",
    });

    updateResults((previous) => ({
      ...previous,
      [key]: { status: "success", data: response.data },
    }));

    download(response.data, key);

    return response;
  } catch (error: any) {
    updateResults((previous) => ({
      ...previous,
      [key]: { status: "error", reason: error },
    }));

    if (onErrorCallback) onErrorCallback(error, key, fileName);
    throw error;
  } finally {
    updateProgresses((previous) => ({ ...previous, [key]: false }));
  }
};

/**
 * useHeadlessDownloader (Headless File Downloader)
 * 
 * This hook is used to download multitple files as Blob.
 * 
 * @param {Record} input - Record containing properties needed to make overall download request
 * @param {boolean} input.enable - Enables or disables the triggering of the request
 * @param {DownloadQuery} input.query - A key-value pair that takes a unique id as the key of the download request and **DownloadQueryData**
     - **DownloadQueryData** consists of:
        > - url: which is the url of the request
        > - method: which can be either GET or POST. can be extended but geneally, downloads are normally in only these two methods
        > - data: an optional data payload for when the method is POST
 * @param {string} input.fileName - A string or function that return a string
        > - if it is just a string, all downloads will have that file name.
        > - if it is a function, each download will dynamically generate a file name based on what the function does. 
        >> - the function takes in the key we set for each request when creating the overral query object.
 * @param {boolean} input.swrOptions - **(Optional)** this is basically the SWR Options we pass in a normal SWR function. it takes all the SWR options
 * @param {(data: Blob, key: string, fileName: string) => void} input.onFileDownloadSuccess -  **(Optional)** this callback fired when a file download was successful
 * @param {(error: any, key: string, fileName: string) => void} input.onFileDownloadError -  **(Optional)** this callback fired when a file download was not successful
 * @return {Record} `output` - Record containing properties resolved from running the overall request
 * @param {boolean} output.downloading - A boolean that returns whether or not the overall requests is still running
 * @param {Record<string, boolean>} output.progresses - An object that returns the key-value pair of the request key and a boolean of whether that request is completed or not
 * @param {Record<string, DownloadResult>} output.results - An object that returns the key-value pair of the request key and an object that contains the **DownloadResult**
    - **DownloadResult** consists of:
        > - status: either **success** or **error**
        > - data?: the data downloaded as a blob. only available when status is **success**
        > - reason?: the reason for an error. evidently, only availale when status is **error**
 * @param {SettledFileDownloadResponse} output.apiResults - An array of all the files downloaded as blobs
    - It is an array and not a key-value pair because the request is batched and run in a Promise.allSettled.
    - To get the key-value pair, use **results** instead
 * @param {any} output.apiError - api error returned from the Promise.allSettled call
    - This is not the same as the error you get from each request that might fail
    - This can be an Internal Server error or any other error unforseen error.
    - To get individual request error, use **results** instead
 * @param {KeyedMutator<SettledFileDownloadResponse>} output.mutate - A retriggerable async function that can be used to re run the overall api again. **it comes from SWR**
 * @param {() => void} output.reset - A clean up function that can and should be used to remove all references to the old call when unmounting or when done with the download process
 * 
 */
const useHeadlessDownloader = ({
  enable,
  query,
  fileName,
  swrOptions,
  onFileDownloadSuccess,
  onFileDownloadError,
}: UseHeadlessDownloadProps): UseHeadlessDownloadReturnData => {
  const { api } = useContext(AxiosContext);

  const [error, setError] = useState<any>(null);
  const [progresses, setProgresses] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, DownloadResult>>({});

  const key = useMemo(
    () => (enable ? Object.keys(query).reduce((accum, key) => accum.concat(`%${key}`), "use-headless-downloader") : ""),
    [query, enable],
  );

  const download = useCallback(
    (blob: Blob, key: string) => {
      downloadProgrammatically(
        blob,
        key,
        typeof fileName === "function" ? fileName(key) : fileName,
        onFileDownloadSuccess,
        onFileDownloadError,
      );
    },
    [fileName, onFileDownloadSuccess, onFileDownloadError],
  );

  const request = useCallback(async (): Promise<SettledFileDownloadResponse> => {
    try {
      const queries: Record<number, { key: string; query: Promise<{ data: Blob }> }> = {};

      for (const [index, [key, entry]] of Object.entries(query).entries()) {
        queries[index] = {
          key,
          query: runFileApiDownload(
            entry,
            key,
            typeof fileName === "function" ? fileName(key) : fileName,
            api,
            setProgresses,
            setResults,
            download,
          ),
        };
      }

      const response = await Promise.allSettled(Object.values(queries).map(({ query }) => query));
      return response;
    } catch (error: any) {
      setError(error);
      return [];
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  const { data: apiResults, mutate } = useSWR<SettledFileDownloadResponse>(enable ? (key as any) : null, request, {
    ...swrOptions,
    revalidateOnFocus: false,
  });

  const cleanUp = (): void => {
    setError(null);
    setProgresses({});
    setResults({});
  };

  return {
    downloading: Object.values(progresses).some((progress) => progress),
    progresses,
    results,
    apiResults: apiResults ?? [],
    apiError: error,
    mutate,
    reset: cleanUp,
  };
};

export default useHeadlessDownloader;
