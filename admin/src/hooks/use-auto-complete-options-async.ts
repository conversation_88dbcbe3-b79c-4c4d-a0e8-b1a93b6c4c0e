import {
  All<PERSON><PERSON>s,
  <PERSON>ricRecord,
  NonEmptyArray,
  pickFromArrayOfRecords,
  px,
  UseTriggerRequestProps,
} from "@rubiconcarbon/frontend-shared";
import { UseAutoCompleteOptionsReturnEntry } from "./use-auto-complete-options";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useDebounce } from "react-use";
import { ReactNode, useMemo } from "react";

type UseAutoCompleteOptionsAsync<
  Transformed extends GenericRecord,
  ResponseType,
  RequestBody extends GenericRecord = GenericRecord,
  PathParams extends GenericRecord = GenericRecord,
  QueryParams extends GenericRecord & { q?: string } = GenericRecord & { q?: string },
> = {
  q: string;
  keys: NonEmptyArray<AllKeys<Transformed>>;
  label: (entry: Partial<Transformed>) => string;
  displayLabel?: (entry: Partial<Transformed>) => ReactNode;
  value: (entry: Partial<Transformed>) => any;
  request: UseTriggerRequestProps<ResponseType, RequestBody, PathParams, QueryParams>;
  debounceMs?: number;
  preTransform?: (data: ResponseType) => Transformed[];
  postTransform?: (data: UseAutoCompleteOptionsReturnEntry[]) => UseAutoCompleteOptionsReturnEntry[];
};

/**
 * `useAutoCompleteOptionsAsync` fetches and transforms data asynchronously for an autocomplete component based on user input.
 */
const useAutoCompleteOptionsAsync = <
  Transformed extends GenericRecord<string, any>,
  ResponseType,
  RequestBody extends GenericRecord = GenericRecord,
  PathParams extends GenericRecord = GenericRecord,
  QueryParams extends GenericRecord & { q?: string } = GenericRecord & { q?: string },
>({
  q,
  keys,
  label,
  displayLabel,
  value,
  request,
  debounceMs = 300,
  preTransform = (data: ResponseType): Transformed[] => data as Transformed[],
  postTransform = (data: UseAutoCompleteOptionsReturnEntry[]): UseAutoCompleteOptionsReturnEntry[] => data,
}: UseAutoCompleteOptionsAsync<
  Transformed,
  ResponseType,
  RequestBody,
  PathParams,
  QueryParams
>): UseAutoCompleteOptionsReturnEntry[] => {
  const { url, method, pathParams, queryParams, requestBody, swrOptions } = request;

  const { data, trigger } = useTriggerRequest<ResponseType, RequestBody, PathParams, QueryParams>({
    url,
    method,
    pathParams,
    queryParams: {
      ...(queryParams ?? {}),
      q,
    } as QueryParams,
    requestBody,
    swrOptions,
  });

  const options = useMemo(
    () =>
      data
        ? postTransform(
            pickFromArrayOfRecords(preTransform(data), keys as any).map((entry) => ({
              label: label(entry),
              ...{ ...px({ displayLabel: !!displayLabel && displayLabel(entry) }, [false, null, undefined]) },
              value: value(entry),
            })),
          )
        : [],
    [data, displayLabel, keys, label, postTransform, preTransform, value],
  );

  useDebounce(
    () => {
      if (q) setTimeout(async () => await trigger());
    },
    debounceMs,
    [q],
  );

  return q ? options : [];
};

export default useAutoCompleteOptionsAsync;
