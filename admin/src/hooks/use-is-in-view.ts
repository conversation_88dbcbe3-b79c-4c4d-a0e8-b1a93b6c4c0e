import { RefObject, useEffect, useState } from "react";

type IntersectionOptions = {
  threshold?: number | number[];
  rootMargin?: string;
};

const useIsInView = (ref: RefObject<HTMLElement>, options: IntersectionOptions = {}): boolean => {
  const [isInView, setIsInView] = useState<boolean>(false);

  useEffect(() => {
    const element = ref.current;
    const { threshold = 0, rootMargin = "0px" } = options;

    if (!element) return;

    const intersectObserver = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold, rootMargin },
    );

    intersectObserver.observe(element);

    return (): void => intersectObserver.unobserve(element);
  }, [ref, options]);

  return isInView;
};

export default useIsInView;
