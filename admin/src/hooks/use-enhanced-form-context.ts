import { deepEqual } from "@rubiconcarbon/frontend-shared";
import { useFormContext, FieldValues, Path, PathValue, UseFormReturn } from "react-hook-form";
import { useCallback } from "react";
import { UseEnhancedFormReturn } from "./use-enhanced-form";

/**
 * A hook that wraps useFormContext and provides a smart setValue
 * that prevents redundant updates when used inside form components.
 */
export function useEnhancedFormContext<
  TFieldValues extends FieldValues = FieldValues,
>(): UseEnhancedFormReturn<TFieldValues> {
  // Get the original form context methods
  const methods = useFormContext<TFieldValues>();
  const { setValue, getValues } = methods;

  // Create smart setValue that avoids unnecessary updates
  const smartSetValue = useCallback(
    <TFieldName extends Path<TFieldValues>>(
      name: TFieldName,
      value: PathValue<TFieldValues, TFieldName>,
      options?: {
        shouldValidate?: boolean;
        shouldDirty?: boolean;
        shouldTouch?: boolean;
      },
    ) => {
      // Get current value at this path
      const currentValue = getValues(name);

      // Only update if the value is different
      if (!deepEqual(currentValue, value)) {
        setValue(name, value, options);
      }
    },
    [setValue, getValues],
  );

  // Return all original methods plus our smart setValue one
  return {
    ...methods,
    smartSetValue,
  };
}
