import { useState, useEffect } from "react";
import { useDataPolling } from "@rubiconcarbon/frontend-shared";

type UseRefreshableS3LinkProps<R> = {
  seedLinks: R;
  fetchApiUrl: string;
  immediately?: boolean;
  every: number;
  linkProps: string[];
};

const useRefreshableS3Link = <T, R = Record<string, string>>({
  seedLinks,
  fetchApiUrl,
  immediately = false,
  every,
  linkProps,
}: UseRefreshableS3LinkProps<R>): R => {
  const [refreshedLinks, setRefreshedLinks] = useState<R>(seedLinks);
  const [startNewLinkPolling, setStartNewLinkPolling] = useState<boolean>(immediately);
  const { data, error, isLoading } = useDataPolling<T>(fetchApiUrl, { engage: startNewLinkPolling, every });

  useEffect(
    () => {
      if (every === -1) return;

      const timer = setTimeout(
        () => {
          setStartNewLinkPolling(true);
        },
        immediately ? 0 : every,
      );

      return (): void => {
        setStartNewLinkPolling(false);
        clearTimeout(timer);
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [immediately, every, setStartNewLinkPolling],
  );

  useEffect(
    () => {
      if (!error && !isLoading && !!data)
        setRefreshedLinks(
          linkProps.reduce((accum, key) => {
            (accum as any)[key] = (data as any)[key];
            return accum;
          }, {} as R),
        );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data, setRefreshedLinks],
  );

  return refreshedLinks;
};

export default useRefreshableS3Link;
