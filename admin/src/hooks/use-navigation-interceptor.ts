import { Nullable } from "@rubiconcarbon/frontend-shared";
import { useCallback, useEffect, useRef, useState } from "react";
import { useAppPathname, useAppRouter } from "./router-hooks";

type NavigationInterceptorProps = {
  shouldIntercept?: (url: string) => boolean;
  debounceTime?: number;
};

type NavigationInterceptorResult = {
  navigationUrl: Nullable<string>;
  navigating: boolean;
  continueNavigation: () => void;
  cancelNavigation: () => void;
};

/**
 * A hook that intercepts navigation in Next.js App Router
 * Works with Next's router, Link components, and browser back/forward actions
 */
const useNavigationInterceptor = ({
  shouldIntercept = (): boolean => true,
  debounceTime = 300,
}: NavigationInterceptorProps = {}): NavigationInterceptorResult => {
  const router = useAppRouter();
  const currentPathname = useAppPathname();

  const [navigationUrl, setNavigationUrl] = useState<Nullable<string>>(null);
  const [navigating, setNavigating] = useState<boolean>(false);

  const pendingNavigation = useRef<
    Nullable<{
      url: string;
      handler: () => void;
    }>
  >(null);

  const timeoutRef = useRef<Nullable<NodeJS.Timeout>>(null);
  const isProcessingNavigation = useRef<boolean>(false);

  // Helper function to intercept navigation
  const interceptNavigation = useCallback(
    (url: string, handler: () => void): void => {
      // If we're already processing a navigation, don't start another one
      if (isProcessingNavigation.current) {
        return;
      }

      // Debounce navigation attempts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set pending navigation
      pendingNavigation.current = { url, handler };

      // Set timeout for debounce
      timeoutRef.current = setTimeout(() => {
        if (!pendingNavigation.current) return;

        isProcessingNavigation.current = true;
        setNavigationUrl(pendingNavigation.current.url);
        setNavigating(true);
      }, debounceTime);
    },
    [debounceTime],
  );

  // Clean up timeout on unmount
  useEffect(() => {
    return (): void => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Handle browser's forward/back actions
  useEffect(() => {
    const handlePopState = (event: PopStateEvent): void => {
      // Prevent the default behavior
      event.preventDefault();

      // Get the URL the user is trying to navigate to via browser back/forward
      const targetUrl = window.location.pathname + window.location.search + window.location.hash;

      // Don't intercept if we're already on this URL or shouldn't intercept
      if (targetUrl === currentPathname || !shouldIntercept(targetUrl)) {
        return;
      }

      // Intercept navigation
      interceptNavigation(targetUrl, () => {
        // Let Next.js router handle the actual navigation to preserve App Router behavior
        router.push(targetUrl);
      });
    };

    // Add event listener for popstate (browser back/forward)
    window.addEventListener("popstate", handlePopState);

    return (): void => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [currentPathname, interceptNavigation, router, shouldIntercept]);

  // Intercept clicks on anchor tags
  useEffect(() => {
    const handleClick = (event: MouseEvent): void => {
      // Only process link clicks with left mouse button and no modifier keys
      if (event.button !== 0 || event.metaKey || event.ctrlKey || event.shiftKey) {
        return;
      }

      // Find closest anchor tag
      let element = event.target as HTMLElement | null;
      let href: string | null = null;

      while (element && !href) {
        if (element.tagName === "A" && (element as HTMLAnchorElement).href) {
          href = (element as HTMLAnchorElement).href;
          break;
        }
        element = element.parentElement;
      }

      // Not a link or no href
      if (!element || !href) {
        return;
      }

      // Check if this is an internal link (same origin)
      try {
        const url = new URL(href);
        const isInternalLink = url.origin === window.location.origin;

        if (!isInternalLink) {
          // Don't intercept external links
          return;
        }

        const targetUrl = url.pathname + url.search + url.hash;

        // Don't intercept if we're already on this URL or shouldn't intercept
        if (targetUrl === currentPathname || !shouldIntercept(targetUrl)) {
          return;
        }

        // Prevent default link behavior
        event.preventDefault();

        // Intercept navigation
        interceptNavigation(targetUrl, () => {
          // Let Next.js router handle the navigation to preserve App Router behavior
          router.push(targetUrl);
        });
      } catch (error) {
        // Invalid URL, let browser handle it
        console.error("Error parsing URL:", error);
      }
    };

    // Add event listener for clicks
    document.addEventListener("click", handleClick);

    return (): void => {
      document.removeEventListener("click", handleClick);
    };
  }, [currentPathname, interceptNavigation, router, shouldIntercept]);

  // Override Next.js router methods
  useEffect(() => {
    // Store original methods
    const originalPush = router.push;
    const originalReplace = router.replace;
    const originalBack = router.back;
    const originalForward = router.forward;

    // Override router.push
    router.push = (href: string, options?: any): void => {
      if (!shouldIntercept(href)) {
        return originalPush(href, options);
      }

      interceptNavigation(href, () => {
        originalPush(href, options);
      });
    };

    // Override router.replace
    router.replace = (href: string, options?: any): void => {
      if (!shouldIntercept(href)) {
        return originalReplace(href, options);
      }

      interceptNavigation(href, () => {
        originalReplace(href, options);
      });
    };

    // Override router.back
    router.back = (): void => {
      interceptNavigation("back", () => {
        originalBack();
      });
    };

    // Override router.forward
    router.forward = (): void => {
      interceptNavigation("forward", () => {
        originalForward();
      });
    };

    // Clean up: restore original methods when component unmounts
    return (): void => {
      router.push = originalPush;
      router.replace = originalReplace;
      router.back = originalBack;
      router.forward = originalForward;
    };
  }, [interceptNavigation, router, shouldIntercept]);

  // Function to continue navigation
  const continueNavigation = useCallback(() => {
    if (pendingNavigation.current) {
      // Execute the navigation handler
      pendingNavigation.current.handler();

      // Reset state
      setNavigating(false);
      setNavigationUrl(null);
      pendingNavigation.current = null;
      isProcessingNavigation.current = false;
    }
  }, []);

  // Function to cancel navigation
  const cancelNavigation = useCallback(() => {
    // Reset state without performing navigation
    setNavigating(false);
    setNavigationUrl(null);
    pendingNavigation.current = null;
    isProcessingNavigation.current = false;

    // If we intercepted a back/forward browser action, we need to update the URL
    // to match the current route since we prevented the browser's default behavior
    if (window.location.pathname !== currentPathname) {
      // Update browser URL without triggering navigation
      window.history.replaceState(null, "", currentPathname);
    }
  }, [currentPathname]);

  return {
    navigationUrl,
    navigating,
    continueNavigation,
    cancelNavigation,
  };
};

export default useNavigationInterceptor;
