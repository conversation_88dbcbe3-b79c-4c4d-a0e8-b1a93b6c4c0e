"use client";

import { Nullable } from "@rubiconcarbon/frontend-shared";
import { ReadonlyURLSearchParams, usePathname, useRouter, useSearchParams } from "next/navigation";

/**
 * Use this hook to get the current pathname
 * @returns The current pathname string
 */
export function useAppPathname(): string {
  return usePathname() ?? "";
}

/**
 * Use this hook to get the router instance
 * @returns The Next.js router instance
 */
export function useAppRouter(): ReturnType<typeof useRouter> {
  return useRouter();
}

/**
 * Use this hook to get the search params
 * @returns URLSearchParams object
 */
export function useAppSearchParams(): Nullable<ReadonlyURLSearchParams> {
  return useSearchParams();
}

/**
 * Create a URLSearchParams object with the provided params
 * @param params Object containing key-value pairs for search params
 * @returns URLSearchParams object
 */
export function createSearchParams(
  params: Record<string, string | string[] | number | null | undefined>,
): URLSearchParams {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value === null || value === undefined) return;

    if (Array.isArray(value)) {
      value.forEach((v) => searchParams.append(key, String(v)));
    } else {
      searchParams.append(key, String(value));
    }
  });

  return searchParams;
}

/**
 * Creates a URL by appending search params to a path
 * @param path Base path
 * @param params Search params to append
 * @returns Full URL string
 */
export function createUrl(
  path: string,
  params: Record<string, string | string[] | number | null | undefined> = {},
): string {
  const searchParams = createSearchParams(params);
  const searchParamsString = searchParams.toString();
  const queryString = searchParamsString ? `?${searchParamsString}` : "";

  return `${path}${queryString}`;
}
