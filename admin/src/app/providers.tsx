"use client";

import React from "react";
import { StyledEngineProvider } from "@mui/material/styles";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { AuthProvider } from "@/providers/auth-provider";
import { AxiosProvider } from "@/providers/axios-provider";
import SnackbarProvider from "@/providers/themed-snackbar-provider";
import ThemeProvider from "@/providers/theme-provider";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import FeaturesProvider from "@/providers/features-provider";
import { NavigationMenuProvider } from "@/providers/navigation-menu-provider";
import StoreProvider from "@/providers/store-provider";
import { LoggingProvider } from "@/providers/logging";
import { TelemetryProvider } from "@/providers/telemetry";
import InactivityChecker from "@/components/inactivity-checker/inactivity-checker";
import ChatbotIcon from "@/components/chatbot/chatbot-icon";
import "@/utils/reflect-metadata";

export function Providers({ children }: { children: React.ReactNode }): JSX.Element {
  return (
    <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ""}>
      <TelemetryProvider />
      <LoggingProvider />
      <AuthProvider>
        <InactivityChecker>
          <StoreProvider>
            <StyledEngineProvider injectFirst>
              <ThemeProvider>
                <SnackbarProvider>
                  <AxiosProvider>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <FeaturesProvider extraFlags={[]}>
                        <NavigationMenuProvider>
                          {children}
                          <ChatbotIcon />
                        </NavigationMenuProvider>
                      </FeaturesProvider>
                    </LocalizationProvider>
                  </AxiosProvider>
                </SnackbarProvider>
              </ThemeProvider>
            </StyledEngineProvider>
          </StoreProvider>
        </InactivityChecker>
      </AuthProvider>
    </GoogleOAuthProvider>
  );
}
