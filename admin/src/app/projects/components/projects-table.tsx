import React, { useState, useEffect, useCallback, useMemo } from "react";
import useAuth from "@providers/auth-provider";
import { PermissionEnum, AdminProjectQueryResponse } from "@rubiconcarbon/shared-types";
import { stringComparator } from "@utils/comparators/comparator";
import { ProjectData, mapProjectsData } from "@models/project-data";
import { MISSING_DATA, RCT_STANDARD, SUSPENDED } from "@constants/constants";
import { Box, Stack, Tooltip } from "@mui/material";
import integerFormat from "@/utils/formatters/integer-format";
import { Maybe, Undefinable } from "@rubiconcarbon/frontend-shared";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useFileDownloader, { DownloadResult } from "@hooks/use-file-downloader";
import COLORS from "@components/ui/theme/colors";
import useNavigation from "@/hooks/use-navigation";
import { ProjectImageItem, ProjectImageResponse, ProjectImageType } from "../types/project-image";
import Img from "@components/ui/img/img";
import InfoButton from "@components/ui/info-button/info-button";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import ProgressBar, { ProgressBarData } from "@components/ui/charts/progress-bar";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import ProjectSummary from "./project-summary";
import TableBox from "@components/ui/table-box/table-box";
import ProjectImageViewer from "./project-image-viewer";
import { ProjectImageTypeToDimensions } from "../[project-id]/image-management/constants/project-image-constants";

const disabledBtn = {
  backgroundColor: "white",
};

const enabledBtn = {
  backgroundColor: "white",
  color: COLORS.rubiconGreen,
  "&:hover": {
    backgroundColor: "white",
    color: COLORS.rubiconGreen,
  },
};

const actionsStyle = {
  borderLeft: "1px solid",
  borderColor: "rgba(224, 224, 224, 1)",
  height: "35px",
};

const BLOB_LOCATION = process.env.NEXT_PUBLIC_BLOB_STORAGE;

const getIsSuspended = (inputMap: Map<string, boolean>): string => {
  return inputMap.get("suspended") ? "Yes" : "No";
};

export default function ProjectsTable({
  projects: projectsResponse,
}: {
  projects: AdminProjectQueryResponse;
}): JSX.Element {
  const { user: loginUser } = useAuth();
  const { pushToPath } = useNavigation();
  const { enqueueError, enqueueSuccess } = useSnackbarVariants();
  const { download } = useFileDownloader();

  const [currentCarouselProjectId, setCurrentCarouselProjectId] = useState<Undefinable<string>>(undefined);
  const [projectImages, setProjectImages] = useState<ProjectImageResponse[]>();
  const [projects, setProjects] = useState<ProjectData[]>();

  const canManageImages = useMemo(() => loginUser?.hasPermission(PermissionEnum.PROJECTS_WRITE), [loginUser]);

  const getProjectImages = useCallback(
    (inputMap: Map<string, string>): JSX.Element => {
      const projectId = inputMap.get("id");

      const urls = [inputMap.get("previewImageUrl"), inputMap.get("mapImageUrl"), inputMap.get("illustrationImageUrl")];
      const [previewUrl, mapUrl, illustrationUrl] = urls;

      const hasImages = !!urls?.some((url) => !!url);
      const hasNoImages = !!urls?.every((url) => !url);

      const openProjectImageManagement = (): boolean => pushToPath(`${projectId}/image-management`);

      const onClick = (): void => {
        if (hasImages) {
          setProjectImages([
            {
              image_type: "profile",
              url: previewUrl,
            },
            {
              image_type: "map",
              url: mapUrl,
            },
            {
              image_type: "illustration",
              url: illustrationUrl,
            },
          ]);
          setCurrentCarouselProjectId(projectId);
        } else if (hasNoImages && canManageImages) openProjectImageManagement();
      };
      return (
        <Tooltip title={hasNoImages && !canManageImages ? "Insufficient permissions" : null}>
          <Box onClick={onClick} sx={{ cursor: hasNoImages && !canManageImages ? "no-drop" : "pointer" }}>
            <Img
              source={
                previewUrl ? `${previewUrl?.startsWith("public-image") ? `${BLOB_LOCATION}/` : ""}${previewUrl}` : ""
              }
              size={70}
              style={{ backgroundColor: "#D3D3D373" }}
            />
          </Box>
        </Tooltip>
      );
    },
    [canManageImages, pushToPath],
  );

  const downloadPDFHandler = useCallback(
    async (projectId?: string, registryProjectId?: string) => {
      if (projectId) {
        const result: DownloadResult = await download(
          `${registryProjectId}`,
          `/reporting/projects/report?project_id=${projectId}`,
        );
        if (result?.isSuccess) {
          enqueueSuccess("PDF exported successfully");
        } else {
          enqueueError("Unable to download PDF");
        }
      }
    },
    [download, enqueueSuccess, enqueueError],
  );

  const getProjectActions = useCallback(
    (inputMap: Map<string, string | boolean>): JSX.Element => {
      const projectId = inputMap.get("id");
      const registryProjectId = inputMap.get("registryProjectId");
      const pdfReady = inputMap.get("pdfReady");
      return (
        <Stack direction="row" gap={2}>
          <InfoButton id={projectId?.toString() ?? ""} />
          <Box sx={actionsStyle}>
            <ActionButton
              style={pdfReady ? enabledBtn : disabledBtn}
              variant="text"
              isDisabled={!pdfReady}
              onClickHandler={() => downloadPDFHandler(projectId?.toString(), registryProjectId?.toString())}
            >
              <PictureAsPdfIcon sx={{ color: pdfReady ? COLORS.rubiconGreen : "lightGray" }} />
            </ActionButton>
          </Box>
        </Stack>
      );
    },
    [downloadPDFHandler],
  );

  const getProjectStatusBar = useCallback((inputMap: Map<string, number>): JSX.Element => {
    const allocated = inputMap.get("allocatedToBasket");
    const unallocated = inputMap.get("unallocatedToBasket");
    if (allocated === undefined || unallocated === undefined || allocated + unallocated === 0)
      return <p>{MISSING_DATA}</p>;
    const chartsColor = (allocated * 100) / (allocated + unallocated) > 90 ? COLORS.warning : COLORS.infoBlue;
    const chartsData: ProgressBarData = {
      topRange: 100,
      charts: [
        {
          name: "Allocated",
          value: allocated,
          total: allocated + unallocated,
          title: `Allocated (${integerFormat(allocated)}) | Total (${integerFormat(allocated + unallocated)})`,
        },
      ],
    };

    return (
      <Box sx={{ width: "300px", height: "65px", marginTop: "5px", marginBottom: "-10px" }}>
        <ProgressBar
          chartsData={chartsData}
          chartsStyle={{ height: "65px", width: "300px" }}
          chartsColor={chartsColor}
        />
      </Box>
    );
  }, []);

  const columnsDef: ColDef[] = [
    {
      columnName: "projectImages",
      displayName: "",
      formatter: {
        func: getProjectImages,
        inputFields: ["id", "previewImageUrl", "mapImageUrl", "illustrationImageUrl"],
        overrideMissingDataDisplay: true,
      },
      sortable: false,
      exportable: false,
    },
    {
      columnName: "name",
      displayName: "Project",
      comparator: stringComparator,
      formatter: {
        func: (x: any): JSX.Element => {
          return (
            <>
              <div>{x.get("name")}</div>
              <div style={{ color: "#a0a0a0" }}>
                {x.get("registryProjectId")}
                <Stack direction="row" gap={1}>
                  <Maybe condition={x.get("suspended") === true}>
                    <SuspendedChip />
                  </Maybe>
                </Stack>
              </div>
            </>
          );
        },
        inputFields: ["name", "registryProjectId", "suspended"],
      },
    },
    {
      columnName: "registryProjectId",
      displayName: "Registry ID",
      hide: true,
    },
    {
      columnName: "suspended",
      displayName: "Suspended",
      hide: true,
      exportFormatter: {
        func: getIsSuspended,
        inputFields: ["suspended"],
      },
    },
    {
      columnName: "projectType",
      displayName: "Type",
      comparator: stringComparator,
    },
    {
      columnName: "projectCategory",
      displayName: "Category",
      comparator: stringComparator,
    },
    {
      columnName: "projectLocation",
      displayName: "Country",
    },
    {
      columnName: "credits",
      displayName: "Total",
      formatter: {
        func: getProjectStatusBar,
        inputFields: ["allocatedToBasket", "unallocatedToBasket"],
        overrideMissingDataDisplay: true,
      },
      sortable: false,
      exportable: false,
    },
    {
      columnName: "id",
      displayName: "",
      formatter: {
        func: getProjectActions,
        inputFields: ["id", "pdfReady", "registryProjectId"],
      },
      exportable: false,
      sortable: false,
    },
  ];

  useEffect(() => {
    if (projectsResponse?.data) {
      const sortedProjects = projectsResponse?.data?.sort((a, b) =>
        (a.registryName ?? "").localeCompare(b.registryName ?? ""),
      );
      setProjects(mapProjectsData(sortedProjects));
    }
  }, [projectsResponse?.data]);

  const popExpandContent = (row: ProjectData): JSX.Element => {
    return <ProjectSummary projectId={row.id} />;
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = projectsResponse?.data
      .filter(
        (row) =>
          row.name?.toUpperCase().includes(searchString) ||
          row.registryProjectId?.toUpperCase().includes(searchString) ||
          row.registryName?.toUpperCase().includes(searchString) ||
          row.bookChartDisplayGroup?.toUpperCase().includes(searchString) ||
          row?.country?.name?.toUpperCase().includes(searchString) ||
          row?.projectType?.category?.toUpperCase().includes(searchString) ||
          row?.projectType?.type?.toUpperCase().includes(searchString) ||
          (row?.suspended === true && SUSPENDED.includes(searchString.toUpperCase())) ||
          (row?.rctStandard === true && RCT_STANDARD.includes(searchString.toUpperCase())),
      )
      .sort((a, b) => (a.registryName ?? "").localeCompare(b.registryName ?? ""));

    setProjects(mapProjectsData(filteredData));
  };

  const newProjectHandler = (): void => {
    pushToPath("create-project");
  };

  const newVintageHandler = (): void => {
    pushToPath("create-vintage");
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <Stack direction="row" gap={0.5}>
        <ActionButton
          onClickHandler={newProjectHandler}
          requiredPermission={PermissionEnum.PROJECTS_WRITE}
          style={{ width: "170px" }}
        >
          Create Project
        </ActionButton>
        <ActionButton
          onClickHandler={newVintageHandler}
          requiredPermission={PermissionEnum.PROJECTS_WRITE}
          style={{ width: "160px" }}
        >
          Create Vintage
        </ActionButton>
      </Stack>
    );
  };

  return (
    <>
      <TableBox>
        {projects && (
          <EnhancedTable
            name={"projects_info"}
            columnsDef={columnsDef}
            exportable={loginUser?.hasPermission(PermissionEnum.PROJECTS_EXPORT_CSV)}
            data={projects}
            rowsCountPerPage={100}
            expandedContent={popExpandContent}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "name", order: SortOrder.ASC }}
          />
        )}
      </TableBox>
      <ProjectImageViewer
        projectId={currentCarouselProjectId}
        images={projectImages
          ?.filter(({ url }) => !!url)
          ?.reduce(
            (record, { image_type, url }) => ({
              ...record,
              [image_type]: {
                url,
                dimensions: ProjectImageTypeToDimensions?.[image_type],
              },
            }),
            {} as Record<ProjectImageType, ProjectImageItem>,
          )}
        onClose={() => setCurrentCarouselProjectId(undefined)}
      />
    </>
  );
}
