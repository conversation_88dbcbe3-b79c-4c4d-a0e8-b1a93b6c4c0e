import GenericCarousel from "@components/ui/generic-carousel/generic-carousel";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import Img from "@components/ui/img/img";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import useNavigation from "@/hooks/use-navigation";
import { Stack, Typography, Button } from "@mui/material";
import { capitalize } from "lodash";
import { ProjectImageItem, ProjectImageType } from "../types/project-image";
import useAuth from "@providers/auth-provider";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

import classes from "../styles/project-image-viewer.module.scss";

type ProjectImageViewerProps = {
  projectId?: string;
  images?: Partial<Record<ProjectImageType, ProjectImageItem>>;
  onClose: () => void;
};

const ProjectImageViewer = ({ projectId, images = {}, onClose }: ProjectImageViewerProps): JSX.Element => {
  const { pushToPath } = useNavigation();
  const { user } = useAuth();

  const slides = Object.entries(images)?.map(([key, data]) => ({ key, data }));

  return (
    <GenericDialog
      open={!!projectId}
      fullscreen
      dismissIcon={<MatIcon value="close" variant="round" sx={{ color: "white" }} />}
      dividers={false}
      classes={{
        root: classes.Root,
        title: classes.Title,
        content: classes.Content,
      }}
      onClose={onClose}
    >
      <GenericCarousel
        value={slides?.at(0)?.key ?? ""}
        slides={slides}
        swipeable={{
          threshold: 300,
        }}
        classes={{
          root: classes.Carousel,
          sliderRoot: classes.SliderRoot,
          navigator: classes.Navigator,
          indicatorRoot: classes.IndicatorRoot,
          activeIndicator: classes.ActiveIndicator,
        }}
        renderSlide={(data: ProjectImageItem, key: string) => (
          <Stack key={key} position="relative">
            <Img source={data?.url} width={data?.dimensions[0]} height={data?.dimensions[1]} scale={1.5} />
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{
                position: "absolute",
                bottom: 0,
                width: "100%",
                height: 40,
                padding: "5px 7px",
                backgroundColor: "#000000B2",
                borderBottomLeftRadius: "5px",
                borderBottomRightRadius: "5px",
              }}
            >
              <Typography sx={{ fontSize: 12, color: "white" }}>{capitalize(key)} image</Typography>
              <Maybe condition={!!user?.hasPermission(PermissionEnum.PROJECTS_WRITE)}>
                <Button
                  variant="contained"
                  sx={{ borderRadius: 1, height: 30, backgroundColor: "#9AB79A", color: "white" }}
                  onClick={() => pushToPath(`${projectId}/image-management`)}
                >
                  <Typography sx={{ fontSize: 12, textTransform: "capitalize" }}>Edit Images</Typography>
                </Button>
              </Maybe>
            </Stack>
          </Stack>
        )}
        renderIndicator={(data: ProjectImageItem) => <Img source={data?.url} size={70} />}
      />
    </GenericDialog>
  );
};

export default ProjectImageViewer;
