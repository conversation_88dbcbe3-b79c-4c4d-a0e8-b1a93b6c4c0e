import { Grid, Box } from "@mui/material";
import { PermissionEnum, uuid } from "@rubiconcarbon/shared-types";
import ButtonGroup from "@components/ui/button-group/button-group";
import { useRouter } from "next/navigation";
import ProjectVintages from "./project-vintages";

interface ProjectSummaryProps {
  projectId: uuid;
}

export default function ProjectSummary(props: ProjectSummaryProps): JSX.Element {
  const { push } = useRouter();

  const { projectId } = props;

  function viewDetailsHandler(): void {
    push(`/projects/${projectId}`);
  }

  const buttons = [
    {
      id: "viewDetails",
      name: "view details",
      handler: viewDetailsHandler,
      tooltip: "Show more details",
      requiredPermission: PermissionEnum.PROJECTS_READ,
    },
  ];

  return (
    <Grid container spacing={0}>
      <Grid item xs={12}>
        <Box mt={2}>
          <ProjectVintages projectId={projectId} />
        </Box>
      </Grid>
      <Grid item xs={12}>
        <Box mt={2} mb={2}>
          <ButtonGroup buttons={buttons} />
        </Box>
      </Grid>
    </Grid>
  );
}
