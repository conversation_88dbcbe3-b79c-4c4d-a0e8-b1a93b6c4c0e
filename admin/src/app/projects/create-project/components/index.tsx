"use client";

import Page from "@components/layout/containers/page";
import { CountryResponse, ProjectTypeResponse } from "@rubiconcarbon/shared-types";
import NewProject from "./new-project";

const CreateProject = ({
  types,
  countries,
}: {
  types: ProjectTypeResponse[];
  countries: CountryResponse[];
}): JSX.Element => {
  return (
    <Page>
      <NewProject types={types} countries={countries} />
    </Page>
  );
};

export default CreateProject;
