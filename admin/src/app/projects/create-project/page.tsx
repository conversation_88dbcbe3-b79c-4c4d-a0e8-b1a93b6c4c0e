import { AuthorizeServer } from "@app/authorize-server";
import { CountryQueryResponse, PermissionEnum, ProjectTypeQueryResponse } from "@rubiconcarbon/shared-types";
import CreateProject from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";

/**
 * Create Project Page
 *
 * This is a server component that renders the Create Project page
 */
export default async function CreateProjectPage(): Promise<JSX.Element> {
  const typesPromise = withErrorHandling(async () =>
    baseApiRequest<ProjectTypeQueryResponse>(
      `admin/project-types?${generateQueryParams({ limit: SERVER_PAGINATION_LIMIT })}`,
    ),
  );

  const countriesPromise = withErrorHandling(async () =>
    baseApiRequest<CountryQueryResponse>(`admin/countries?${generateQueryParams({ limit: SERVER_PAGINATION_LIMIT })}`),
  );

  const [types, countries] = await Promise.all([typesPromise, countriesPromise]);

  // Check if the result is a server error
  if (isValidElement(types)) return types;
  if (isValidElement(countries)) return countries;

  return (
    <AuthorizeServer permissions={[PermissionEnum.PROJECTS_WRITE]}>
      <CreateProject
        types={(types as ProjectTypeQueryResponse)?.data}
        countries={(countries as CountryQueryResponse)?.data}
      />
    </AuthorizeServer>
  );
}
