import { AuthorizeServer } from "@app/authorize-server";
import { AdminProjectQueryResponse, PermissionEnum, ProjectRelations } from "@rubiconcarbon/shared-types";
import Projects from "./components";
import { baseApiRequest, generateQueryParams } from "../libs/server";
import { isValidElement } from "react";
import { withErrorHandling } from "../data-server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Projects Page
 *
 * This is a server component that renders the Projects page
 */
export default async function ProjectsPage(): Promise<JSX.Element> {
  const projects = await withErrorHandling(async () =>
    baseApiRequest<AdminProjectQueryResponse>(
      `admin/projects?${generateQueryParams({
        offset: 0,
        limit: SERVER_PAGINATION_LIMIT,
        hasTrades: true,
        includeRelations: [
          ProjectRelations.PROJECT_TYPE,
          ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
          ProjectRelations.COUNTRY,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(projects)) return projects;

  return (
    <AuthorizeServer permissions={[PermissionEnum.PROJECTS_READ]}>
      <Projects projects={projects as AdminProjectQueryResponse} />
    </AuthorizeServer>
  );
}
