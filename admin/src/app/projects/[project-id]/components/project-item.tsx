import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import React, { useCallback, useContext, useEffect, useState } from "react";
import { Box, Grid, Card, CardActions, CardContent, CardMedia } from "@mui/material";
import { LoadingButton } from "@mui/lab";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ProjectVintages from "../../components/project-vintages";
import ProjectItemAccordion from "./project-item-accordion";
import ProjectItemDetails from "./project-item-details";
import ImagesRow from "@components/ui/images-row/images-row";
import BackButton from "@components/ui/back-button/back-button";

const buttonPDFStyle = {
  px: 2.5,
  height: "30px",
};

export default function ProjectItem({ project }: { project: AdminProjectResponse }): JSX.Element {
  const { api } = useContext(AxiosContext);
  const { updateBreadcrumbName } = useBreadcrumbs();
  const [downloading, setDownloading] = useState(false);
  const { enqueueError } = useSnackbarVariants();

  const projectId = project?.id;

  const downloadPDF = useCallback((): void => {
    // using Java Script method to get PDF file
    setDownloading(true);
    api
      .get(`/reporting/projects/report?project_id=${projectId}`, { responseType: "blob" })
      .then((response) => {
        const match = response.headers["content-disposition"].match(/filename="(.+)"$/);
        const fileURL = window.URL.createObjectURL(response.data);
        const alink = document.createElement("a");
        alink.href = fileURL;
        alink.download = decodeURI(match[1]);
        alink.click();
        setDownloading(false);
      })
      .catch((e) => {
        setDownloading(false);
        console.error("Failed to download.", e);
        enqueueError("Failed to download PDF.");
      });
  }, [api, projectId, enqueueError]);

  useEffect(() => {
    if (project) updateBreadcrumbName?.("Project Details", project.name);
  }, [project, updateBreadcrumbName]);

  if (!projectId) return <p>No Data</p>;

  const images = [
    { id: "image1", src: project?.squareImage1Url },
    { id: "image2", src: project?.squareImage2Url },
  ].filter((x) => x.src != null) as { id: string; src: string }[];

  const itemDetailsCard = project && (
    <React.Fragment>
      {project.previewImageUrl && (
        <CardMedia component="img" height="300" image={project.previewImageUrl} title={project.name}></CardMedia>
      )}
      <CardContent>
        <ProjectItemDetails project={project} />
        <ImagesRow images={images} />
        <Grid container spacing={0} mt={3}>
          <Grid item xs={12}>
            <BackButton />

            <Maybe condition={!!project.pdfReady}>
              <LoadingButton
                onClick={downloadPDF}
                loading={downloading}
                variant="outlined"
                sx={{ ...buttonPDFStyle, marginLeft: "10px" }}
              >
                Download
              </LoadingButton>
            </Maybe>
          </Grid>
        </Grid>
      </CardContent>
      <CardActions></CardActions>
    </React.Fragment>
  );

  return (
    <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
      <Box mt={4} sx={{ width: "98%" }}>
        <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
          {project && itemDetailsCard}
        </Card>
      </Box>
      <Box mt={4} sx={{ width: "98%" }}>
        {project && <ProjectVintages projectId={project.id} />}
      </Box>
      <Box mt={4} sx={{ width: "98%" }}>
        <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
          <ProjectItemAccordion project={project} />
        </Card>
      </Box>
    </Grid>
  );
}
