import { AdminProjectResponse } from "@rubiconcarbon/shared-types";
import { Grid } from "@mui/material";
import ItemDetails from "@components/ui/details/item-details";
import dateFormatter from "@/utils/formatters/date-formatter";
import LinksGroup from "@components/ui/links-group/links-group";

const creatLinks = (project: AdminProjectResponse): { name: string; path: string; tooltip: string }[] => {
  const links: { name: string; path: string; tooltip: string }[] = [];

  if (project?.registryLink) links.push({ name: "Registry", path: project?.registryLink, tooltip: "Registry" });

  if (project?.pddReportLink)
    links.push({ name: "PDD", path: project?.pddReportLink, tooltip: "Project Design Document" });

  if (project?.vvbReportLink)
    links.push({ name: "VVB", path: project?.vvbReportLink, tooltip: "Validation & Verification Bodies" });

  return links;
};

export default function ProjectItemDetails(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;

  if (!project) return <p>No Data</p>;

  const linksPresent = !!(project?.registryLink || project?.pddReportLink || project?.vvbReportLink);

  return (
    <Grid container spacing={0}>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Project developer:" value={project?.projectDeveloperName} />
      </Grid>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Registry name:" value={project?.registryName} />
      </Grid>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Date started:" value={dateFormatter(project?.startDate)} />
      </Grid>

      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Project location:" value={project?.country?.name} />
      </Grid>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Registry project ID:" value={project?.registryProjectId} />
      </Grid>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Date ended:" value={dateFormatter(project?.endDate)} />
      </Grid>
      <Grid item={true} lg={4} xs={12}>
        <ItemDetails label="Buffer category:" value={project?.bufferCategory?.name} />
      </Grid>

      {linksPresent && (
        <Grid item={true} xs={12} mt={2} mb={4}>
          <LinksGroup links={creatLinks(project)} />
        </Grid>
      )}
    </Grid>
  );
}
