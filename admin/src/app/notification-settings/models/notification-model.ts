import { NotificationSection } from "../types/notification";

export class NotificationModel {
  purchase: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  retirement: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  basket: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  project: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  "org and user": Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  trade: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  book: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  activity: Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
  "model portfolio": Pick<NotificationSection, "eventsSelected" | "cadencesSelected">;
}
