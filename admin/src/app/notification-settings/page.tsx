import { AuthorizeServer } from "@app/authorize-server";
import { NotificationSubscriptionsBulkResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import NotificationSettings from "./components";
import { NOTIFICATION_SUBSCRIPTIONS_API_URL } from "./constants/notification";
import { isValidElement } from "react";
import { baseApiRequest } from "../libs/server";

/**
 * Notification Settings Page
 *
 * This is a server component that fetches initial notification subscription data
 * and passes it to the client component
 */
export default async function NotificationSettingsPage(): Promise<JSX.Element> {
  // Fetch notification subscriptions data on the server
  const subscriptionsData = await withErrorHandling(async () =>
    baseApiRequest<NotificationSubscriptionsBulkResponse>(NOTIFICATION_SUBSCRIPTIONS_API_URL),
  );

  // Check if the result is a server error
  if (isValidElement(subscriptionsData)) return subscriptionsData;

  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <NotificationSettings initialData={subscriptionsData as NotificationSubscriptionsBulkResponse} />
    </AuthorizeServer>
  );
}
