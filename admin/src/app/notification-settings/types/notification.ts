import { NotificationCadence, NotificationEvent, PermissionEnum } from "@rubiconcarbon/shared-types";
import { UseControllerReturn } from "react-hook-form";
import { NotificationModel } from "../models/notification-model";

export type NotificationSectionType =
  | "purchase"
  | "retirement"
  | "basket"
  | "project"
  | "org and user"
  | "trade"
  | "book"
  | "activity"
  | "model portfolio";

export type NotificationEventPermissions = Partial<Record<NotificationEvent, PermissionEnum[]>>;

export type NotificationSection = {
  label: string;
  subLabel: string;
  events: NotificationEvent[];
  cadences: NotificationCadence[];
  eventsSelected: NotificationEvent[];
  cadencesSelected: NotificationCadence[];
  permissions: NotificationEventPermissions;
  controller?: UseControllerReturn<NotificationModel, any>;
};

export type NotificationSectionError = {
  events: boolean;
  cadences: boolean;
};

export type NotificationSections = Record<NotificationSectionType, NotificationSection>;

export type NotificationSectionsError = Record<NotificationSectionType, NotificationSectionError>;
