"use client";

import {
  Box,
  <PERSON>ton,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import {
  ChangeEvent,
  Fragment,
  MouseEvent,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import SectionHeader from "./section-header";
import {
  NotificationCadence,
  NotificationEvent,
  NotificationSubscriptionsBulkResponse,
  NotificationSubscriptionsRequest,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import { Control, ControllerRenderProps, useController, useForm } from "react-hook-form";
import { LoadingButton } from "@mui/lab";
import {
  NOTIFICATION_SUBSCRIPTIONS_API_URL,
  NotificationCadenceUILabel,
  NotificationEventUILabel,
} from "../constants/notification";
import useSWR from "swr";
import { NotificationModel } from "../models/notification-model";
import {
  NotificationSections,
  NotificationSectionsError,
  NotificationSectionType,
  NotificationSection,
} from "../types/notification";
import useAuth from "@providers/auth-provider";
import { Maybe, pickFromRecord } from "@rubiconcarbon/frontend-shared";
import Page from "@components/layout/containers/page";

import classes from "../styles/notification-settings.module.scss";

const SeedNotificationSectionsData: NotificationSections = {
  purchase: {
    label: "Customer Sales",
    subLabel: "Receive email notifications on sale-related transactions.",
    events: [NotificationEvent.PURCHASE_CREATED, NotificationEvent.PURCHASE_STATUS_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.PURCHASE_CREATED]: [PermissionEnum.CUSTOMER_SALES_READ],
      [NotificationEvent.PURCHASE_STATUS_UPDATED]: [PermissionEnum.CUSTOMER_SALES_READ],
    },
  },
  retirement: {
    label: "Retirements",
    subLabel: "Receive email notifications on retirement-related transactions.",
    events: [NotificationEvent.RETIREMENT_CREATED, NotificationEvent.RETIREMENT_STATUS_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.RETIREMENT_CREATED]: [PermissionEnum.RETIREMENTS_READ],
      [NotificationEvent.RETIREMENT_STATUS_UPDATED]: [PermissionEnum.RETIREMENTS_READ],
    },
  },
  basket: {
    label: "Portfolios",
    subLabel: "Receive email notifications on basket changes.",
    events: [
      NotificationEvent.BOOK_CREATED,
      NotificationEvent.BOOK_PRICE_UPDATED,
      NotificationEvent.BASKET_COMPOSITION_UPDATED,
    ],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.BOOK_CREATED]: [PermissionEnum.BOOKS_READ],
      [NotificationEvent.BOOK_PRICE_UPDATED]: [PermissionEnum.BOOKS_READ],
      [NotificationEvent.BASKET_COMPOSITION_UPDATED]: [PermissionEnum.BOOKS_READ],
    },
  },
  project: {
    label: "Projects",
    subLabel: "Receive email notifications on project changes.",
    events: [NotificationEvent.PROJECT_CREATED, NotificationEvent.PROJECT_BUFFER_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.PROJECT_CREATED]: [PermissionEnum.PROJECTS_READ],
      [NotificationEvent.PROJECT_BUFFER_UPDATED]: [PermissionEnum.PROJECTS_READ],
    },
  },
  "org and user": {
    label: "Organizations and Users",
    subLabel: "Receive email notifications on organization and user changes.",
    events: [NotificationEvent.ORGANIZATION_CREATED, NotificationEvent.USER_CREATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.ORGANIZATION_CREATED]: [PermissionEnum.ORGANIZATIONS_READ],
      [NotificationEvent.USER_CREATED]: [PermissionEnum.USERS_READ],
    },
  },
  trade: {
    label: "Trades",
    subLabel: "Receive email notifications on trade changes.",
    events: [NotificationEvent.TRADE_CREATED, NotificationEvent.TRADE_UPDATED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.TRADE_CREATED]: [PermissionEnum.TRADES_READ],
      [NotificationEvent.TRADE_UPDATED]: [PermissionEnum.TRADES_READ],
    },
  },
  book: {
    label: "Books",
    subLabel: "Receive email notifications on book changes.",
    events: [NotificationEvent.TRANSFER_EXECUTED],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY, NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.TRANSFER_EXECUTED]: [PermissionEnum.BOOKS_READ],
    },
  },
  activity: {
    label: "Customer Activities",
    subLabel: "Receive email notifications on customer activity changes.",
    events: [NotificationEvent.ACTIVITY_EVENT],
    cadences: [NotificationCadence.DAILY, NotificationCadence.WEEKLY],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.ACTIVITY_EVENT]: [PermissionEnum.USER_ACTIONS_VIEW_CUSTOMER],
    },
  },
  "model portfolio": {
    label: "Portfolio Sandbox",
    subLabel: "Receive email notifications on portfolio sandbox changes.",
    events: [NotificationEvent.MODEL_PORTFOLIO_CREATED, NotificationEvent.MODEL_PORTFOLIO_UPDATED],
    cadences: [NotificationCadence.REALTIME],
    eventsSelected: [],
    cadencesSelected: [],
    permissions: {
      [NotificationEvent.MODEL_PORTFOLIO_CREATED]: [PermissionEnum.MODEL_PORTFOLIOS_READ],
      [NotificationEvent.MODEL_PORTFOLIO_UPDATED]: [PermissionEnum.MODEL_PORTFOLIOS_READ],
    },
  },
};

const SeedNotificationSectionErrorData: NotificationSectionsError = {
  purchase: { events: false, cadences: false },
  retirement: { events: false, cadences: false },
  basket: { events: false, cadences: false },
  project: { events: false, cadences: false },
  "org and user": { events: false, cadences: false },
  trade: { events: false, cadences: false },
  book: { events: false, cadences: false },
  activity: { events: false, cadences: false },
  "model portfolio": { events: false, cadences: false },
};

const useSetControllerOnSections = (control: Control<NotificationModel>, sections: NotificationSections): void => {
  for (const [key, section] of Object.entries(sections)) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    section.controller = useController({ name: key as NotificationSectionType, control });
  }
};

type NotificationSettingsProps = {
  initialData?: NotificationSubscriptionsBulkResponse;
};

export default function NotificationSettings({ initialData }: NotificationSettingsProps): JSX.Element {
  const { user: loginUser } = useAuth();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [formErrors, setFormErrors] = useState<NotificationSectionsError>(SeedNotificationSectionErrorData);

  const {
    data: subscriptionsResponse,
    isLoading,
    error,
    mutate,
  } = useSWR<NotificationSubscriptionsBulkResponse>(NOTIFICATION_SUBSCRIPTIONS_API_URL, null, {
    fallbackData: initialData,
  });

  const sections = useMemo(() => {
    let updatableSections = Object.assign({}, SeedNotificationSectionsData);
    updatableSections = Object.entries(updatableSections).reduce((accum, [sectionKey, sectionValue]) => {
      let { events } = sectionValue;
      const { permissions, ...rest } = sectionValue;

      events = Object.entries(permissions)
        /**
         * currently, I check for the presence of 'all' permissions so it is an 'every' and not a 'some'
         * this might change in the future.
         */
        .filter(([, eventPermissions]) => loginUser?.hasPermissions(eventPermissions))
        .map(([eventKey]) => eventKey as NotificationEvent);

      if (events.length > 0) {
        return {
          ...accum,
          [sectionKey]: {
            permissions,
            ...rest,
            events,
          },
        };
      }

      return accum;
    }, {} as NotificationSections);

    if (!error && !isLoading && !!subscriptionsResponse?.subscriptions) {
      const updateSection = (
        key: NotificationSectionType,
        event: NotificationEvent,
        cadence: NotificationCadence,
      ): void => {
        if (updatableSections[key]) {
          updatableSections[key] = {
            ...updatableSections[key],
            eventsSelected: Array.from(new Set([...updatableSections[key].eventsSelected, event])),
            cadencesSelected: Array.from(new Set([...updatableSections[key].cadencesSelected, cadence])),
          };
        }
      };

      for (const { event, cadence } of subscriptionsResponse.subscriptions) {
        switch (event) {
          case NotificationEvent.PURCHASE_CREATED:
          case NotificationEvent.PURCHASE_STATUS_UPDATED:
            updateSection("purchase", event, cadence);
            break;
          case NotificationEvent.RETIREMENT_CREATED:
          case NotificationEvent.RETIREMENT_STATUS_UPDATED:
            updateSection("retirement", event, cadence);
            break;
          case NotificationEvent.BOOK_CREATED:
          case NotificationEvent.BOOK_PRICE_UPDATED:
          case NotificationEvent.BASKET_COMPOSITION_UPDATED:
            updateSection("basket", event, cadence);
            break;
          case NotificationEvent.PROJECT_CREATED:
          case NotificationEvent.PROJECT_BUFFER_UPDATED:
            updateSection("project", event, cadence);
            break;
          case NotificationEvent.ORGANIZATION_CREATED:
          case NotificationEvent.USER_CREATED:
            updateSection("org and user", event, cadence);
            break;
          case NotificationEvent.TRADE_CREATED:
          case NotificationEvent.TRADE_UPDATED:
            updateSection("trade", event, cadence);
            break;
          case NotificationEvent.TRANSFER_EXECUTED:
            updateSection("book", event, cadence);
            break;
          case NotificationEvent.ACTIVITY_EVENT:
            updateSection("activity", event, cadence);
            break;
          case NotificationEvent.MODEL_PORTFOLIO_CREATED:
          case NotificationEvent.MODEL_PORTFOLIO_UPDATED:
            updateSection("model portfolio", event, cadence);
            break;
          default:
            break;
        }
      }
    }

    return updatableSections;
  }, [subscriptionsResponse, error, isLoading, loginUser]);

  const defaultValues = useMemo(
    () => ({
      purchase: pickFromRecord(sections.purchase, ["eventsSelected", "cadencesSelected"]),
      retirement: pickFromRecord(sections.retirement, ["eventsSelected", "cadencesSelected"]),
      basket: pickFromRecord(sections.basket, ["eventsSelected", "cadencesSelected"]),
      project: pickFromRecord(sections.project, ["eventsSelected", "cadencesSelected"]),
      "org and user": pickFromRecord(sections["org and user"], ["eventsSelected", "cadencesSelected"]),
      trade: pickFromRecord(sections.trade, ["eventsSelected", "cadencesSelected"]),
      book: pickFromRecord(sections.book, ["eventsSelected", "cadencesSelected"]),
      activity: pickFromRecord(sections.activity, ["eventsSelected", "cadencesSelected"]),
      "model portfolio": pickFromRecord(sections["model portfolio"], ["eventsSelected", "cadencesSelected"]),
    }),
    [sections],
  );

  const hasFormError = useMemo(
    () => Object.values(formErrors).some(({ events, cadences }) => events || cadences),
    [formErrors],
  );

  const {
    handleSubmit,
    formState: { isDirty, isSubmitting },
    control,
    reset,
  } = useForm<NotificationModel>({
    mode: "onTouched",
    defaultValues,
  });

  useSetControllerOnSections(control, sections);

  useEffect(() => reset(defaultValues), [defaultValues, reset]);

  const RenderedSelection = useCallback((value: NotificationCadence[]): ReactNode => {
    const count = value.length || 0;
    const hasCadence = count > 0;
    const hasMultiple = hasCadence && count > 1;

    return (
      <>
        <Maybe condition={hasCadence && !hasMultiple}>{NotificationCadenceUILabel[value[0]]}</Maybe>
        <Maybe condition={hasCadence && hasMultiple}>{count} selected</Maybe>
      </>
    );
  }, []);

  const handleFormReset = (event: MouseEvent<HTMLButtonElement>): void => {
    event.preventDefault();
    reset(defaultValues);
    setFormErrors(SeedNotificationSectionErrorData);
  };

  const handleFrequencySelection = (
    event: SelectChangeEvent<(typeof NotificationCadence)[]>,
    field?: ControllerRenderProps<NotificationModel, NotificationSectionType>,
  ): void => {
    if (!field) return;

    const { value, name } = event.target;
    const { value: current, onChange } = field;

    const updatedCadences = value;
    const hasEvents = current.eventsSelected.length > 0;
    const hasCadences = updatedCadences.length > 0;

    onChange({
      ...current,
      cadencesSelected: updatedCadences,
    });

    setFormErrors((previous) => ({
      ...previous,
      [name]: {
        events: hasCadences ? !hasEvents : false,
        cadences: hasEvents ? !hasCadences : false,
      },
    }));
  };

  const handleEventCheck = (
    event: ChangeEvent<HTMLInputElement>,
    field?: ControllerRenderProps<NotificationModel, NotificationSectionType>,
  ): void => {
    if (!field) return;

    const { checked, value, name } = event.target;
    const { value: current, onChange } = field;

    const updatedEvents = checked
      ? [...(current?.eventsSelected || []), value]
      : current?.eventsSelected?.filter((event) => event !== value);
    const hasEvents = updatedEvents.length > 0;
    const hasCadences = current.cadencesSelected.length > 0;

    if (checked)
      onChange({
        ...current,
        eventsSelected: updatedEvents,
      });
    else
      onChange({
        ...current,
        eventsSelected: updatedEvents,
      });

    setFormErrors((previous) => ({
      ...previous,
      [name]: {
        events: hasCadences ? !hasEvents : false,
        cadences: hasEvents ? !hasCadences : false,
      },
    }));
  };

  const onSubmit = async (formData: NotificationModel): Promise<void> => {
    const payload: NotificationSubscriptionsRequest = {
      subscriptions: [],
    };

    for (const { eventsSelected = [], cadencesSelected = [] } of Object.values(formData) as Pick<
      NotificationSection,
      "eventsSelected" | "cadencesSelected"
    >[]) {
      for (const event of eventsSelected)
        cadencesSelected.forEach((cadence) => payload.subscriptions.push({ event, cadence }));
    }

    try {
      await api.put(NOTIFICATION_SUBSCRIPTIONS_API_URL, payload);
      enqueueSuccess("Successful saved notification settings");
      mutate();
    } catch {
      enqueueError("Unable to save notification settings");
    }
  };

  return (
    <Page>
      <Box className={classes.Container}>
        <form id="notification-settings" onSubmit={handleSubmit(onSubmit)}>
          <Grid className={classes.Content} container direction="column" gap={3}>
            {/* Main Header */}
            <Grid className={classes.SectionHeader} item>
              <Typography className={classes.DescriptionLabel}>
                Select the email notifications you want to receive.
              </Typography>
            </Grid>
            {/*Notification Content */}
            <Grid item container direction="column" gap={1}>
              {Object.entries(sections).map(([key, { label, subLabel, events, cadences, controller }]) => {
                const { field } = controller ?? {};
                const { value } = field ?? {};
                const { eventsSelected, cadencesSelected } = value ?? {};

                return (
                  <Fragment key={key}>
                    {/* Section */}
                    <Grid item container direction="column" gap={2}>
                      {/* Section Header */}
                      <SectionHeader label={label} subLabel={subLabel} />
                      {/* Section Content */}
                      <Grid item container gap={2}>
                        {/* Frequencies */}
                        <Grid item xs={12} sm={4} md={5}>
                          <FormControl className={classes.Frequency} error={!!formErrors[key]?.cadences}>
                            <InputLabel>Frequency</InputLabel>
                            <Select
                              multiple
                              {...field}
                              label="Frequency"
                              renderValue={RenderedSelection}
                              value={cadencesSelected ?? []}
                              onChange={(event: SelectChangeEvent<(typeof NotificationCadence)[]>) =>
                                handleFrequencySelection(event, field)
                              }
                            >
                              {cadences.map((cadence) => (
                                <MenuItem key={cadence} value={cadence}>
                                  <Checkbox checked={cadencesSelected?.includes(cadence)} />
                                  <ListItemText primary={NotificationCadenceUILabel[cadence]} />
                                </MenuItem>
                              ))}
                            </Select>
                            <Maybe condition={!!formErrors[key]?.cadences}>
                              <FormHelperText error>At least one frequency selection required</FormHelperText>
                            </Maybe>
                          </FormControl>
                        </Grid>
                        {/* Events */}
                        <Grid item xs={12} sm={6} md={5}>
                          <FormGroup className={classes.CheckboxGroup}>
                            {events.map((event) => (
                              <FormControlLabel
                                key={event}
                                control={
                                  <Checkbox
                                    {...field}
                                    value={event}
                                    checked={eventsSelected?.includes(event)}
                                    onChange={(event: ChangeEvent<HTMLInputElement>) => handleEventCheck(event, field)}
                                  />
                                }
                                label={NotificationEventUILabel[event]}
                              />
                            ))}
                            <Maybe condition={!!formErrors[key]?.events}>
                              <FormHelperText error>At least one event selection required</FormHelperText>
                            </Maybe>
                          </FormGroup>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Divider />
                  </Fragment>
                );
              })}
            </Grid>
            {/* Action Buttons */}
            <Grid className={classes.Actions} item container justifyContent="center" gap={2}>
              <Button className={classes.TextButton} variant="text" onClick={handleFormReset} disabled={!isDirty}>
                reset
              </Button>
              <LoadingButton
                variant="contained"
                type="submit"
                form="notification-settings"
                loading={isSubmitting}
                disabled={!isDirty || (isDirty && hasFormError) || isSubmitting}
              >
                save
              </LoadingButton>
            </Grid>
          </Grid>
        </form>
      </Box>
    </Page>
  );
}
