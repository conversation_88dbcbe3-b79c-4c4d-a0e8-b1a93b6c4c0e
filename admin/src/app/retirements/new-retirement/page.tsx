import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookQueryResponse,
  BookAction,
  BookRelations,
  BookType,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import NewRetirementForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * New Retirement Page
 *
 * This is a server component that renders the New Retirement page
 */
export default async function NewRetirementPage(): Promise<JSX.Element> {
  const customerPortfoliosResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: false,
        includeRelations: [BookRelations.ORGANIZATION],
        types: [BookType.PORTFOLIO_CUSTOMER],
        allowedAction: BookAction.RETIRE,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(customerPortfoliosResponse)) return customerPortfoliosResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_CREATE]}>
      <NewRetirementForm customerPortfoliosResponse={customerPortfoliosResponse as AdminBookQueryResponse} />
    </AuthorizeServer>
  );
}
