import COLORS from "@components/ui/theme/colors";
import { MISSING_DATA } from "@constants/constants";
import { Stack, Grid, Typography, Divider } from "@mui/material";
import { numberFormat, toNumber } from "@rubiconcarbon/frontend-shared";
import { Fragment } from "react";
import PublicStatus from "@components/ui/public-status/public-status";
import { AssetType } from "@rubiconcarbon/shared-types";
import { TransactionModel } from "@models/transaction";

import classes from "../../styles/confirmation.module.scss";

type RetirementConfirmationProps = {
  data: TransactionModel;
};

const RetirementConfirmation = ({ data }: RetirementConfirmationProps): JSX.Element => {
  return (
    <Stack gap={4}>
      <Grid item container gap={2}>
        <Typography className={classes.Label}>Organization:</Typography>
        <Typography className={classes.Value}>{data?.retirement?.customerPortfolio?.organization?.name}</Typography>
      </Grid>

      <Grid item container gap={1}>
        <Typography className={classes.SubHeader} color={COLORS.rubiconGreen}>
          Product Details
        </Typography>
        <Grid className={classes.Table} container gap={0.5}>
          <Grid className={classes.Header} item container gap={1}>
            <Grid className={classes.Label} item xs={9}>
              Product
            </Grid>
            <Grid className={classes.Label} item xs={2}>
              Quantity
            </Grid>
          </Grid>
          <Grid className={classes.Body} item container gap={1}>
            {data?.assets?.map((lineItem) => (
              <Fragment key={lineItem?.supplementaryAssetDetails?.id}>
                <Grid className={classes.Value} item xs={9}>
                  {data?.retirement?.productType === AssetType.REGISTRY_VINTAGE
                    ? `${lineItem?.supplementaryAssetDetails?.registryProjectId} - ${lineItem?.supplementaryAssetDetails?.name}`
                    : lineItem?.supplementaryAssetDetails?.name}
                </Grid>
                <Grid className={classes.Value} item xs={2}>
                  {numberFormat(lineItem?.amount)}
                </Grid>
                <Grid item xs={12}>
                  <Divider />
                </Grid>
              </Fragment>
            ))}
          </Grid>
          <Grid className={classes.Footer} item container>
            <Grid item xs={8} />
            <Grid className={classes.Value} item container xs={4} alignItems="center" gap={0.5}>
              <Typography variant="caption">Total Quantity:</Typography>
              <Typography variant="body2" fontWeight="bold">
                {numberFormat(
                  data?.assets?.reduce((sum, { amount }) => sum + toNumber(amount, { parserBlacklist: [","] }), 0),
                  { fallback: MISSING_DATA },
                )}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Grid item container gap={1}>
        <Typography className={classes.SubHeader} color={COLORS.rubiconGreen}>
          Purchase Details
        </Typography>
        <Grid container gap={1}>
          <Grid item container gap={1}>
            <Grid className={classes.Label} item xs={4}>
              Visibility
            </Grid>
            <Grid className={classes.Label} item xs={3}>
              Beneficiary
            </Grid>
          </Grid>
          <Grid item container gap={1}>
            <Grid className={classes.Value} item xs={4}>
              <PublicStatus isPublic={data?.retirement?.isPublic} />
            </Grid>
            <Grid className={classes.Value} item xs={3}>
              {data?.retirement?.beneficiary}
            </Grid>
          </Grid>
        </Grid>
        <Grid container direction="column" gap={1}>
          <Typography className={classes.Label}>Memo</Typography>
          <Typography className={classes.Value}>{data?.memo?.trim() || MISSING_DATA}</Typography>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default RetirementConfirmation;
