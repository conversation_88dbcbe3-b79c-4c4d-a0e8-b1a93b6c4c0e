"use client";

import { AdminBookQueryResponse } from "@rubiconcarbon/shared-types";
import NewRetirementForm from "./new";
import Page from "@components/layout/containers/page";

const NewRetirement = ({
  customerPortfoliosResponse,
}: {
  customerPortfoliosResponse: AdminBookQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <NewRetirementForm customerPortfoliosResponse={customerPortfoliosResponse} />
    </Page>
  );
};

export default NewRetirement;
