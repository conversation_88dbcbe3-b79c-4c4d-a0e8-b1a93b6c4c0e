import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import { useBoolean, usePrevious } from "react-use";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  AdminRetirementRequest,
  AssetAllocationQuery,
  AssetAllocationQueryResponse,
  AssetType,
  AdminBookQueryResponse,
  AdminBookResponse,
  RetirementType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useEffect, useMemo } from "react";
import { KeyboardArrowRightRounded } from "@mui/icons-material";
import { Container, Stack, Autocomplete, TextField, Button, MenuItem } from "@mui/material";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import PublicStatus from "@components/ui/public-status/public-status";
import { ProductTypeOptions } from "@constants/products";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { toBoolean, toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import { TransactionModel } from "@models/transaction";
import { getNewRetirementModel } from "@utils/helpers/transaction/get-new-transaction-models";
import RetirementProductDetails from "./retirement-product-details";
import RetirementConfirmation from "./retirement-confirmation";

import classes from "../../styles/new-form.module.scss";

const parserBlacklist = ["$", ","];

const VisibilityOptions: { label: string; value: boolean }[] = [
  {
    label: "Public",
    value: true,
  },
  {
    label: "Private",
    value: false,
  },
];

const RetirementModelResolver = classValidatorResolver(TransactionModel);

const NewRetirementForm = ({
  customerPortfoliosResponse,
}: {
  customerPortfoliosResponse: AdminBookQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [canConfirm, setCanConfirm] = useBoolean(false);

  const model = getNewRetirementModel();

  const {
    control,
    formState: { errors },
    trigger,
    setValue,
    watch,
    resetField,
    handleSubmit,
  } = useForm<TransactionModel>({
    resolver: RetirementModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: lineItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "assets",
  });

  const data = watch();
  const customerPortfolio = data?.retirement?.customerPortfolio;

  const isPortfolioSelection = useMemo(
    () => data?.retirement?.productType === AssetType.RCT,
    [data?.retirement?.productType],
  );

  const hiddenLines = isPortfolioSelection
    ? lineItems?.reduce((ids, { _id }, index) => [...ids, index > 0 ? _id : null].filter((id) => !!id), [] as string[])
    : [];

  const previousSelectedCustomerPortfolioId = usePrevious(customerPortfolio?.id);

  const {
    data: assets,
    trigger: getAssets,
    isMutating: loadingAssets,
  } = useTriggerRequest<AssetAllocationQueryResponse, null, { id: uuid }, AssetAllocationQuery>({
    url: "admin/books/{id}/assets/holdings",
    pathParams: {
      id: customerPortfolio?.id,
    },
    queryParams: {
      assetTypes: [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
      limit: SERVER_PAGINATION_LIMIT,
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch assets for ${customerPortfolio?.name}`);
        logger.error(`Unable to load assets for ${customerPortfolio?.name}: ${error?.message}`, {});
      },
    },
  });

  const hasPortfolioProducts = useMemo(
    () => assets?.data?.some(({ detailedAsset }) => !Object.hasOwn(detailedAsset, "project")),
    [assets?.data],
  );

  const hasVintageProducts = useMemo(
    () => assets?.data?.some(({ detailedAsset }) => Object.hasOwn(detailedAsset, "project")),
    [assets?.data],
  );

  const productTypeOptions = useMemo(
    () =>
      ProductTypeOptions.filter((option) =>
        option.value === AssetType.RCT
          ? hasPortfolioProducts
          : option.value === AssetType.REGISTRY_VINTAGE
            ? hasVintageProducts
            : false,
      ),
    [hasPortfolioProducts, hasVintageProducts],
  );

  const { trigger: commitRetirement, isMutating: commitingRetirement } = useTriggerRequest<any, AdminRetirementRequest>(
    {
      url: "admin/retirements",
      method: "post",
      requestBody: {
        customerPortfolioId: data?.retirement?.customerPortfolio?.id,
        isPublic: toBoolean(data?.retirement?.isPublic),
        assetType: data?.retirement?.productType,
        beneficiary: data?.retirement?.beneficiary,
        memo: data?.memo,
        type: RetirementType.RETIREMENT,
        assets: data?.assets?.map((item) => ({
          assetId: item?.supplementaryAssetDetails?.id,
          sourceId: data?.retirement?.customerPortfolio?.id,
          amount: toNumber(item?.amount, { parserBlacklist }),
          rawPrice: toDecimal(0.0),
        })),
      },
      swrOptions: {
        onSuccess: () => {
          enqueueSuccess("Retirement created successfully.");
          popFromPath(1);
        },
        onError: (error: any): void => {
          enqueueError("Unable to create retirement.");
          logger.error(`Unable to create retirement: ${error?.message}`, {});
        },
      },
    },
  );

  const allocations = useMemo(() => assets?.data, [assets?.data]);

  useEffect(() => {
    const customerPortfolioId = customerPortfolio?.id;
    if (!!customerPortfolioId && previousSelectedCustomerPortfolioId !== customerPortfolioId && !loadingAssets)
      setTimeout(async () => await getAssets());
  }, [customerPortfolio?.id, getAssets, loadingAssets, previousSelectedCustomerPortfolioId]);

  const customerPortfolioOptions = useAutoCompleteOptions<AdminBookResponse, AdminBookResponse>({
    data: customerPortfoliosResponse?.data || [],
    keys: ["id", "organization"],
    label: (entry: AdminBookResponse) => entry?.organization?.name,
    value: (entry: AdminBookResponse) => entry,
    preTransform: (data: AdminBookResponse[]) => data?.filter(({ isEnabled }) => isEnabled),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const onSubmit = (): void => setCanConfirm(true);

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="retirement.customerPortfolio"
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = customerPortfolioOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
            return (
              <Autocomplete
                options={customerPortfolioOptions}
                value={selectedOption}
                onChange={(_, selection) => onChange(selection?.value)}
                id="organization"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.value?.id}
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.label}
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ref,
                      ...InputProps,
                    }}
                    label="Organization"
                    {...otherProps}
                    error={!!errors?.retirement?.customerPortfolio}
                    helperText={errors?.retirement?.customerPortfolio?.message}
                    fullWidth
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <Controller
          name="retirement.isPublic"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Visibility"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.isPublic}
              helperText={errors?.retirement?.isPublic?.message}
              {...otherProps}
              fullWidth
            >
              {VisibilityOptions.map((option) => (
                <MenuItem key={option.label} value={option.value?.toString()}>
                  <PublicStatus isPublic={option.value} />
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <Controller
          name="retirement.productType"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Product Type"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.productType}
              helperText={
                !customerPortfolio
                  ? "Please select Organization"
                  : !loadingAssets && !productTypeOptions?.length
                    ? "No Products Available to Retire"
                    : errors?.retirement?.productType?.message
              }
              {...otherProps}
              fullWidth
              disabled={loadingAssets || !customerPortfolio || !productTypeOptions?.length}
            >
              {productTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <RetirementProductDetails
          loadingAssets={loadingAssets}
          allocations={allocations}
          control={control}
          errors={errors}
          lineItems={lineItems}
          trigger={trigger}
          setValue={setValue}
          resetField={resetField}
          watch={watch}
          append={append}
          remove={remove}
        />
        <Controller
          name="retirement.beneficiary"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Beneficiary"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.retirement?.beneficiary}
              helperText={errors?.retirement?.beneficiary?.message}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        <Controller
          name="memo"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Memo"
              value={value ?? ""}
              InputProps={{ ref, sx: { whiteSpace: "pre-wrap" } }}
              minRows={4}
              {...otherProps}
              error={!!errors.memo}
              helperText={errors.memo?.message}
              fullWidth
              multiline
            />
          )}
        />
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
            disabled={loadingAssets}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        title="Review and Confirm Retirement"
        open={canConfirm}
        onClose={() => setCanConfirm(false)}
        positiveAction={{
          buttonText: "CONFIRM RETIREMENT",
          loading: commitingRetirement,
          onClick: async () => await commitRetirement(),
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setCanConfirm(false),
        }}
        classes={{
          root: classes.Dialog,
          title: classes.Title,
          content: classes.Content,
          actions: classes.Actions,
        }}
      >
        <RetirementConfirmation
          data={{
            ...data,
            assets: data?.assets?.filter((item) => !hiddenLines?.length || !hiddenLines.includes(item?._id)),
          }}
        />
      </GenericDialog>
    </Container>
  );
};

export default NewRetirementForm;
