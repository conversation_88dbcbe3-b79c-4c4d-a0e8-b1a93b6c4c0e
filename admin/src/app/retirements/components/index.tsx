"use client";

import Page from "@components/layout/containers/page";
import RetirementsComponent from "./retirements";
import { RetirementQueryResponse } from "@rubiconcarbon/shared-types";

const Retirements = ({ retirementsResponse }: { retirementsResponse: RetirementQueryResponse }): JSX.Element => {
  return (
    <Page>
      <RetirementsComponent retirementsResponse={retirementsResponse} />
    </Page>
  );
};

export default Retirements;
