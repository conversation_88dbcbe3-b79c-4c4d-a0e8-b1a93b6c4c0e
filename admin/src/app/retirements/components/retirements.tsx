import React from "react";
import {
  OrderByDirection,
  PermissionEnum,
  RetirementOrderByOptions,
  RetirementQuery,
  RetirementQueryResponse,
  RetirementRelations,
} from "@rubiconcarbon/shared-types";
import useNavigation from "@/hooks/use-navigation";
import { Box } from "@mui/material";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { AddRounded, FileDownloadRounded } from "@mui/icons-material";
import { toRetirementModel } from "@utils/helpers/transaction/to-transaction-models";
import { TransactionModel } from "@models/transaction";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericTable from "@components/ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { COLUMNS } from "../constants/columns";
import RetirementSummary from "./retirements-summary";

export default function Retirements({
  retirementsResponse: serverRetirementsResponse,
}: {
  retirementsResponse: RetirementQueryResponse;
}): JSX.Element {
  const { pushToPath } = useNavigation();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const {
    data: retirementsResponse,
    isMutating: loadingRetirements,
    trigger: refreshRetirements,
  } = useTriggerRequest<RetirementQueryResponse, null, null, RetirementQuery>({
    url: "/admin/retirements",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
        RetirementRelations.REQUESTED_BY,
      ],
      orderBys: [`${RetirementOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
    },
    optimisticData: serverRetirementsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch retirements.");
        logger.error(`Unable to fetch retirements. Error: ${error?.message}`, {});
      },
    },
  });

  const { table } = useGenericTableUtility<TransactionModel>({});

  return (
    <GenericTable
      id="Retirements"
      loading={loadingRetirements}
      toRowModel={toRetirementModel}
      columns={COLUMNS}
      pageableData={retirementsResponse}
      globalSearch={{
        searchKeys: [
          "uiKey",
          "type",
          "product",
          "retirement.customerPortfolio.organization.name",
          "transfer.customerPortfolio.organization.name",
          "status",
          "amount",
          "dateStarted",
          "dateFinished",
          "memo",
          "retirement.beneficiary",
          "retirement.isPublic",
        ],
      }}
      export={{
        filename: `Retirements-${new Date()}`,
        setClientCanExport: table?.setClientCanExport,
        bindClientExport: table?.bindClientExport,
      }}
      styles={{
        root: {
          maxHeight: "calc(100vh - 145px)",
        },
      }}
      toolbarActionButtons={[
        {
          children: "Export",
          startIcon: <FileDownloadRounded />,
          requiredPermission: PermissionEnum.RETIREMENTS_READ,
          style: DEFAULT_EXPORT_STYLE,
          isDisabled: loadingRetirements || !table?.clientCanExport,
          onClickHandler: table?.handleClientExport,
        },
        {
          children: "Retirement Calculator",
          requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_ADMIN_REVIEW,
          isDisabled: loadingRetirements,
          onClickHandler: () => pushToPath("retirement-calculator"),
        },
        {
          children: "Retirement",
          startIcon: <AddRounded />,
          requiredPermission: PermissionEnum.RETIREMENTS_CREATE,
          isDisabled: loadingRetirements,
          onClickHandler: () => pushToPath("/new-retirement"),
        },
        {
          children: "Transfer",
          startIcon: <AddRounded />,
          requiredPermission: PermissionEnum.RETIREMENTS_CREATE,
          isDisabled: loadingRetirements,
          onClickHandler: () => pushToPath("/new-transfer"),
        },
      ]}
      isExpandable
      eager={{
        expand: true,
      }}
      renderExpandContent={(row) => (
        <Box padding={2}>
          <RetirementSummary transaction={row} refresh={refreshRetirements} />
        </Box>
      )}
    />
  );
}
