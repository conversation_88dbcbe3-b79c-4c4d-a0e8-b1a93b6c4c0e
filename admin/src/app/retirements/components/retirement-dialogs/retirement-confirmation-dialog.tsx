import React, { useContext, useMemo } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import { useState } from "react";
import { RetirementUpdateStatus, TransactionResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import { AxiosContext } from "@providers/axios-provider";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface RetirementConfirmationDialogProps extends BaseDialogProps {
  retirement: TransactionModel;
  refreshData: () => void;
}
export default function RetirementConfirmationDialog({
  isOpen,
  retirement,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: RetirementConfirmationDialogProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);

  const isTransfer = useMemo(() => retirement?.type === AllTransactionType.TRANSFER_OUTFLOW, [retirement?.type]);

  const submitConfirmationHandler = (): void => {
    setRequestInFlight(true);
    api
      .patch<TransactionResponse>(`admin/retirements/${retirement.id}/${RetirementUpdateStatus.COMPLETE}`)
      .then(() => {
        refreshData();
        onConfirm?.(`Successfully completed ${isTransfer ? "transfer" : "retirement"}`);
        onClose();
      })
      .catch((e) => {
        onError?.("Sorry, we are unable to complete your request");
        console.error(e);
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle sx={{ backgroundColor: COLORS.modalMargins }}>
        Confirm {isTransfer ? "transfer" : "retirement"} request
      </DialogTitle>
      <DialogContent
        sx={{
          borderTop: "1px solid #B2B6BB",
          borderBottom: "1px solid #B2B6BB",
        }}
      >
        <Typography variant="body1">
          Are you sure you want to complete the {isTransfer ? "transfer" : "retirement"} request,
          <em>
            <b>{` ${retirement?.uiKey}`}</b>
          </em>
          ?
        </Typography>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions sx={{ height: 60, backgroundColor: COLORS.modalMargins }}>
        <Button disabled={requestInFlight} variant="text" onClick={onClose} sx={{ fontWeight: 600 }}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          variant="contained"
          type="submit"
          sx={{ px: 3.5, color: "#FFFFFF" }}
          onClick={submitConfirmationHandler}
        >
          Yes, proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}
