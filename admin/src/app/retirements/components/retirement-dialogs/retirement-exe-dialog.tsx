import React, { useContext, useMemo } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";
import { useState } from "react";
import { RetirementUpdateStatus, TransactionResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import { AxiosContext } from "@providers/axios-provider";
import { AllTransactionType, TransactionModel } from "@models/transaction";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface RetirementExecutionDialogProps extends BaseDialogProps {
  retirement: TransactionModel;
  refreshData: () => void;
}

export default function RetirementExecutionDialog({
  isOpen,
  retirement,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: RetirementExecutionDialogProps): JSX.Element {
  const [requestInFlight, setRequestInFlight] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const dangerTheme = { backgroundColor: COLORS.red, color: COLORS.white };

  const isTransfer = useMemo(() => retirement?.type === AllTransactionType.TRANSFER_OUTFLOW, [retirement?.type]);

  const submitExecutionHandler = (): void => {
    setRequestInFlight(true);
    api
      .patch<TransactionResponse>(`admin/retirements/${retirement.id}/${RetirementUpdateStatus.PROCESS}`)
      .then(() => {
        refreshData();
        onConfirm?.(`${isTransfer ? "Transfer" : "Retirement"} request ${retirement.uiKey} successfully executed`);
        onClose();
      })
      .catch(() => {
        onError?.("Sorry, we are unable to complete your request");
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose}>
      <DialogTitle sx={dangerTheme}>Execute {isTransfer ? "transfer" : "retirement"} request</DialogTitle>
      <DialogContent>
        <Typography variant="body1" component="p">
          Are you sure you want to execute {isTransfer ? "transfer" : "retirement"} request <b>{retirement.uiKey}</b>{" "}
          for <b>{retirement?.retirement?.customerPortfolio?.organization?.name}</b>?
        </Typography>
        <br />
        <Typography variant="body1" component="p">
          This action <em>cannot</em> be undone. Proceed only if you are ready to permanently{" "}
          {isTransfer ? "transfer" : "retire"} these assets.
        </Typography>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions>
        <Button disabled={requestInFlight} variant="text" onClick={onClose} sx={{ fontWeight: 600 }}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          type="submit"
          variant="contained"
          color="error"
          onClick={submitExecutionHandler}
        >
          Yes, proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}
