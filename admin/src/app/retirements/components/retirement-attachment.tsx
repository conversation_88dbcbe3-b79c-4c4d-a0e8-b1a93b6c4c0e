import { Attachment, AttachmentDocument } from "@components/attachment-list/attachment-list";
import { TEN_MINUTES } from "@constants/constants";
import { useDataPolling } from "@rubiconcarbon/frontend-shared";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { DocumentResponse } from "@rubiconcarbon/shared-types";
import { useState, useEffect, useCallback } from "react";
import { useUpdateEffect } from "react-use";

type RetirementAttachmentProps = {
  retirementCertificate: DocumentResponse;
  renderFileAs?: "link" | "button";
  onDelete: () => Promise<any>;
};

const RetirementAttachment = ({
  retirementCertificate,
  renderFileAs,
  onDelete,
}: RetirementAttachmentProps): JSX.Element => {
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [startNewLinkPolling, setStartNewLinkPolling] = useState<boolean>(false);
  const [attachment, setAttachment] = useState<AttachmentDocument>(retirementCertificate);

  const {
    data: document,
    error,
    isLoading,
  } = useDataPolling<DocumentResponse>(`/admin/documents/${attachment?.id}`, {
    every: TEN_MINUTES,
    engage: !!attachment && startNewLinkPolling,
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setStartNewLinkPolling(true);
    }, TEN_MINUTES);

    return (): void => {
      setStartNewLinkPolling(false);
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    if (retirementCertificate) {
      setAttachment({
        ...retirementCertificate,
        renderedFileName: retirementCertificate.filename,
        as: renderFileAs,
      });
    }
  }, [retirementCertificate, renderFileAs]);

  useUpdateEffect(() => {
    if (!error && !isLoading && !!document) {
      const clonedAttachment: AttachmentDocument = Object.assign({}, attachment);
      const updatedAttachment = {
        ...clonedAttachment,
        downloadUrl: document.downloadUrl,
      };

      setAttachment(updatedAttachment);
    }
  }, [document, error, isLoading]);

  const handleAttachmentRemovalSuccess = useCallback(
    async () => {
      await onDelete();
      enqueueSuccess(`Successfully deleted retirement certificate for ${retirementCertificate.relatedKey}`);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [retirementCertificate, onDelete],
  );

  const handleAttachmentRemovalError = useCallback(
    async () => {
      enqueueError(`Failed to delete retirement certificate for ${retirementCertificate.relatedKey}`);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [retirementCertificate],
  );

  return (
    <Attachment
      document={attachment}
      deleteConfirmation={{
        title: "Confirm document deletion",
        content: "Are you sure you want to delete retirement certificate?",
      }}
      onRemoveSuccess={handleAttachmentRemovalSuccess}
      onRemoveError={handleAttachmentRemovalError}
    />
  );
};

export default RetirementAttachment;
