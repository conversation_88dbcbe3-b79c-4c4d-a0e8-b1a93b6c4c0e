import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import { MISSING_DATA } from "@constants/constants";
import { Stack } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { RetirementLink, TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import { ExtendedRetirementDetailsModel } from "../types/extended-retirement-details-model";
import dateRangeFormatter from "@/utils/formatters/date-range-formatter";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";

export const COLUMNS: GenericTableColumn<ExtendedRetirementDetailsModel>[] = [
  {
    field: "id",
    label: "",
    exportable: false,
    width: GenericTableFieldSizeEnum.xtiny,
    fixedWidth: true,
    renderDataCell: (row) => <RCTEligibilityChip vintage={row?.projectVintage} />,
  },
  {
    field: "projectVintage.project.registryProjectId",
    label: "Project ID",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage.project.name",
    label: "Project Name",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage.name",
    label: "Vintage",
    hide: true,
    exportable: true,
  },
  {
    field: "projectVintage",
    label: "Name",
    exportable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    maxWidth: GenericTableFieldSizeEnum.large,
    transformDataValue: (value: TrimmedProjectVintageResponse) => value?.name,
    renderDataCell: (row) => (
      <>
        <div>{row?.projectVintage?.project?.name}</div>
        <div style={{ color: "#a0a0a0" }}>
          {row?.projectVintage?.project?.registryProjectId} - {row?.projectVintage?.name}
          <Stack direction="row" gap={1}>
            <Maybe condition={row?.projectVintage?.project?.suspended}>
              <SuspendedChip />
            </Maybe>
          </Stack>
        </div>
      </>
    ),
  },
  {
    field: "projectVintage.interval",
    label: "Vintage date range",
    transformDataValue: (value: string): string => `${dateRangeFormatter(value)}`,
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "links",
    label: "Registry confirmation",
    sortable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    transformDataValue: (value: RetirementLink[]) =>
      value.reduce((accum, link, index) => accum.concat(index > 0 ? ", " : "").concat(link?.url), "") || MISSING_DATA,
  },
];
