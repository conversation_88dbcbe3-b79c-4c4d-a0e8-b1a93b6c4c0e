import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, TextField, Badge } from "@mui/material";
import {
  LinkRounded,
  CloseRounded,
  EditRounded,
  DeleteRounded,
  CheckRounded,
  UndoRounded,
  AddCircleRounded,
  CancelRounded,
} from "@mui/icons-material";
import { Tooltip, IconButton, Typography, Grow, Paper, MenuList, Divider } from "@mui/material";
import { Stack, Box } from "@mui/system";
import { px, Maybe, pickFromArrayOfRecords, NO_OP } from "@rubiconcarbon/frontend-shared";
import { RetirementLink, RetirementResponse, UpdateRetirementLinksRequest, uuid } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import { KeyboardEvent, MouseEvent, useMemo, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { isNotEmpty, isURL } from "class-validator";
import { useUpdateEffect } from "react-use";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import COLORS from "@components/ui/theme/colors";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";

import classes from "../styles/registry-links.module.scss";

type RegistryLinksProps = {
  id: uuid;
  projectVintageId: uuid;
  links: RetirementLink[];
  refreshRetirement: () => Promise<RetirementResponse>;
};

class LinkItem {
  editing?: boolean;
  deleting?: boolean;
  fromServer?: boolean;
  label?: string;
  url: string;
}

class LinksModel {
  links: LinkItem[];
}

const DEFAULT_EMPTY_LINK: LinkItem = {
  editing: true,
  deleting: false,
  label: "",
  url: "",
};

const ERROR_MESSAGE = {
  IsUrl: "Invalid URL",
  IsNotEmpty: "Required",
};

const RegistryLinks = ({ id, projectVintageId, links = [], refreshRetirement }: RegistryLinksProps): JSX.Element => {
  const noLinks = !links.length;

  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const anchorRef = useRef<HTMLButtonElement>(null);
  const [openPopper, setOpenPopper] = useState<boolean>(false);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [requestBody, setRequestBody] = useState<UpdateRetirementLinksRequest>();

  const { trigger: commitLinks } = useTriggerRequest({
    method: "put",
    url: `admin/retirements/${id}/links`,
    requestBody,
    swrOptions: {
      onSuccess: async () => {
        enqueueSuccess("Successfully updated retirement links");
        handleDiaglogClose();
        await refreshRetirement();
      },
      onError: () => enqueueError("Unable to update retirement links"),
    },
  });

  const defaultValues: LinksModel = useMemo(
    () => ({ links: links.map((link) => ({ ...link, fromServer: true })) }),
    [links],
  );

  const {
    control,
    register,
    watch,
    setValue,
    reset,
    setError,
    clearErrors,
    formState: { errors },
  } = useForm<LinksModel>({ defaultValues });
  const { fields, append, remove } = useFieldArray({
    control,
    name: "links",
  });

  const formLinks = watch("links");
  const canAddNewLink = !!formLinks?.at(formLinks?.length - 1)?.url;
  const inEditMode = formLinks?.some((link) => link.editing);

  useUpdateEffect(() => {
    if (!formLinks?.length && openDialog) append(DEFAULT_EMPTY_LINK);
  }, [formLinks, openDialog]);

  useUpdateEffect(() => {
    reset({ links: defaultValues.links });
  }, [defaultValues]);

  const transformURL = (url: string): string => {
    const protocol = /^(https?:\/\/)?/;
    const domain = /^([A-z0-9.-]+)(\/)/;
    const endSlashAndBeyond = /(\/[A-z0-9.?=-]*)$/;

    const withoutProtocolPath = url.replace(protocol, "");

    const domainPath = withoutProtocolPath.match(domain)?.[0];
    const endSlashAndBeyondPath = withoutProtocolPath.match(endSlashAndBeyond)?.[0];

    if (!!endSlashAndBeyondPath && withoutProtocolPath !== `${domainPath.replace("/", "")}${endSlashAndBeyondPath}`)
      return `${domainPath}...${endSlashAndBeyondPath}`;
    else return withoutProtocolPath;
  };

  const toOpennableUrl = (url: string): string => {
    const protocol = /^(http|https):\/\//i;

    if (protocol.test(url)) return url;
    else return `http://${url}`;
  };

  const getFormLink = (index: number): LinkItem => formLinks?.at(index);

  const handlePopperToggle = (): void => setOpenPopper((prevOpen) => !prevOpen);

  const handlePopperClose = (event: Event | React.SyntheticEvent): void => {
    if (openPopper) {
      event?.preventDefault();
      if (!anchorRef?.current?.contains(event.target as HTMLElement)) setOpenPopper(false);
    }
  };

  const handleEditModeToggle = (event: MouseEvent<HTMLButtonElement>, index: number): void => {
    event?.preventDefault();

    const { url, editing } = getFormLink(index);

    const ine = isNotEmpty(url);
    const iurl = isURL(url);

    if (ine && iurl) {
      clearErrors(`links.${index}.url`);
      setValue(`links.${index}.editing`, !editing);
    } else {
      if (!ine) setError(`links.${index}.url`, { type: "ine", message: ERROR_MESSAGE.IsNotEmpty });
      else if (!iurl) setError(`links.${index}.url`, { type: "iurl", message: ERROR_MESSAGE.IsUrl });
    }
  };

  const handleDeletionToggle = (event: MouseEvent<HTMLButtonElement>, index: number): void => {
    event?.preventDefault();

    const { deleting, fromServer } = getFormLink(index);

    if (fromServer) {
      setValue(`links.${index}.deleting`, !deleting);
    } else remove(index);
  };

  const handleDiaglogClose = (): void => {
    reset();
    setOpenDialog(false);
  };

  const handleLinkClick = (event: MouseEvent<HTMLAnchorElement>): void => {
    event.stopPropagation();
    setOpenPopper(false);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>, index: number): void => {
    if (event?.code === "Enter") {
      event.preventDefault();
      event.stopPropagation();
      handleEditModeToggle({ preventDefault: NO_OP } as any, index);
    }
  };

  const onSubmit = async (): Promise<void> => {
    const payload: UpdateRetirementLinksRequest = {
      projectVintageId,
      links: pickFromArrayOfRecords(
        formLinks.filter((link) => !link.deleting),
        ["label", "url"],
      ) as RetirementLink[],
    };

    setRequestBody(payload);
    setTimeout(() => commitLinks());
  };

  return (
    <Box>
      <Stack direction="row" alignItems="center" gap={0.2}>
        <Tooltip title={!openPopper ? (noLinks ? "No links added" : "View links") : ""} arrow>
          <Box className={classes.LinkDropdownButtonContainer}>
            <IconButton
              ref={anchorRef}
              disabled={noLinks}
              className={classes.LinkDropdownButton}
              sx={{ ...px({ backgroundColor: openPopper && "rgb(9 68 54 / 24%)" }) }}
              aria-controls={openPopper ? "links-menu" : undefined}
              aria-expanded={openPopper ? "true" : undefined}
              aria-haspopup="true"
              onClick={handlePopperToggle}
            >
              <Badge color="primary" badgeContent={links.length} classes={{ badge: classes.Badge }}>
                <LinkRounded htmlColor={noLinks ? "gray" : COLORS.rubiconGreen} />
              </Badge>
            </IconButton>
          </Box>
        </Tooltip>
        <Tooltip title="Add or Edit links" arrow>
          <Button className={classes.ManageLinksButton} disableRipple onClick={() => setOpenDialog(true)}>
            <Typography className={classes.ManageLinksText} variant="body2">
              Manage links
            </Typography>
          </Button>
        </Tooltip>
      </Stack>
      <Popper id="registry-links" open={openPopper} anchorEl={anchorRef.current} transition keepMounted>
        {({ TransitionProps }) => (
          <Grow {...TransitionProps}>
            <Paper>
              <ClickAwayListener onClickAway={handlePopperClose}>
                <MenuList autoFocusItem={openPopper} id="links-menu" aria-labelledby="links-button">
                  {links.map((link) => (
                    <MenuItem key={link?.url} className={classes.MenuItem} onClick={handlePopperClose}>
                      <Link
                        className={classes.Link}
                        href={toOpennableUrl(link.url)}
                        target="_blank"
                        onClick={handleLinkClick}
                      >
                        <Typography className={classes.Text} variant="body1" textAlign="center">
                          {transformURL(link.url)}
                        </Typography>
                      </Link>
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
      <GenericDialog
        open={openDialog}
        title="Manage Links"
        dismissIcon={<CloseRounded />}
        positiveAction={{
          buttonText: "SAVE",
          disabled: inEditMode,
          onClick: onSubmit,
        }}
        negativeAction
        classes={{
          root: classes.Dialog,
        }}
        onClose={handleDiaglogClose}
        onNegativeClick={handleDiaglogClose}
      >
        <Stack gap={1} padding={1}>
          {fields.map((field, index) => {
            const { url: value, editing, deleting, fromServer } = getFormLink(index) || {};
            const hasError = !!errors?.links?.[index]?.url;

            return (
              <TextField
                key={field.id}
                variant={editing ? "outlined" : "standard"}
                multiline
                disabled={!editing}
                maxRows={2}
                InputProps={{
                  ...px({
                    endAdornment: (
                      <Stack direction="row">
                        <IconButton
                          disabled={deleting}
                          onClick={(event: MouseEvent<HTMLButtonElement>) => handleEditModeToggle(event, index)}
                        >
                          <Maybe condition={!editing}>
                            <Tooltip title="Edit link">
                              <EditRounded fontSize="small" color={!deleting ? "primary" : "disabled"} />
                            </Tooltip>
                          </Maybe>
                          <Maybe condition={editing}>
                            <Tooltip title="Commit link changes">
                              <CheckRounded fontSize="small" color="primary" />
                            </Tooltip>
                          </Maybe>
                        </IconButton>
                        <Maybe condition={!fromServer || !editing}>
                          <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
                          <IconButton
                            onClick={(event: MouseEvent<HTMLButtonElement>) => handleDeletionToggle(event, index)}
                          >
                            <Maybe condition={!deleting}>
                              <Maybe condition={fromServer}>
                                <Tooltip title="Delete link">
                                  <DeleteRounded fontSize="small" color="primary" />
                                </Tooltip>
                              </Maybe>
                              <Maybe condition={!fromServer}>
                                <Tooltip title="Remove newly added link">
                                  <CancelRounded fontSize="small" color="error" />
                                </Tooltip>
                              </Maybe>
                            </Maybe>
                            <Maybe condition={deleting}>
                              <UndoRounded fontSize="small" color="primary" />
                            </Maybe>
                          </IconButton>
                        </Maybe>
                      </Stack>
                    ),
                  }),
                  classes: {
                    root: classes.InputRoot,
                    input: classes.Input,
                  },
                  sx: {
                    textDecoration: deleting ? "line-through" : "none",
                  },
                }}
                defaultValue={value}
                {...register(`links.${index}.url`)}
                placeholder={`Add ${index > 0 ? "another" : ""} link`}
                error={hasError}
                helperText={hasError ? errors?.links?.[index]?.url?.message : null}
                onKeyDown={(event: KeyboardEvent<HTMLInputElement>) => handleKeyDown(event, index)}
              />
            );
          })}
          <Stack alignItems="flex-end" pr={0.75}>
            <IconButton color="primary" disabled={!canAddNewLink} onClick={() => append(DEFAULT_EMPTY_LINK)}>
              <Tooltip title="Add another link">
                <AddCircleRounded />
              </Tooltip>
            </IconButton>
          </Stack>
        </Stack>
      </GenericDialog>
    </Box>
  );
};

export default RegistryLinks;
