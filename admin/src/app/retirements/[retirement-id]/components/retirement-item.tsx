import {
  DocumentType,
  PermissionEnum,
  VintageAssetResponse,
  RetirementRelations,
  RetirementResponse,
  RetirementStatus,
  RetirementQuery,
} from "@rubiconcarbon/shared-types";
import React, { useMemo } from "react";
import { Grid, Card, CardContent } from "@mui/material";
import { UploadFile } from "@mui/icons-material";
import { MISSING_DATA } from "@constants/constants";
import useNavigation from "@/hooks/use-navigation";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";
import { toRetirementModel } from "@utils/helpers/transaction/to-transaction-models";
import { AllTransactionType } from "@models/transaction";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import BackButton from "@components/ui/back-button/back-button";
import CustomButton from "@components/ui/custom-button/custom-button";
import ItemDetails from "@components/ui/details/item-details";
import ProductName from "@components/ui/product-name/product-name";
import PublicStatus from "@components/ui/public-status/public-status";
import StatusChip from "@components/ui/status-chip/StatusChip";
import RetirementAttachment from "../../components/retirement-attachment";
import RetirementDetailsTable from "./retirement-details-table";

import classes from "../styles/retirement-item.module.scss";

const RetirementItem = ({
  retirementResponse: serverRetirementResponse,
}: {
  retirementResponse: RetirementResponse;
}): JSX.Element => {
  const { id: retirementId } = serverRetirementResponse || {};
  const { currentPath, pushToPath } = useNavigation();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const { data: retirementResponse, trigger: refreshRetirement } = useTriggerRequest<
    RetirementResponse,
    object,
    object,
    RetirementQuery
  >({
    url: `/admin/retirements/${retirementId}`,
    queryParams: {
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
        RetirementRelations.LINKS,
        RetirementRelations.REQUESTED_BY,
      ],
    },
    optimisticData: serverRetirementResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to fetch retirement/transfer.");
        logger.error(`Unable to fetch retirement/transfer with id ${retirementId}. Error: ${error?.message}`, {});
      },
    },
  });

  const { id, uiKey, amount, status, type, assets, retirement, transfer, memo, dateStarted, dateFinished } =
    toRetirementModel(retirementResponse) || {};

  const isTransfer = useMemo(() => type === AllTransactionType.TRANSFER_OUTFLOW, [type]);
  const customerPortfolio = useMemo(
    () => (isTransfer ? transfer : retirement)?.customerPortfolio,
    [isTransfer, retirement, transfer],
  );
  const organization = useMemo(() => customerPortfolio?.organization, [customerPortfolio?.organization]);
  const isPortfolioAssets = useMemo(() => Object.hasOwn(assets?.at?.(0) || {}, "rct"), [assets]);
  const associatedVintages = useMemo(
    () =>
      assets?.reduce(
        (vintages: VintageAssetResponse[], asset) => [
          ...vintages,
          ...(isPortfolioAssets ? (asset?.associatedVintages ?? []) : [asset as unknown as VintageAssetResponse]),
        ],
        [],
      ),
    [assets, isPortfolioAssets],
  );

  const {
    documents: [retirementCertificate],
    fetching,
    fetch,
  } = useDocumentsApi({
    query: {
      relatedKey: uiKey,
      types: [DocumentType.RETIREMENT_CERTIFICATE],
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!organization?.id && !!customerPortfolio?.id && !!uiKey) {
      setTimeout(async () => await fetch());
    }
  }, [customerPortfolio?.id, organization?.id, uiKey]);

  const canUploadRetirementCertificate = useMemo(
    () => !isTransfer && !retirementCertificate && status === RetirementStatus.PROCESSING,
    [isTransfer, retirementCertificate, status],
  );

  const retirementCompHandler = (): void => {
    pushToPath(`retirement-composition`);
  };

  return (
    <Grid container spacing={0} direction="column" alignItems="center" gap={2}>
      <Maybe condition={!!retirementResponse}>
        <Card variant="elevation" sx={{ width: "100%", borderRadius: 4, backgroundColor: "#FAFAFA" }}>
          <CardContent>
            <Grid container spacing={0}>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Type:" value={isTransfer ? "Transfer" : "Retirement"} sx={{ paddingBottom: 1 }} />
              </Grid>
            </Grid>
            <Grid container spacing={0}>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Status:" value={<StatusChip status={status} />} />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails
                  label="Product:"
                  value={<ProductName assets={assets} style={{ fontSize: "0.875rem" }} />}
                  sx={{
                    alignItems: "center",
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Date/Time Requested:" value={dateStarted ?? MISSING_DATA} />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Transaction Key:" value={uiKey} />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Amount of Credits:" value={numberFormat(amount)} />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails label="Date/Time Completed:" value={dateFinished ?? MISSING_DATA} />
              </Grid>
              <Grid item xs={12} md={4}>
                <ItemDetails
                  label="Organization:"
                  value={
                    organization ? (
                      <CustomerPortfolio portfolio={customerPortfolio} style={{ fontSize: 14 }} />
                    ) : (
                      MISSING_DATA
                    )
                  }
                />
              </Grid>
              <Maybe condition={!isTransfer}>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Beneficiary:" value={retirement?.beneficiary ?? MISSING_DATA} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Public/Private:" value={<PublicStatus isPublic={retirement?.isPublic} />} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Memo:" value={memo ?? MISSING_DATA} />
                </Grid>
                <Grid item xs={12} md={4}></Grid>
                <Maybe condition={canUploadRetirementCertificate}>
                  <Grid item container xs={12} md={4} justifyContent="start" alignItems="center">
                    <Link href={`${currentPath}/upload-retirement-certification`} className={classes.Link}>
                      <UploadFile fontSize="small" /> <span>upload retirement certification</span>
                    </Link>
                  </Grid>
                </Maybe>
              </Maybe>
              <Maybe condition={isTransfer}>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Transfer Credits to Account:" value={transfer?.registryAccount ?? MISSING_DATA} />
                </Grid>
              </Maybe>
              <Maybe condition={!!retirementCertificate}>
                <Grid item container xs={7} sm={4} md={3} lg={2.5} justifyContent="start" alignItems="center">
                  <RetirementAttachment
                    retirementCertificate={retirementCertificate}
                    renderFileAs="button"
                    onDelete={fetch}
                  />
                </Grid>
              </Maybe>
            </Grid>
            <Grid container spacing={0}>
              <Grid item={true} xs={12} mt={1}>
                <Grid container direction="row" justifyContent="flex-start" alignItems="flex-start"></Grid>
              </Grid>
              <Grid container spacing={0} mt={3}>
                <Grid item={true} xs={12}>
                  <BackButton popInstead />
                  <Maybe condition={status === RetirementStatus.PORTFOLIO_MANAGER_REVIEW && isPortfolioAssets}>
                    <CustomButton
                      requiredPermission={PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW}
                      onClickHandler={retirementCompHandler}
                      style={{ height: "30px", marginLeft: "10px" }}
                    >
                      Edit Composition
                    </CustomButton>
                  </Maybe>
                </Grid>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Maybe>

      <Maybe
        condition={
          status !== RetirementStatus.CANCELED &&
          !!associatedVintages?.length &&
          Object?.hasOwn(associatedVintages?.at(0), "projectVintage")
        }
      >
        <RetirementDetailsTable id={id} rows={associatedVintages} refreshRetirement={refreshRetirement} />
      </Maybe>
    </Grid>
  );
};

export default RetirementItem;
