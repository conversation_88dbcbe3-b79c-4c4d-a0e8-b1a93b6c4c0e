import React, { use<PERSON>allback, useMemo } from "react";
import {
  PermissionEnum,
  RetirementLink,
  RetirementResponse,
  uuid,
  VintageAssetResponse,
} from "@rubiconcarbon/shared-types";
import { MISSING_DATA } from "@constants/constants";
import { FileDownloadRounded } from "@mui/icons-material";
import { numberFormat } from "@rubiconcarbon/frontend-shared";
import dateRangeFormatter from "@/utils/formatters/date-range-formatter";
import GenericTable from "@components/ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import TableBox from "@components/ui/table-box/table-box";
import { COLUMNS } from "../constants/details-columns";
import { ExtendedRetirementDetailsModel } from "../types/extended-retirement-details-model";
import RegistryLinks from "./registry-links";

interface RetirementDetailTableProps {
  id: uuid;
  rows: VintageAssetResponse[];
  refreshRetirement: () => Promise<RetirementResponse>;
}

export default function RetirementDetailsTable(props: RetirementDetailTableProps): JSX.Element {
  const { id, rows, refreshRetirement } = props;

  const tableRows = useMemo(() => rows?.filter(({ amount }) => !!amount), [rows]);

  const toRowModel = useCallback(
    (row: VintageAssetResponse & { links: RetirementLink[] }): GenericTableRowModel<ExtendedRetirementDetailsModel> => {
      return {
        id: row?.projectVintage?.id,
        ...row,
        suspendedF: row?.projectVintage?.project?.suspended ? "Yes" : "No",
        rctStandardF: row?.projectVintage?.project?.rctStandard ? "Yes" : "No",
        intervalF: dateRangeFormatter(row?.projectVintage?.interval as string),
        amountF: numberFormat(row?.amount, { fallback: MISSING_DATA }),
        linksF:
          row?.links.reduce((accum, link, index) => accum.concat(index > 0 ? ", " : "").concat(link?.url), "") ||
          MISSING_DATA,
      };
    },
    [],
  );

  const { table } = useGenericTableUtility<ExtendedRetirementDetailsModel>({});

  const columns: GenericTableColumn<ExtendedRetirementDetailsModel>[] = useMemo(
    () =>
      COLUMNS.reduce((accum, column) => {
        return [
          ...accum,
          ["Registry confirmation"].includes(column.label)
            ? {
                ...column,
                renderDataCell:
                  column.label === "Registry confirmation"
                    ? (row: ExtendedRetirementDetailsModel): JSX.Element => (
                        <RegistryLinks
                          id={id}
                          projectVintageId={row?.projectVintage?.id}
                          links={row?.links}
                          refreshRetirement={refreshRetirement}
                        />
                      )
                    : column.renderDataCell,
              }
            : column,
        ];
      }, []),
    [id, refreshRetirement],
  );

  return (
    <TableBox>
      <GenericTable
        id="Retirement-Info"
        toRowModel={toRowModel}
        columns={columns}
        pageableData={{
          data: tableRows,
          page: {
            offset: 0,
            limit: 500,
            size: tableRows?.length,
            totalCount: tableRows?.length,
          },
        }}
        sort={{
          sorts: {
            "projectVintage.project": "asc",
          },
        }}
        globalSearch={{
          searchKeys: [
            "projectVintage.project.name",
            "projectVintage.project.registryProjectId",
            "projectVintage.name",
            "suspendedF",
            "rctStandardF",
            "intervalF",
            "amount",
            "amountF",
            "linksF",
          ],
        }}
        export={{
          filename: `Retirement-Info-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.RETIREMENTS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
        ]}
      />
    </TableBox>
  );
}
