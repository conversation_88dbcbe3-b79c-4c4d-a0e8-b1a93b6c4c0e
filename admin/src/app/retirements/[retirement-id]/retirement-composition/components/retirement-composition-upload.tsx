import React, { useCallback, useRef, useState } from "react";
import useAuth from "@providers/auth-provider";
import { isEmpty, isNil } from "lodash";
import { AllocationResponse, PermissionEnum, TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import { Box, Button, Tooltip } from "@mui/material";
import integerFormat from "@/utils/formatters/integer-format";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { styled } from "@mui/material/styles";
import Papa from "papaparse";
import {
  Severity,
  UploadReport,
  validateColumns,
  UploadRow,
  ProjectVintageTableRow,
  convertStringToNumber,
  validateMissingRowValues,
} from "./retirement-composition-model";
import RetirementCompositionUploadModal from "./retirement-composition-upload-dialog";
import COLORS from "@components/ui/theme/colors";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const buttonSX = {
  width: "130px",
  height: 35,
  color: "rgba(255, 255, 255, 1)",
  borderColor: COLORS.lightGrey,
  borderRadius: "5px",
  backgroundColor: "rgba(9, 68, 54, 1)",
  "&.Mui-disabled": {
    color: "gray !important",
  },
  "&:hover": {
    backgroundColor: "rgba(9, 68, 54, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
  },
};

interface RetirementCompositionUploadModalProps {
  sumOriginalAmount: number;
  vintageAllocations: AllocationResponse[];
  onVintagesUpload: (compositionData: ProjectVintageTableRow[]) => void;
}

export default function RetirementCompositionUpload({
  sumOriginalAmount,
  vintageAllocations: bookVintages,
  onVintagesUpload,
}: RetirementCompositionUploadModalProps): JSX.Element | null {
  const inputFile = useRef(null);
  const { enqueueSuccess } = useSnackbarVariants();
  const [showReportDialog, setShowReportDialog] = useState<boolean>(false);
  const [uploadResult, setUploadResult] = useState<UploadReport>();
  const { user: loginUser } = useAuth();

  const handleParseResults = useCallback(
    (results: any, bookVintages: AllocationResponse[]): void => {
      let uploadReport: UploadReport;
      const parseArr: UploadRow[] = results.data;
      if (parseArr.length > 0) {
        const columns = Object.keys(parseArr[0]);
        const columnsValidation = validateColumns(columns);
        if (columnsValidation.length > 0) {
          uploadReport = {
            title: "Upload failed",
            alerts: columnsValidation,
          };
        } else {
          uploadReport = {
            title: "Upload completed with errors",
            alerts: [],
          };
          const compositionData: ProjectVintageTableRow[] = [];
          let totalQuantity = 0;

          parseArr.forEach((row, index) => {
            const missingValues = validateMissingRowValues(row, index + 1);
            if (!isEmpty(missingValues)) {
              uploadReport.alerts.push({
                severity: Severity.warning,
                message: missingValues,
              });
              return;
            }

            if (
              compositionData.find((cd) => cd.registryProjectId === row.projectID && cd.vintageName === row.vintage)
            ) {
              uploadReport.alerts.push({
                severity: Severity.warning,
                message: `Vintage ${row.projectID} - ${row.vintage} exists more than once. Only first row was included.`,
              });
              return;
            }

            const rowQuantity = convertStringToNumber(row.quantity);
            if (!Number.isInteger(rowQuantity) || rowQuantity < 0) {
              uploadReport.alerts.push({
                severity: Severity.warning,
                message: `Vintage ${row.projectID} - ${row.vintage} quantity value ${row.quantity} is invalid`,
              });
              return;
            }

            const bookComponent = bookVintages.find(
              (bc) => bc.asset.registryProjectId === row.projectID && bc.asset.label === row.vintage,
            );

            const vintage = bookComponent?.detailedAsset as TrimmedProjectVintageResponse;
            if (!bookComponent || !vintage) {
              uploadReport.alerts.push({
                severity: Severity.warning,
                message: `Vintage ${row.projectID} - ${row.vintage} doesn't exist in portfolio`,
              });
              return;
            }

            if (bookComponent.amountAllocated < +row.quantity) {
              uploadReport.alerts.push({
                severity: Severity.warning,
                message: `Vintage ${row.projectID} - ${row.vintage} requested quantity (${integerFormat(row.quantity)}) exceeds unallocated amount (${integerFormat(bookComponent.amountAllocated)})`,
              });
              return;
            }

            compositionData.push({
              id: vintage.id,
              projectName: vintage.project?.name,
              registryProjectId: row.projectID,
              projectType: vintage.project?.projectType?.type,
              location: vintage.project?.country?.name,
              vintageName: vintage.name,
              interval: (vintage.interval ?? "")?.toString(),
              amountTransacted: convertStringToNumber(row.quantity),
              buffer: !isNil(vintage?.riskBufferPercentage) ? +vintage.riskBufferPercentage : undefined,
              costBasis: +(vintage.averageCostBasis ?? 0),
              amountUnallocated: bookComponent.amountAllocated,
              isHide: false,
            });

            totalQuantity += convertStringToNumber(row.quantity);
          });

          if (totalQuantity !== sumOriginalAmount) {
            uploadReport.alerts.push({
              severity: Severity.warning,
              message: `Total quantity should be equal to ${integerFormat(sumOriginalAmount)}`,
            });
          }
          if (!!compositionData && compositionData.length > 0) {
            //setVintages(compositionData);
            onVintagesUpload(compositionData);
          }
        }
      } else {
        uploadReport = {
          title: "Upload failed",
          alerts: [
            {
              severity: Severity.error,
              message: "Uploaded file is empty",
            },
          ],
        };
      }

      if (uploadReport.alerts.length > 0) {
        setUploadResult(uploadReport);
        setShowReportDialog(true);
      } else {
        enqueueSuccess("Successfully uploaded data");
      }
    },
    [enqueueSuccess, onVintagesUpload, sumOriginalAmount],
  );

  const uploadCSVHandler = useCallback(
    (e: any): void => {
      const uploadReport: UploadReport = {
        title: "Upload failed",
        alerts: [],
      };

      if (e !== null && e?.target?.files?.length === 1) {
        if (bookVintages?.length > 0) {
          Papa.parse(e.target.files[0], {
            header: true,
            skipEmptyLines: true,
            transformHeader: function (h: string) {
              if (!isEmpty(h)) {
                const trimmed = h.replace(/\s/g, "");
                return trimmed[0].toLowerCase() + trimmed.slice(1);
              }

              return h;
            },
            complete: (results: any) => handleParseResults(results, bookVintages),
          });
        } else {
          uploadReport.alerts.push({
            severity: Severity.error,
            message: "Vintages information is not available",
          });
        }
      } else {
        uploadReport.alerts.push({
          severity: Severity.error,
          message: "Could not process uploaded file",
        });
      }

      inputFile.current.value = "";
      inputFile.current.type = "text";
      inputFile.current.type = "file";
    },
    [bookVintages, handleParseResults],
  );

  const tooltipText = !loginUser?.hasPermission(PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW) ? (
    <div>
      Insufficient permissions.
      <br />
      <NAME_EMAIL>.
    </div>
  ) : (
    ""
  );

  return (
    <Box>
      <Tooltip title={tooltipText} placement="bottom">
        <Button
          component="label"
          role={undefined}
          variant="contained"
          disabled={!loginUser.hasPermission(PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW)}
          tabIndex={-1}
          sx={buttonSX}
          onChange={uploadCSVHandler}
        >
          Upload CSV
          <VisuallyHiddenInput type="file" accept=".csv" ref={inputFile} />
        </Button>
      </Tooltip>
      <RetirementCompositionUploadModal
        isOpen={showReportDialog}
        uploadResult={uploadResult}
        onClose={() => setShowReportDialog(false)}
      />
    </Box>
  );
}
