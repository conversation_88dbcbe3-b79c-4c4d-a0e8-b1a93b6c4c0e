import React from "react";
import { Box, Typography, Table, TableBody, TableCell, TableContainer, TableRow, Stack } from "@mui/material";
import ConfirmationModal from "@components/ui/dialogs/confirmation-dialog";
import ReportGmailerrorredIcon from "@mui/icons-material/ReportGmailerrorred";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { Severity, UploadReport } from "./retirement-composition-model";
import { BaseDialogProps } from "@models/dialogs";

interface RetirementCompositionUploadModalProps extends BaseDialogProps {
  isOpen: boolean;
  uploadResult: UploadReport;
  onClose: () => void;
}

export default function RetirementCompositionUploadModal({
  isOpen,
  uploadResult,
  onClose,
}: RetirementCompositionUploadModalProps): JSX.Element | null {
  return (
    <Box>
      <ConfirmationModal isOpen={isOpen} onClose={onClose} title={uploadResult?.title} onCloseButtonLabel={"CLOSE"}>
        <TableContainer sx={{ width: 500, maxHeight: 500, marginTop: "-10px", marginBottom: "-10px" }}>
          <Table aria-label="composition summary table">
            <TableBody>
              {uploadResult?.alerts?.map((alert, idx) => (
                <TableRow key={idx}>
                  <TableCell align="left" sx={{ borderBottom: "none" }}>
                    <Stack direction="row">
                      <Box sx={{ paddingTop: "0px" }}>
                        {alert.severity === Severity.error ? (
                          <ReportGmailerrorredIcon sx={{ color: "red" }} />
                        ) : (
                          <WarningAmberIcon sx={{ color: "orange" }} />
                        )}
                      </Box>
                      <Typography variant="body1" sx={{ paddingTop: "2px", paddingLeft: "8px" }}>
                        {alert.message}
                      </Typography>
                    </Stack>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </ConfirmationModal>
    </Box>
  );
}
