import { Project } from "@models/project";
import { TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import { TransactionProjectVintage } from "@models/transaction-project-vintage";
import { isEmpty } from "lodash";

export interface ExtendedTrimmedProjectVintageResponse extends TrimmedProjectVintageResponse {
  amountAvailable: number;
}

export enum Severity {
  warning,
  error,
}

export interface Alert {
  severity: Severity;
  message: string;
}

export interface UploadReport {
  title: string;
  alerts: Alert[];
}

export interface UploadRow {
  projectID: string;
  vintage: string;
  quantity: string;
}

export interface IFormField {
  error: boolean;
  message: string;
}

export interface IProject extends IFormField {
  value: Project | null;
}

export interface IVintage extends IFormField {
  value: ExtendedTrimmedProjectVintageResponse | null;
}

export interface IQuantity extends IFormField {
  value: number | null;
}

export interface ProjectVintageTableRow extends TransactionProjectVintage {
  isHide?: boolean;
}

export const validateColumns = (columns: string[]): Alert[] => {
  const alerts: Alert[] = [];

  if (!columns.includes("projectID")) {
    alerts.push({
      severity: Severity.error,
      message: "Import file is missing Project ID column",
    });
  }

  if (!columns.includes("vintage")) {
    alerts.push({
      severity: Severity.error,
      message: "Import file is missing Vintage column",
    });
  }

  if (!columns.includes("quantity")) {
    alerts.push({
      severity: Severity.error,
      message: "Import file is missing Quantity column",
    });
  }

  return alerts;
};

export const validateMissingRowValues = (row: UploadRow, rowIndex: number): string => {
  const message: string[] = [];

  if (isEmpty(row.projectID)) {
    message.push("Project ID");
  }

  if (isEmpty(row.vintage)) {
    message.push("Vintage");
  }

  if (isEmpty(row.projectID)) {
    message.push("Quantity");
  }

  if (message.length > 0) {
    return `${message.join(", ")} ${message.length > 1 ? "are" : "is"} missing in row number ${rowIndex}`;
  }

  return "";
};

export function convertStringToNumber(input: string): number {
  if (input) return +input.replaceAll(",", "").replaceAll("$", "").replaceAll("%", "");
}
