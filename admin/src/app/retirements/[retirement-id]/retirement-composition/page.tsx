import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, RetirementResponse, RetirementRelations } from "@rubiconcarbon/shared-types";
import RetirementComposition from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { isValidElement } from "react";

/**
 * Retirement Composition Page
 *
 * This is a server component that renders the Retirement Composition page
 */
export default async function RetirementCompositionPage({
  params,
}: {
  params: Promise<{ "retirement-id": string }>;
}): Promise<JSX.Element> {
  const { "retirement-id": id } = await params;

  const retirementResponse = await withErrorHandling(async () =>
    baseApiRequest<RetirementResponse>(
      `admin/retirements/${id}?${generateQueryParams({
        includeRelations: [
          RetirementRelations.CUSTOMER_PORTFOLIO,
          RetirementRelations.ASSETS,
          RetirementRelations.RCT_VINTAGES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(retirementResponse)) return retirementResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_READ, PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW]}>
      <RetirementComposition retirementResponse={retirementResponse as RetirementResponse} />
    </AuthorizeServer>
  );
}
