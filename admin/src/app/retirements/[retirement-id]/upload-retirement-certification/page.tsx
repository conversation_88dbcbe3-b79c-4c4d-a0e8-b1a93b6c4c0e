import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum, RetirementResponse, RetirementRelations } from "@rubiconcarbon/shared-types";
import RetirementUpload from "./components";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { isValidElement } from "react";

/**
 * Upload Retirement Certification Page
 *
 * This is a server component that renders the Upload Retirement Certification page
 */
export default async function RetirementUploadPage({
  params,
}: {
  params: Promise<{ "retirement-id": string }>;
}): Promise<JSX.Element> {
  const { "retirement-id": id } = await params;

  const retirementResponse = await withErrorHandling(async () =>
    baseApiRequest<RetirementResponse>(
      `admin/retirements/${id}?${generateQueryParams({
        includeRelations: [RetirementRelations.CUSTOMER_PORTFOLIO],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(retirementResponse)) return retirementResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_WRITE]}>
      <RetirementUpload retirementResponse={retirementResponse as RetirementResponse} />
    </AuthorizeServer>
  );
}
