"use client";

import Page from "@components/layout/containers/page";
import { RetirementResponse } from "@rubiconcarbon/shared-types";
import RetirementUploadComponent from "./retirement-upload";

export default function RetirementUpload({
  retirementResponse,
}: {
  retirementResponse: RetirementResponse;
}): JSX.Element {
  return (
    <Page>
      <RetirementUploadComponent retirementResponse={retirementResponse} />
    </Page>
  );
}
