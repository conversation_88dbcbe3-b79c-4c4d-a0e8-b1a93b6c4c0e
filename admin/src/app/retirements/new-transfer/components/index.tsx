"use client";

import { AdminBookQueryResponse } from "@rubiconcarbon/shared-types";
import NewTransferFormComponent from "./new";
import Page from "@components/layout/containers/page";

const NewTransfer = ({
  customerPortfoliosResponse,
}: {
  customerPortfoliosResponse: AdminBookQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <NewTransferFormComponent customerPortfoliosResponse={customerPortfoliosResponse} />
    </Page>
  );
};

export default NewTransfer;
