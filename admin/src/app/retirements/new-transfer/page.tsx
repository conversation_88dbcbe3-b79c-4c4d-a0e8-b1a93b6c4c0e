import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookQueryResponse,
  BookAction,
  BookRelations,
  BookType,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import NewTransferForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * New Transfer Page
 *
 * This is a server component that renders the New Transfer page
 */
export default async function NewTransferPage(): Promise<JSX.Element> {
  const customerPortfoliosResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: false,
        includeRelations: [BookRelations.ORGANIZATION],
        types: [BookType.PORTFOLIO_CUSTOMER],
        allowedAction: BookAction.TRANSFER_OUTFLOW,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(customerPortfoliosResponse)) return customerPortfoliosResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_CREATE]}>
      <NewTransferForm customerPortfoliosResponse={customerPortfoliosResponse as AdminBookQueryResponse} />
    </AuthorizeServer>
  );
}
