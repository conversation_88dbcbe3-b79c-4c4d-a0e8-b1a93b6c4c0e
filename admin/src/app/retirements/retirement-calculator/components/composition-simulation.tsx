import React, { useState, BaseSyntheticEvent, useContext, use<PERSON><PERSON>back, useMemo, SyntheticEvent } from "react";
import { useRouter } from "next/navigation";
import { Box, Grid, Card, CardActions, CardContent, TextField, Button, Autocomplete, Typography } from "@mui/material";
import { NumericFormat } from "react-number-format";
import { AxiosContext } from "@providers/axios-provider";
import { convertAmountToNumber } from "@utils/helpers/general/general";
import { numberFormat } from "@rubiconcarbon/frontend-shared";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { CsvBuilder } from "filefy";
import { MISSING_DATA } from "@constants/constants";
import integerFormat from "@/utils/formatters/integer-format";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { BookEstimateRetirementResponse, AdminBookQueryResponse, AdminBookResponse } from "@rubiconcarbon/shared-types";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

interface CompositionExport {
  requestDate: Date;
  projectId: string;
  projectName: string;
  projectCountry?: string;
  projectType: string;
  quantity: number;
  vintage: string;
}

const formatRowValue = (row: CompositionExport, exportColDef: ExportColDef): string => {
  if (!row[exportColDef.name]) return MISSING_DATA;

  if (exportColDef?.formatter) {
    return exportColDef.formatter.func(row[exportColDef.name]);
  }

  return row[exportColDef.name];
};

interface IExportFormatter {
  func: (input: string | number | Date | boolean | Map<string, string | number | Date>) => any;
}

interface ExportColDef {
  name: string;
  displayName: string;
  formatter?: IExportFormatter;
}

const exportColumns: ExportColDef[] = [
  {
    name: "requestDate",
    displayName: "Request Date",
    formatter: { func: dateFormatterEST },
  },
  {
    name: "projectId",
    displayName: "Project ID",
  },
  {
    name: "projectName",
    displayName: "Project Name",
  },
  {
    name: "vintage",
    displayName: "Vintage",
  },
  {
    name: "projectCountry",
    displayName: "Project Country",
  },
  {
    name: "projectType",
    displayName: "Project Type",
  },
  {
    name: "quantity",
    displayName: "Quantity",
    formatter: { func: integerFormat },
  },
];

const exportSimulation = (data: CompositionExport[]): void => {
  const columns = exportColumns?.map((colDef) => colDef.displayName);
  const rows = data.map((row) => exportColumns.map((colDef) => formatRowValue(row, colDef)));
  const csvBuilder = new CsvBuilder("retirement_composition_simulation.csv");
  csvBuilder.setColumns(columns).addRows(rows).exportFile();
};

interface Quantity {
  value?: string;
  error?: boolean;
  message?: string;
  tooltip?: string;
}

function convertStringToNumber(input: string): number {
  if (input) return +input.replaceAll(",", "").replaceAll("$", "").replaceAll("%", "");
  return 0;
}

export default function CompositionSimulation({
  booksResponse,
}: {
  booksResponse: AdminBookQueryResponse;
}): JSX.Element {
  const { api } = useContext(AxiosContext);
  const router = useRouter();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);

  const [selectedBook, setSelectedBook] = useState<Partial<AdminBookResponse>>(null);
  const [isConfirmationOpen, setIsConfirmationOpen] = useState<boolean>(false);
  const [quantity, setQuantity] = useState<Quantity>({
    value: "",
    error: false,
    message: "",
    tooltip: "",
  });

  const books: AdminBookResponse[] = useMemo(
    () =>
      booksResponse?.data?.filter((book) => book.isEnabled && book.assetAllocations?.totalAmountAllocated > 0) || [],
    [booksResponse?.data],
  );

  const bookOptions = useAutoCompleteOptions<AdminBookResponse, Partial<AdminBookResponse>>({
    data: books,
    keys: ["id", "name", "assetAllocations"],
    label: (entry: AdminBookResponse) => entry?.name,
    value: (entry: AdminBookResponse) => entry,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const handleSelectionChange = (
    event: SyntheticEvent,
    entry: UseAutoCompleteOptionsReturnEntry<Partial<AdminBookResponse>>,
  ): void => {
    event?.preventDefault();
    setSelectedBook(entry?.value);
    setQuantity({
      value: "",
      error: false,
      message: entry?.value?.assetAllocations
        ? `${numberFormat(entry?.value?.assetAllocations.totalAmountAvailable)} available`
        : "",
      tooltip: "",
    });
  };

  // todo : @kofi this uses "amount allocated" but says available, should this be available or allocated?
  const handleQuantityChange = (event): void => {
    const newQuantity = convertStringToNumber(event.target.value);
    if (newQuantity == 0) {
      setQuantity({
        value: newQuantity.toString(),
        error: true,
        message: "Quantity must be greater than 0",
      });
    } else {
      if (
        !!selectedBook?.assetAllocations?.totalAmountAllocated &&
        newQuantity <= selectedBook.assetAllocations?.totalAmountAllocated
      ) {
        setQuantity({
          value: newQuantity.toString(),
          error: false,
          message: `${numberFormat(selectedBook?.assetAllocations.totalAmountAvailable)} available`,
        });
      } else {
        setQuantity({
          value: newQuantity.toString(),
          error: true,
          message: `Quantity can't exceed allocated amount (${numberFormat(selectedBook?.assetAllocations?.totalAmountAvailable)})`,
        });
      }
    }
  };

  const onSubmit = useCallback(
    (event: BaseSyntheticEvent): void => {
      event.preventDefault();

      if (quantity.error) return;

      setIsConfirmationOpen(true);
    },
    [quantity.error],
  );

  const onConfirm = useCallback((): void => {
    setIsConfirmationOpen(false);
    const quantityRequested = convertAmountToNumber(quantity.value);

    setSubmissionInFlight(true);

    api
      .get<BookEstimateRetirementResponse[]>(
        `admin/books/${selectedBook.id}/retirement-calculations/${quantityRequested}`,
      )
      .then((response) => {
        const currentDate = new Date();
        const compositionExport: CompositionExport[] = response.data.map((element) => {
          return {
            requestDate: currentDate,
            projectId: element.projectVintage.project.registryProjectId,
            projectName: element.projectVintage.project.name,
            vintage: element.projectVintage.name,
            projectCountry: element.projectVintage.project?.country.name,
            projectType: element.projectVintage.project?.projectType.name,
            quantity: element.amountTransacted,
          };
        });

        exportSimulation(compositionExport);
        enqueueSuccess("Successfully generated retirement composition simulation.");
      })
      .catch((e) => {
        enqueueError(`Unable to generate retirement composition simulation. ${e?.message ?? ""}`);
      })
      .finally(() => {
        setSubmissionInFlight(false);
      });
  }, [selectedBook?.id, api, enqueueError, enqueueSuccess, quantity.value]);

  const dialogButtons: ButtonDef[] = useMemo(
    () => [
      {
        label: "Confirm",
        variant: "contained",
        onClickHandler: () => onConfirm(),
      },
    ],
    [onConfirm],
  );

  const itemDtailsCard = (
    <React.Fragment>
      <CardContent>
        <form id="retirement-request-form" onSubmit={onSubmit}>
          <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
            <Grid container gap={4} flexDirection="column">
              <Autocomplete
                id="portfolio"
                options={bookOptions}
                isOptionEqualToValue={(option, value) => option.value?.id === value?.value?.id}
                onChange={(event, entry) => handleSelectionChange(event, entry)}
                disablePortal
                renderInput={(params) => <TextField required {...params} label="Portfolio" />}
                renderOption={(props, option) => (
                  <Box component="li" {...props} key={option.value?.id}>
                    <span
                      key={option.value?.id}
                      style={{
                        height: "24px",
                        width: "4px",
                        marginRight: "4px",
                        marginLeft: "-8px",
                        background: getPortfolioColor(option.value?.id),
                      }}
                    />
                    {option.label}
                  </Box>
                )}
              />
              <NumericFormat
                required
                value={quantity?.value}
                defaultValue={0}
                decimalScale={0}
                inputProps={{ maxLength: 14 }}
                allowNegative={false}
                label="Quantity"
                customInput={TextField}
                type="text"
                thousandSeparator={","}
                disabled={!selectedBook}
                onChange={handleQuantityChange}
                error={quantity?.error}
                helperText={quantity?.message}
              />
            </Grid>
          </fieldset>
        </form>
      </CardContent>
      <CardActions
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Box ml={2} sx={{ display: "flex" }}></Box>
        <Box mr={2} mb={1} mt={1} sx={{ display: "flex" }}>
          <Button
            sx={{ marginRight: 3, fontWeight: 600 }}
            disabled={submissionInFlight}
            variant="text"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="retirement-request-form"
            sx={{ px: 3.5, color: "#FFFFFF" }}
            disabled={submissionInFlight}
          >
            Submit
          </Button>
        </Box>
      </CardActions>
    </React.Fragment>
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <ConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          The retirement simulation tool reflects the current system allocation. <br></br>Please note, any changes to
          portfolio allocations or actual retirements may affect the outcome of the actual retirement.
        </Typography>
      </ConfirmationModal>
    </Box>
  );
}
