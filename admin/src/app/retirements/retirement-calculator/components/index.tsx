"use client";

import { AdminBookQueryResponse } from "@rubiconcarbon/shared-types";
import CompositionSumulationComponent from "./composition-simulation";
import Page from "@components/layout/containers/page";

export default function RetirementCalculator({
  booksResponse,
}: {
  booksResponse: AdminBookQueryResponse;
}): JSX.Element {
  return (
    <Page>
      <CompositionSumulationComponent booksResponse={booksResponse} />
    </Page>
  );
}
