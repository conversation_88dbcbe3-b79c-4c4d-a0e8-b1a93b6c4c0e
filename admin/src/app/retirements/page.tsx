import { AuthorizeServer } from "@app/authorize-server";
import {
  OrderByDirection,
  PermissionEnum,
  RetirementOrderByOptions,
  RetirementQueryResponse,
  RetirementRelations,
} from "@rubiconcarbon/shared-types";
import Retirements from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";
import { withErrorHandling } from "../data-server";
import { baseApiRequest, generateQueryParams } from "../libs/server";

/**
 * Retirements Page
 *
 * This is a server component that renders the Retirements page
 */
export default async function RetirementsPage(): Promise<JSX.Element> {
  const retirementsResponse = await withErrorHandling(async () =>
    baseApiRequest<RetirementQueryResponse>(
      `admin/retirements?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        includeTotalCount: true,
        includeRelations: [
          RetirementRelations.CUSTOMER_PORTFOLIO,
          RetirementRelations.ASSETS,
          RetirementRelations.RCT_VINTAGES,
          RetirementRelations.REQUESTED_BY,
        ],
        orderBys: [`${RetirementOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(retirementsResponse)) return retirementsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RETIREMENTS_READ]}>
      <Retirements retirementsResponse={retirementsResponse as RetirementQueryResponse} />
    </AuthorizeServer>
  );
}
