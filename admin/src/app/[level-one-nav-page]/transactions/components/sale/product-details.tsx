import {
  <PERSON><PERSON>,
  Autocomplete,
  Box,
  Divider,
  Grid,
  Icon<PERSON>utton,
  <PERSON><PERSON>,
  TextField,
  <PERSON>ltip,
  Typo<PERSON>,
} from "@mui/material";
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldErrors,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFormResetField,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
} from "react-hook-form";
import { AddCircleRounded, DeleteRounded, InfoRounded } from "@mui/icons-material";
import {
  AllKeys,
  calculator,
  currencyFormat,
  Maybe,
  numberFormat,
  toDecimal,
  toNumber,
} from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import {
  AllocationResponse,
  AssetType,
  AdminBookResponse,
  uuid,
  AssetTypeGroupedAllocationResponse,
} from "@rubiconcarbon/shared-types";
import { NumericFormat } from "react-number-format";
import { BookTypeToLabel } from "@constants/book-type-to-label";
import { Fragment, useCallback, useEffect, useMemo } from "react";
import { MISSING_DATA } from "@constants/constants";
import { useToggle } from "react-use";
import { NonTradeAssetDetails, TransactionModel, AssetOrder } from "@models/transaction";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import { PrimitiveTypeBook } from "@models/primitive-type-book";

import classes from "../../styles/product-details.module.scss";

type LineItemProps = {
  index: number;
  type: AssetType;
  currentAccumulatedAssetQuantities: Record<uuid, number>;
  productOptions: UseAutoCompleteOptionsReturnEntry<
    NonTradeAssetDetails & { projectName?: string; registryProjectId?: string }
  >[];
  bookOptions: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>[];
} & Pick<ProductDetailsProps, "loadingAssets" | "control" | "errors" | "trigger" | "setValue" | "watch" | "remove">;

type ProductDetailsProps = {
  allocations: AllocationResponse[];
  books: AdminBookResponse[];
  loadingAssets: boolean;
  control: Control<TransactionModel, any>;
  errors: FieldErrors<TransactionModel>;
  lineItems: FieldArrayWithId<TransactionModel, "assets", "id">[];
  trigger: UseFormTrigger<TransactionModel>;
  setValue: UseFormSetValue<TransactionModel>;
  resetField: UseFormResetField<TransactionModel>;
  watch: UseFormWatch<TransactionModel>;
  append: UseFieldArrayAppend<TransactionModel, "assets">;
  remove: UseFieldArrayRemove;
};

type ProductAutoCompleteValue = NonTradeAssetDetails & { projectName?: string; registryProjectId?: string };

const parserBlacklist = ["$", ","];

const LineItem = ({
  index,
  type,
  currentAccumulatedAssetQuantities = {},
  productOptions = [],
  bookOptions = [],
  loadingAssets,
  control,
  errors,
  setValue,
  trigger,
  watch,
  remove,
}: LineItemProps): JSX.Element => {
  const customerPortfolio = watch("sale.customerPortfolio");
  const productType = watch("sale.productType");
  const lineItem = watch(`assets.${index}`);
  const asset = lineItem?.supplementaryAssetDetails;
  const book = lineItem?.source;

  const [focused, setFocused] = useToggle(false);

  const lineItemErrors = useMemo(() => errors?.assets?.at?.(index), [errors?.assets, index]);
  const isPortfolioSelection = useMemo(() => type === AssetType.RCT, [type]);
  const grossAvailable = useMemo(() => toNumber(asset?.available), [asset?.available]);
  const netAvailable = useMemo(
    () =>
      calculator(grossAvailable)
        .subtract(currentAccumulatedAssetQuantities?.[asset?.id] || 0)
        .calculate()
        .toNumber(),
    [asset?.id, grossAvailable, currentAccumulatedAssetQuantities],
  );
  const positiveAvailability = useMemo(() => netAvailable >= 0, [netAvailable]);

  useEffect(() => {
    setValue(`assets.${index}.amountAvailable`, grossAvailable);
  }, [grossAvailable, index, setValue]);

  useEffect(() => {
    const subscription = watch(({ assets = [] }, { name, type }) => {
      if (type === "change") {
        const item = assets?.at(index);

        const amount = toDecimal(item?.amount, { parserBlacklist, treatNothingAsNaN: true });

        switch (name) {
          case `assets.${index}.supplementaryAssetDetails`:
            if (!assets?.at(index)?.supplementaryAssetDetails) {
              setValue(`assets.${index}.amount`, "" as any);
              setValue(`assets.${index}.rawPrice`, "" as any);
              setValue(`assets.${index}.source`, undefined);
            } else {
              const {
                sourceId: id,
                sourceType: type,
                sourceName: name,
              } = assets?.at(index)?.supplementaryAssetDetails || {};
              setValue(`assets.${index}.source`, { id, type, name } as any);
            }

            if (!amount?.isNaN()) trigger(`assets.${index}.amount`);

            break;
          case `assets.${index}.unitPrice`:
            if (!amount.isNaN() && !amount.isZero())
              setValue(
                `assets.${index}.rawPrice`,
                calculator(item?.unitPrice, { parserBlacklist })
                  .multiply(amount)
                  .calculate()
                  .toDecimalPlaces(2)
                  .toString(),
                {
                  shouldValidate: true,
                },
              );
            else setValue(`assets.${index}.rawPrice`, undefined, { shouldValidate: true });
            break;
          case `assets.${index}.amount`:
            if (!amount.isNaN() && !amount.isZero())
              setValue(
                `assets.${index}.rawPrice`,
                calculator(item?.unitPrice, { parserBlacklist })
                  .multiply(amount)
                  .calculate()
                  .toDecimalPlaces(2)
                  .toString(),
                {
                  shouldValidate: true,
                },
              );
            else setValue(`assets.${index}.rawPrice`, undefined, { shouldValidate: true });
            break;
          case `assets.${index}.rawPrice`:
            if (!amount.isNaN() && !amount.isZero())
              setValue(
                `assets.${index}.unitPrice`,
                calculator(item?.rawPrice, { parserBlacklist, treatNothingAsNaN: true })
                  .divide(item?.amount)
                  .calculate()
                  .toDecimalPlaces(2)
                  .toString(),
                {
                  shouldValidate: true,
                },
              );
            break;
          default:
            break;
        }
      }
    });
    return (): void => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [index, watch]);

  useEffect(() => {
    if ((asset?.type === "portfolio" && !isPortfolioSelection) || (asset?.type === "vintage" && isPortfolioSelection)) {
      setValue(`assets.${index}.supplementaryAssetDetails`, null);
      setValue(`assets.${index}.source`, null);
    }
  }, [asset?.type, index, isPortfolioSelection, setValue]);

  const hasError = (field: AllKeys<AssetOrder>): boolean => !!lineItemErrors?.[field];

  const onFocus = (): void => setFocused(true);

  const onBlur = (): void => setFocused(false);

  return (
    <Grid className={classes.BodyRow} container item alignItems="start" gap={0.5}>
      <Grid className={classes.BodyCell} item xs={3.5}>
        <Controller
          name={`assets.${index}.supplementaryAssetDetails`}
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = productOptions.find(
              (option) => value?.id === option?.value?.id && value?.sourceId === option?.value?.sourceId,
            );

            return (
              <Stack gap={0.5}>
                <Autocomplete
                  options={productOptions}
                  getOptionDisabled={(option) => option?.disabled}
                  value={selectedOption || null}
                  loading={loadingAssets}
                  onChange={(_, selection) => onChange(selection?.value)}
                  id="asset"
                  renderInput={({ InputProps, ...params }) => (
                    <Box position="relative">
                      <TextField
                        {...params}
                        InputProps={{
                          ref,
                          ...InputProps,
                          sx: {
                            position: "relative",
                            zIndex: 1,
                            color: focused ? "black" : "transparent",
                          },
                        }}
                        label="Search and select product"
                        {...otherProps}
                        error={!!lineItemErrors?.supplementaryAssetDetails}
                        helperText={
                          !hasError("supplementaryAssetDetails") && !productType
                            ? "Please select Product Type"
                            : lineItemErrors?.supplementaryAssetDetails?.message
                        }
                        fullWidth
                        onFocus={onFocus}
                        onBlur={onBlur}
                      />
                      <Maybe condition={!focused}>
                        <Box
                          sx={{
                            position: "absolute",
                            top: 10,
                            left: 13,
                            right: 60,
                            bottom: 10,
                            display: "flex",
                            alignContent: "center",
                            zIndex: 2,
                            pointerEvents: "none",
                          }}
                        >
                          <Stack justifyContent="center" alignItems="flex-start">
                            <Maybe condition={selectedOption?.value?.type === "portfolio"}>
                              <Typography variant="body1" color="black">
                                {selectedOption?.value?.name}
                              </Typography>
                            </Maybe>
                            <Maybe condition={selectedOption?.value?.type === "vintage"}>
                              <Typography variant="body1" color="black">
                                {selectedOption?.value?.registryProjectId} - {selectedOption?.value?.name}
                              </Typography>
                              <Typography variant="caption" color="GrayText">
                                {selectedOption?.value?.projectName}
                              </Typography>
                            </Maybe>
                          </Stack>
                        </Box>
                      </Maybe>
                    </Box>
                  )}
                  renderOption={(props, option) => (
                    <li {...props} key={`${option.value.id}-${option.value.sourceId}`}>
                      {option?.displayLabel}
                    </li>
                  )}
                  fullWidth
                  classes={{
                    root: classes.Autocomplete,
                    inputRoot: classes.InputRoot,
                    input: classes.Input,
                  }}
                  disabled={!customerPortfolio || !type}
                />
                <Maybe condition={!!selectedOption && !isPortfolioSelection}>
                  <Stack component="span" direction="row" gap={1} alignItems="center">
                    <Maybe condition={!!selectedOption?.value?.["wholeAsset"]?.["project"]?.["suspended"]}>
                      <SuspendedChip />
                    </Maybe>
                    <RCTEligibilityChip iconOnly={false} vintage={selectedOption?.value?.["wholeAsset"] as any} />
                  </Stack>
                </Maybe>
              </Stack>
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={2.5}>
        <Controller
          name={`assets.${index}.source`}
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const allOptions = bookOptions?.filter((option) => option.value.type === asset?.sourceType);
            const selectedOption = allOptions.find((entry) => entry?.value?.id === value?.id) ?? null;

            return (
              <Autocomplete
                options={allOptions}
                value={selectedOption}
                loading={!allOptions?.length}
                onChange={(_, selection) => onChange(selection?.value)}
                popupIcon={null}
                id="book"
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ref,
                      ...InputProps,
                      readOnly: true,
                    }}
                    label={asset?.sourceId ? "Selected book" : null}
                    {...otherProps}
                    helperText={
                      !hasError("source") && !asset ? "Please select Product" : lineItemErrors?.source?.message
                    }
                    fullWidth
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props} key={option.value.id}>
                    {option?.displayLabel}
                  </li>
                )}
                fullWidth
                classes={{
                  root: classes.Autocomplete,
                  inputRoot: classes.InputRoot,
                  input: classes.Input,
                }}
                disabled={!value && !asset}
                readOnly
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={1.5}>
        <Controller
          control={control}
          name={`assets.${index}.unitPrice`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              decimalScale={2}
              fixedDecimalScale
              prefix="$"
              label="Enter unit price"
              value={value}
              customInput={TextField}
              InputProps={{
                ref,
                classes: {
                  root: classes.InputRoot,
                  input: classes.Input,
                },
              }}
              error={!!lineItemErrors?.unitPrice}
              helperText={
                !hasError("unitPrice") && !asset
                  ? !value
                    ? "Please select Product"
                    : ""
                  : lineItemErrors?.unitPrice?.message
              }
              {...otherProps}
              fullWidth
              disabled={!value && !asset}
            />
          )}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={1.8}>
        <Controller
          control={control}
          name={`assets.${index}.amount`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
            return (
              <NumericFormat
                allowNegative={false}
                thousandSeparator
                decimalScale={0}
                label="Enter quantity"
                value={value}
                isAllowed={({ floatValue }) => !floatValue || floatValue <= grossAvailable}
                customInput={TextField}
                InputProps={{
                  ref,
                  classes: {
                    formControl: classes.FormControl,
                    root: classes.InputRoot,
                    input: classes.Input,
                  },
                }}
                error={!!lineItemErrors?.amount}
                helperText={
                  !hasError("amount") ? (
                    !asset || !book ? (
                      `Please select ${!asset ? "Product" : "Book"}`
                    ) : positiveAvailability && !!grossAvailable ? (
                      `Available: ${numberFormat(grossAvailable)}`
                    ) : null
                  ) : (
                    <Stack component="span" gap={0.5}>
                      <Maybe condition={!positiveAvailability && !!grossAvailable}>
                        <span>Available: {numberFormat(grossAvailable)}</span>
                      </Maybe>
                      <Maybe
                        condition={
                          lineItemErrors?.amount?.type !== "maxOfField" ||
                          (lineItemErrors?.amount?.type === "maxOfField" && !positiveAvailability && !!grossAvailable)
                        }
                      >
                        <span>{lineItemErrors?.amount?.message}</span>
                      </Maybe>
                    </Stack>
                  )
                }
                {...otherProps}
                fullWidth
                disabled={!asset || !book}
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={1.8}>
        <Controller
          control={control}
          name={`assets.${index}.rawPrice`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              decimalScale={2}
              fixedDecimalScale
              prefix="$"
              label="Enter total price"
              value={value}
              customInput={TextField}
              InputProps={{
                ref,
                classes: {
                  root: classes.InputRoot,
                  input: classes.Input,
                },
              }}
              error={!!lineItemErrors?.rawPrice}
              helperText={!hasError("rawPrice") && !asset ? "Please select Product" : lineItemErrors?.rawPrice?.message}
              {...otherProps}
              fullWidth
              disabled={!asset}
            />
          )}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={0.6} paddingTop="8px">
        <IconButton onClick={() => remove(index)}>
          <DeleteRounded />
        </IconButton>
      </Grid>
    </Grid>
  );
};

const ProductDetails = ({
  allocations = [],
  books = [],
  loadingAssets,
  control,
  errors,
  lineItems,
  trigger,
  setValue,
  resetField,
  watch,
  append,
  remove,
}: ProductDetailsProps): JSX.Element => {
  const type = watch("sale.productType");
  const watchedLineItems = watch("assets");
  const selectedAssetIds = watchedLineItems
    ?.map(({ supplementaryAssetDetails: sale }) => `${sale?.id}-${sale?.sourceId}`)
    ?.filter((id) => !!id);

  const currentAccumulatedAssetQuantities = watchedLineItems?.reduce(
    (record, { supplementaryAssetDetails: sale, source, amount }) => {
      if (!!sale?.id && !!source?.id) {
        if (record?.[sale.id]) record[sale.id] += toNumber(amount, { parserBlacklist });
        else record[sale.id] = toNumber(amount, { parserBlacklist });
      }

      return record;
    },
    {} as Record<uuid, number>,
  );

  const isPortfolioSelection = useMemo(() => type === AssetType.RCT, [type]);

  const productOptions = useAutoCompleteOptions<AllocationResponse, ProductAutoCompleteValue>({
    data: allocations?.filter((allocation) =>
      isPortfolioSelection
        ? !Object.hasOwn(allocation?.detailedAsset, "project")
        : Object.hasOwn(allocation?.detailedAsset, "project"),
    ),
    keys: [
      "asset",
      "detailedAsset",
      "owner",
      "amountAllocated",
      "amountAvailable",
      "amountPendingPurchase",
      "amountPendingRetirement",
      "amountPendingCustomerTransferOutflow",
      "amountPendingBuy",
      "amountPendingSell",
    ],
    label: (entry) =>
      isPortfolioSelection
        ? entry?.detailedAsset?.name
        : `${entry?.detailedAsset?.["project"]?.["registryProjectId"]} - ${entry?.detailedAsset?.name} - ${entry.detailedAsset?.["project"]?.["name"]} - ${entry?.owner.name}`,
    displayLabel: (entry) => {
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <Maybe condition={isPortfolioSelection}>
            <span>{entry?.detailedAsset?.name}</span>
          </Maybe>
          <Maybe condition={!isPortfolioSelection}>
            <Stack gap={1}>
              <span>
                {entry?.detailedAsset?.["project"]?.["registryProjectId"]} - {entry?.detailedAsset?.name}
              </span>
              <Typography variant="button" color="GrayText" fontSize={12} fontWeight="bold">
                {entry?.owner.name}
              </Typography>
              <Typography variant="caption" color="GrayText">
                {entry?.detailedAsset?.["project"]?.["name"]}
              </Typography>
              <Stack direction="row" gap={1} alignItems="center">
                <Maybe condition={!!entry?.detailedAsset?.["project"]?.["suspended"]}>
                  <SuspendedChip />
                </Maybe>
                <RCTEligibilityChip iconOnly={false} vintage={entry?.detailedAsset as any} />
              </Stack>
            </Stack>
          </Maybe>
          <Typography variant="caption" color="GrayText">
            {numberFormat(entry?.amountAvailable)}
          </Typography>
        </Stack>
      );
    },
    disabled: (entry) => selectedAssetIds.includes(`${entry?.detailedAsset?.id}-${entry?.owner.id}`),
    value: (entry) => ({
      id: entry?.asset?.id,
      sourceId: entry?.owner?.id,
      type: isPortfolioSelection ? "portfolio" : "vintage",
      sourceType: entry?.owner?.type,
      name: entry?.asset?.[isPortfolioSelection ? "name" : "label"],
      sourceName: entry?.owner?.name,
      available: entry?.amountAvailable,
      projectName: entry?.detailedAsset?.["project"]?.["name"],
      registryProjectId: entry?.asset?.registryProjectId,
      wholeAsset: entry?.detailedAsset,
    }),
    postTransform: (options) =>
      options
        .filter((option) => !!option?.value?.available)
        .sort((a, b) => {
          const firstOrder = !isPortfolioSelection
            ? a.value?.registryProjectId?.localeCompare(b.value?.registryProjectId)
            : a.value?.name?.localeCompare(b.value?.name);

          if (!firstOrder) return a.value?.name?.localeCompare(b.value?.name);

          return firstOrder;
        }),
  });

  const bookOptions = useAutoCompleteOptions<PrimitiveTypeBook>({
    data: books as unknown as PrimitiveTypeBook[],
    keys: ["id", "type", "name", "ownerAllocationsByAssetType"],
    label: (entry) => entry?.name,
    displayLabel: (entry) => {
      const allocsByAssetType: AssetTypeGroupedAllocationResponse[] = entry?.ownerAllocationsByAssetType || [];
      const { groupedPrices } =
        allocsByAssetType?.find(({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE) || {};

      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <span>{BookTypeToLabel[entry?.type]}</span>
          <Typography variant="caption" color="GrayText">
            {currencyFormat(
              calculator(groupedPrices?.totalPriceAllocated)
                .add(groupedPrices?.totalPricePendingBuy)
                .subtract(groupedPrices?.totalPricePendingSell)
                .calculate()
                .toNumber(),
            )}
          </Typography>
        </Stack>
      );
    },
    value: (entry) => entry,
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  const addNewLineItem = useCallback((): void => {
    const asset = new AssetOrder();
    asset.isASale = true;
    asset.isAPurchase = true;

    append(asset);
    if (errors?.assets) resetField("assets", { keepDirty: false, keepError: false });
  }, [append, errors?.assets, resetField]);

  return (
    <Stack className={classes.ProductDetails} gap={1}>
      <Typography color="black">Product Details</Typography>
      <Grid className={classes.Table} container>
        <Grid className={classes.Header} container item gap={0.5}>
          <Grid className={classes.HeaderCell} item xs={3.5}>
            <Typography variant="body2">Product</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={2.5}>
            <Typography variant="body2">Book</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={1.5} container gap={0.5} alignItems="center">
            <Typography variant="body2">Unit Price</Typography>
            <Tooltip title="Total Price / Quantity" sx={{ cursor: "pointer" }}>
              <InfoRounded fontSize="small" htmlColor="gray" />
            </Tooltip>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={1.8}>
            <Typography variant="body2">Quantity</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={1.8} container gap={0.5} alignItems="center">
            <Typography variant="body2">Total Price</Typography>
            <Tooltip title="Unit Price * Quantity" sx={{ cursor: "pointer" }}>
              <InfoRounded fontSize="small" htmlColor="gray" />
            </Tooltip>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={0.6}>
            <Typography variant="body2">Actions</Typography>
          </Grid>
        </Grid>
        <Grid className={classes.Body} container item justifyContent={"center"} gap={1}>
          <Maybe condition={!!lineItems?.length}>
            {lineItems.map((lineItem, index) => (
              <Fragment key={lineItem?.id}>
                <LineItem
                  index={index}
                  type={type}
                  currentAccumulatedAssetQuantities={currentAccumulatedAssetQuantities}
                  productOptions={productOptions}
                  bookOptions={bookOptions}
                  loadingAssets={loadingAssets}
                  control={control}
                  errors={errors}
                  trigger={trigger}
                  setValue={setValue}
                  watch={watch}
                  remove={remove}
                />
                <Maybe condition={index !== lineItems?.length - 1}>
                  <Divider sx={{ width: "100%" }} />
                </Maybe>
              </Fragment>
            ))}
          </Maybe>
          <Maybe condition={!lineItems?.length}>
            <Alert
              classes={{
                root: `${classes.Alert}${errors?.assets ? ` ${classes.Error}` : ""}`,
                icon: classes.Icon,
                message: classes.Message,
              }}
              severity={errors?.assets ? "error" : "info"}
            >
              <Typography>Please add a product entry</Typography>
            </Alert>
          </Maybe>
        </Grid>
        <Maybe condition={!!lineItems?.length}>
          <Grid className={classes.Footer} container item justifyContent={"center"} gap={0.5}>
            <Divider sx={{ width: "100%" }} />
            <Grid className={classes.FooterRow} container item alignItems="center" gap={1} justifyContent="flex-end">
              <Grid item xs={7.4} />
              <Grid
                className={classes.FooterCell}
                container
                item
                xs={1.8}
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography className={classes.Label} variant="caption">
                  Total Quantity:
                </Typography>
                <Typography className={classes.Value} variant="body2">
                  {numberFormat(
                    watchedLineItems?.reduce((sum, { amount }) => sum + toNumber(amount, { parserBlacklist }), 0),
                    { fallback: MISSING_DATA },
                  )}
                </Typography>
              </Grid>
              <Grid
                className={classes.FooterCell}
                container
                item
                xs={1.8}
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography className={classes.Label} variant="caption">
                  Grand Total:
                </Typography>
                <Typography className={classes.Value} variant="body2">
                  {currencyFormat(
                    watchedLineItems?.reduce((sum, { rawPrice }) => sum + toNumber(rawPrice, { parserBlacklist }), 0),
                    { fallback: MISSING_DATA },
                  )}
                </Typography>
              </Grid>
              <Grid item xs={0.7} />
            </Grid>
          </Grid>
        </Maybe>
      </Grid>
      <Box display="flex" justifyContent="flex-end">
        <IconButton color="primary" onClick={addNewLineItem}>
          <AddCircleRounded fontSize="large" />
        </IconButton>
      </Box>
    </Stack>
  );
};

export default ProductDetails;
