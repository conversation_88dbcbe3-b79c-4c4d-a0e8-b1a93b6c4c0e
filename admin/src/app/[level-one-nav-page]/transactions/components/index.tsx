"use client";

import { TransactionQueryResponse, PurchaseQueryResponse, TradeQueryResponse } from "@rubiconcarbon/shared-types";
import TransactionsComponent from "./transactions";

export default function Transactions({
  transactionsResponse,
  purchasesResponse,
  tradesResponse,
}: {
  transactionsResponse: TransactionQueryResponse;
  purchasesResponse: PurchaseQueryResponse;
  tradesResponse: TradeQueryResponse;
}): JSX.Element {
  return (
    <TransactionsComponent
      transactionsResponse={transactionsResponse}
      purchasesResponse={purchasesResponse}
      tradesResponse={tradesResponse}
    />
  );
}
