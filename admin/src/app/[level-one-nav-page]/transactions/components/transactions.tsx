import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography } from "@mui/material";
import {
  GenericTabI<PERSON>,
  GenericTabKey,
  GenericTabs,
  Match,
  Maybe,
  Nullable,
  px,
  Undefinable,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import { JSX, useMemo, useState } from "react";
import {
  PermissionEnum,
  PurchaseQuery,
  PurchaseQueryResponse,
  PurchaseRelations,
  TradeQuery,
  TradeQueryResponse,
  TradeRelation,
  DocumentType,
  uuid,
  BookType,
  AdminPurchaseDeliverRequest,
  AdminPurchaseExecuteRequest,
  TradeDeliverRequest,
  TradeExecuteRequest,
  TransactionQueryResponse,
  TransactionQuery,
  TransactionType,
  PurchaseOrderByOptions,
  OrderByDirection,
  TransactionOrderByOptions,
  TradeConfirmationRequest,
  TradeConfirmationResponse,
} from "@rubiconcarbon/shared-types";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useToggle } from "react-use";
import { TransactionUpdatableStatus, TransactionUpdateStatus } from "@constants/transaction-status";
import useNavigation from "@/hooks/use-navigation";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { projectIsRCTApproved } from "@utils/helpers/project/project-is-rct-approved";
import usePerformantState from "@/hooks/use-perfomant-state";
import { Dayjs } from "dayjs";
import { bookIsBreached } from "@utils/helpers/portfolio/book-is-breached";
import { useStoreProvider } from "@providers/store-provider";
import {
  toTrimmedTransactionModel,
  toPurchaseModel,
  toTradeModel,
} from "@/utils/helpers/transaction/to-transaction-models";
import useAuth from "@providers/auth-provider";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { AllTransactionType, TransactionModel, TransactionTableFormModel } from "@models/transaction";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import COLORS from "@components/ui/theme/colors";
import GenericTable from "@components/ui/generic-table";
import { TRANSACTION_COLUMNS } from "../constants/transactions-columns";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { SALE_COLUMNS } from "../constants/sales-columns";
import { GenericTableDocumentsButtonExtensions } from "@components/ui/generic-table/types/generic-table-documents-button";
import { TRADE_COLUMNS } from "../constants/trades-columns";
import { DocumentTypeUILabel } from "@constants/documents";
import DocumentsModal from "@components/documents-upload/documents-modal";
import StatusModal from "./status-modal";
import AutomatedEmailConfirmationModal from "./trade/automated-email-confirmation-modal";

import classes from "../styles/transactions.module.scss";

type TabsTypes = "transactions" | "sales" | "trades";

const TransactionTableFormResolver = classValidatorResolver(TransactionTableFormModel);
const getDocumentTypeByStatus = (
  status: TransactionUpdateStatus,
): Nullable<DocumentType.PROOF_OF_CONFIRMATION | DocumentType.CONTRACT | DocumentType.PROOF_OF_DELIVERY> => {
  switch (status) {
    case TransactionUpdateStatus.BIND:
      return DocumentType.PROOF_OF_CONFIRMATION;
    case TransactionUpdateStatus.EXECUTE:
      return DocumentType.CONTRACT;
    case TransactionUpdateStatus.DELIVER:
      return DocumentType.PROOF_OF_DELIVERY;
    default:
      return null;
  }
};

const Transactions = ({
  transactionsResponse: serverTransactionsResponse,
  purchasesResponse: serverPurchasesResponse,
  tradesResponse: serverTradesResponse,
}: {
  transactionsResponse: TransactionQueryResponse;
  purchasesResponse: PurchaseQueryResponse;
  tradesResponse: TradeQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { user } = useAuth();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { pushToPath } = useNavigation();
  const { ephemeralState, updateEphemeralState } = useStoreProvider();

  const [exhaustedEmailConfirmationFetch, setExhaustedEmailConfirmationFetch] = useToggle(false);
  const [openStatusDialog, toggleStatusDialog] = useToggle(false);
  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [openEmailConfirmationPreview, toggleOpenEmailConfirmationPreview] = useToggle(false);

  const [viewingRow, setViewingRow] = useState<Undefinable<GenericTableRowModel<TransactionModel>>>();
  const [tradeEmailConfirmation, setTradeEmailConfirmation] = useState<TradeConfirmationResponse>();

  const [statusRequestPayload, setStatusRequestPayload] = usePerformantState<
    | TradeExecuteRequest
    | AdminPurchaseExecuteRequest
    | TradeDeliverRequest
    | AdminPurchaseDeliverRequest
    | TradeConfirmationRequest
  >(null);
  const [executeStep, setExecuteStep] = usePerformantState<TransactionUpdatableStatus>(null);
  const [deliveryDate, setDeliveryDate] = usePerformantState<Dayjs>(null);

  const hasSalesPermission = useMemo(() => user?.hasPermission(PermissionEnum.CUSTOMER_SALES_READ), [user]);
  const hasTradesPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRADES_READ), [user]);

  const {
    data: transactionsResponse,
    isMutating: loadingTransactions,
    trigger: getTransactions,
  } = useTriggerRequest<TransactionQueryResponse, object, object, TransactionQuery>({
    url: "/admin/transactions",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      types: [
        hasSalesPermission ? TransactionType.PURCHASE : null,
        hasTradesPermission ? TransactionType.TRADE : null,
      ]?.filter((entry) => !!entry),
      orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
    },
    optimisticData: serverTransactionsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch sale and trade transactions.");
        logger.error(`Unable to fetch sale and trade transactions. Error: ${error?.message}`, {});
      },
    },
  });

  const {
    data: purchasesResponse,
    isMutating: loadingPurchases,
    trigger: getPurchases,
  } = useTriggerRequest<PurchaseQueryResponse, object, object, PurchaseQuery>({
    url: "/admin/purchases",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      orderBys: [`${PurchaseOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
      includeRelations: [PurchaseRelations.ASSETS, PurchaseRelations.CUSTOMER_PORTFOLIO],
    },
    optimisticData: serverPurchasesResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch customer sales.");
        logger.error(`Unable to fetch customer sales. Error: ${error?.message}`, {});
      },
    },
  });

  const {
    data: tradesResponse,
    isMutating: loadingTrades,
    trigger: getTrades,
  } = useTriggerRequest<TradeQueryResponse, object, object, TradeQuery>({
    url: "/admin/trades",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      includeRelations: [TradeRelation.BOOK, TradeRelation.PROJECT_VINTAGE],
    },
    optimisticData: serverTradesResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch trades.");
        logger.error(`Unable to fetch trades. Error: ${error?.message}`, {});
      },
    },
  });

  const { isMutating: loadingTradeEmailConfirmation, trigger: getTradeEmailConfirmation } =
    useTriggerRequest<TradeConfirmationResponse>({
      url: "admin/trades/{id}/confirmation",
      swrOptions: {
        onSuccess: (data: TradeConfirmationResponse): void => {
          setExhaustedEmailConfirmationFetch(true);
          setTradeEmailConfirmation(data);
        },
        onError: (error: any) => {
          if (error?.status !== 404) {
            enqueueError("Unable to fetch trade email confirmation.");
            logger.error(`Unable to fetch trade email confirmation. Error: ${error?.message}`, {});
          }
          setExhaustedEmailConfirmationFetch(true);
        },
      },
    });

  const { form, table } = useGenericTableUtility<TransactionModel>({
    form: {
      mode: "onSubmit",
      resolver: TransactionTableFormResolver,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { watch, setValue: sv } = form || {};
  const setValue = sv as any;

  const firstValue = watch("amends.0");

  const amendingRowId = firstValue?.id as uuid;
  const inStatusAmend = firstValue?.inStatusAmend;
  const selectedType = firstValue?.type;
  const currentStatus = firstValue?.currentStatus;
  const selectedStatus = firstValue?.status;
  const currentRawPrice = firstValue?.assets?.at(0)?.rawPrice; // todo: should be verified for multi asset types

  const { transactions } = ephemeralState;
  const { viewing: tab } = transactions;

  const tabs = useMemo(
    () =>
      [
        {
          key: "transactions",
          data: "All Transactions",
        },
        hasSalesPermission
          ? {
              key: "sales",
              data: "Customer Sales",
            }
          : null,
        hasTradesPermission
          ? {
              key: "trades",
              data: "Spot Trades",
            }
          : null,
      ]?.filter((entry) => !!entry),
    [hasSalesPermission, hasTradesPermission],
  ) as GenericTabItem<TabsTypes, string>[];

  const isTransaction = useMemo(() => tab === "transactions", [tab]);
  const isSale = useMemo(() => !isTransaction && tab === "sales", [isTransaction, tab]);
  const selectedBook = useMemo(() => firstValue?.assets?.at(0)?.source as PrimitiveTypeBook, [firstValue?.assets]);
  const bookBreached = useMemo(() => bookIsBreached(selectedBook, currentRawPrice), [currentRawPrice, selectedBook]);
  const rctApproved = useMemo(() => {
    return selectedBook
      ? selectedBook?.type === BookType.PORTFOLIO_DEFAULT
        ? projectIsRCTApproved(firstValue?.assets?.at(0)?.projectVintage as any) // todo: should be verified for multi asset types
        : true
      : false;
  }, [firstValue?.assets, selectedBook]);
  const blockStatusUpdates = useMemo(
    () => !rctApproved && selectedStatus === TransactionUpdateStatus.EXECUTE && selectedType === AllTransactionType.BUY,
    [rctApproved, selectedStatus, selectedType],
  );
  const currentDocumentType = useMemo(
    () => getDocumentTypeByStatus(selectedStatus as TransactionUpdateStatus),
    [selectedStatus],
  );

  const { trigger: updateTradeStatus, isMutating: updatingTradeStatus } = useTriggerRequest({
    url: `admin/${isSale ? "purchases" : "trades"}/{id}/{status}`,
    method: "patch",
    pathParams: {
      id: amendingRowId,
      status: selectedStatus,
    },
    requestBody: [TransactionUpdateStatus.EXECUTE, TransactionUpdateStatus.DELIVER].includes(
      selectedStatus as TransactionUpdateStatus,
    )
      ? statusRequestPayload
      : null,
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess(`Successfully updated ${isSale ? "purchase" : "trade"} status.`);

        setTimeout(async () => {
          setExecuteStep(null);
          onDeliverDateChange(null);
          cleanupOnStatusDialogClose();

          if (isSale) await getPurchases();
          else await getTrades();

          await getTransactions();
        });
      },
      onError: () => {
        enqueueError(`Unable to update ${isSale ? "purchase" : "trade"} status.`);

        setTimeout(() => {
          setExecuteStep(null);
          onDeliverDateChange(null);
          cleanupOnStatusDialogClose();
        });
      },
    },
  });

  usePerformantEffect(() => {
    if (
      openDocumentsModal &&
      tab === "trades" &&
      !tradeEmailConfirmation &&
      !loadingTradeEmailConfirmation &&
      !exhaustedEmailConfirmationFetch
    )
      setTimeout(async () => await getTradeEmailConfirmation({ pathParams: { id: viewingRow?.id } }));
    else if (!openDocumentsModal) {
      setExhaustedEmailConfirmationFetch(false);
      setTradeEmailConfirmation(null);
    }
  }, [
    exhaustedEmailConfirmationFetch,
    loadingTradeEmailConfirmation,
    openDocumentsModal,
    tab,
    tradeEmailConfirmation,
    viewingRow?.id,
  ]);

  usePerformantEffect(() => {
    if (!!amendingRowId && !!selectedStatus && currentStatus !== selectedStatus) toggleStatusDialog(true);
  }, [amendingRowId, selectedStatus]);

  usePerformantEffect(() => {
    if (executeStep) {
      setStatusRequestPayload({
        updatableStatusOrder:
          executeStep === "pending_delivery"
            ? ["pending_delivery", "pending_payment"]
            : ["pending_payment", "pending_delivery"],
      });
    } else if (deliveryDate) {
      setStatusRequestPayload({
        assetsDeliveredAt: deliveryDate.toDate(),
      });
    }
  }, [deliveryDate, executeStep]);

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={classes.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  const onExecuteStepChange = (value: TransactionUpdatableStatus): void => {
    setExecuteStep(value);
  };

  const onDeliverDateChange = (value: Dayjs): void => {
    setDeliveryDate(value);
  };

  const cleanupOnDocumentsDialogClose = (): void => {
    toggleDocumentsModal(false);
  };

  const cleanupOnStatusDialogClose = (): void => {
    if (inStatusAmend) {
      setValue("amends.0", undefined);
      toggleStatusDialog(false);
    }
  };

  const handleEmailConfirmationPreviewToggle = (): void => toggleOpenEmailConfirmationPreview();

  return (
    <>
      <GenericTabs
        tabs={tabs}
        value={tab}
        renderTab={renderTab}
        onTabChange={(key: GenericTabKey): void => updateEphemeralState("transactions.viewing", key)}
        classes={{
          root: classes.Tabs,
          tab: classes.Tab,
          active: classes.Active,
        }}
      />
      <Match
        value={tab}
        cases={[
          {
            case: "transactions",
            component: (
              <GenericTable
                id="transactions"
                reloadingRow={loadingTransactions}
                pageableData={transactionsResponse}
                columns={TRANSACTION_COLUMNS}
                toRowModel={toTrimmedTransactionModel}
                globalSearch={{
                  searchKeys: [
                    "uiKey",
                    "typeF",
                    "counterpartyName",
                    "productF",
                    "totalQuantity",
                    "totalPrice",
                    "status",
                    "updatedAt",
                  ],
                }}
                export={{
                  filename: `Transactions-${new Date()}`,
                  setClientCanExport: table?.setClientCanExport,
                  bindClientExport: table?.bindClientExport,
                }}
                toolbarActionButtons={[
                  {
                    children: "Export",
                    startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
                    style: DEFAULT_EXPORT_STYLE,
                    isDisabled: loadingPurchases || !table?.clientCanExport,
                    onClickHandler: table?.handleClientExport,
                  },
                ]}
                styles={{
                  root: {
                    maxHeight: "calc(100vh - 250px)",
                  },
                }}
              />
            ),
          },
          {
            case: "sales",
            component: (
              <GenericTable
                id="sales"
                reloadingRow={loadingPurchases}
                pageableData={purchasesResponse}
                columns={SALE_COLUMNS}
                toRowModel={toPurchaseModel}
                globalSearch={{
                  searchKeys: [
                    "uiKey",
                    "sale.customerPortfolio.organization.name",
                    "product",
                    "sale.totalValue",
                    "status",
                    "updatedAt",
                  ],
                }}
                export={{
                  filename: `Customer_Sales-${new Date()}`,
                  setClientCanExport: table?.setClientCanExport,
                  bindClientExport: table?.bindClientExport,
                }}
                toolbarActionButtons={[
                  {
                    children: "Export",
                    startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
                    requiredPermission: PermissionEnum.CUSTOMER_SALES_READ,
                    style: DEFAULT_EXPORT_STYLE,
                    isDisabled: loadingPurchases || !table?.clientCanExport,
                    onClickHandler: table?.handleClientExport,
                  },
                  {
                    children: "New Customer Sale",
                    requiredPermission: PermissionEnum.CUSTOMER_SALES_CREATE,
                    isDisabled: loadingPurchases,
                    onClickHandler: () => pushToPath("/new?type=sale"),
                  },
                ]}
                useForm={table?.useForm}
                extensions={
                  {
                    toggleDocumentsModal,
                    setViewingRow,
                  } as GenericTableDocumentsButtonExtensions
                }
                styles={{
                  root: {
                    maxHeight: "calc(100vh - 250px)",
                  },
                }}
              />
            ),
          },
          {
            case: "trades",
            component: (
              <GenericTable
                id="trades"
                reloadingRow={loadingTrades}
                pageableData={tradesResponse}
                columns={TRADE_COLUMNS}
                toRowModel={toTradeModel}
                globalSearch={{
                  searchKeys: [
                    "uiKey",
                    "trade.counterparties.0.counterparty.name",
                    "product",
                    "type",
                    "assets.0.source.name",
                    "status",
                    "updatedAt",
                  ],
                }}
                export={{
                  filename: `Trades-${new Date()}`,
                  setClientCanExport: table?.setClientCanExport,
                  bindClientExport: table?.bindClientExport,
                }}
                toolbarActionButtons={[
                  {
                    children: "Export",
                    startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
                    requiredPermission: PermissionEnum.TRADES_READ,
                    style: DEFAULT_EXPORT_STYLE,
                    isDisabled: loadingTrades || !table?.clientCanExport,
                    onClickHandler: table?.handleClientExport,
                  },
                  {
                    children: "New Trade",
                    requiredPermission: PermissionEnum.TRADES_CONFIRM_INDICATIVE,
                    isDisabled: loadingTrades,
                    onClickHandler: () => pushToPath("/new?type=trade"),
                  },
                ]}
                useForm={table?.useForm}
                extensions={
                  {
                    toggleDocumentsModal,
                    setViewingRow,
                  } as GenericTableDocumentsButtonExtensions
                }
                styles={{
                  root: {
                    maxHeight: "calc(100vh - 250px)",
                  },
                }}
              />
            ),
          },
        ]}
      />
      <DocumentsModal
        open={openDocumentsModal}
        relatedKey={viewingRow?.uiKey}
        customerPortfolioId={isSale ? viewingRow?.sale?.customerPortfolio?.id : undefined}
        organizationId={isSale ? viewingRow?.sale?.customerPortfolio?.organization?.id : undefined}
        counterpartyId={
          !isSale ? viewingRow?.trade?.counterparties?.find(({ isPrimary }) => isPrimary)?.counterparty?.id : undefined
        }
        getTypeOptions={[
          {
            label: DocumentTypeUILabel[DocumentType.PROOF_OF_CONFIRMATION],
            value: DocumentType.PROOF_OF_CONFIRMATION,
          },
          {
            label: DocumentTypeUILabel[DocumentType.CONTRACT],
            value: DocumentType.CONTRACT,
          },
          //** START: old document type. will not be used going forward for customer sales */
          {
            label: DocumentTypeUILabel[DocumentType.PURCHASE_AGREEMENT],
            value: DocumentType.PURCHASE_AGREEMENT,
          },
          {
            label: DocumentTypeUILabel[DocumentType.RECEIPT],
            value: DocumentType.RECEIPT,
          },
          {
            label: DocumentTypeUILabel[DocumentType.INVOICE],
            value: DocumentType.INVOICE,
          },
          {
            label: DocumentTypeUILabel[DocumentType.PURCHASE_ORDER],
            value: DocumentType.PURCHASE_ORDER,
          },
          {
            label: DocumentTypeUILabel[DocumentType.MASTER_AGREEMENT],
            value: DocumentType.MASTER_AGREEMENT,
          },
          //** END: old document type. will not be used going forward for customer sales */
          {
            label: DocumentTypeUILabel[DocumentType.PROOF_OF_DELIVERY],
            value: DocumentType.PROOF_OF_DELIVERY,
          },
        ]}
        saveTypeOptions={[
          {
            label: DocumentTypeUILabel[DocumentType.PROOF_OF_CONFIRMATION],
            value: DocumentType.PROOF_OF_CONFIRMATION,
          },
          {
            label: DocumentTypeUILabel[DocumentType.CONTRACT],
            value: DocumentType.CONTRACT,
          },
          ...(tab === "trades"
            ? [
                {
                  label: DocumentTypeUILabel[DocumentType.PROOF_OF_DELIVERY],
                  value: DocumentType.PROOF_OF_DELIVERY,
                },
              ]
            : []),
        ]}
        onPositiveClick={(callback) => {
          callback();
        }}
        onNegativeClick={(callback) => {
          callback();
          cleanupOnDocumentsDialogClose();
        }}
        onClose={async (callback, uploaded) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (uploaded) {
            if (isSale) await getPurchases();
            else await getTrades();

            await getTransactions();
          }
        }}
      >
        <Maybe condition={!loadingTradeEmailConfirmation && !!tradeEmailConfirmation}>
          <Stack gap={1.5}>
            <Typography variant="body2" width="100%" fontWeight="bold" padding={1} bgcolor="lightgray">
              Automated Email Confirmation
            </Typography>
            <Stack direction="row" gap={1} padding="0 8px">
              <Typography variant="body2" width={100} fontWeight={600}>
                Reciepient(s):{" "}
              </Typography>
              <Typography color="primary">{tradeEmailConfirmation?.recipientEmails?.join(", ")}</Typography>
            </Stack>
            <Box padding="0 8px">
              <Button
                variant="contained"
                size="small"
                sx={{ width: "fit-content" }}
                onClick={handleEmailConfirmationPreviewToggle}
              >
                Preview Confirmation Details
              </Button>
            </Box>
          </Stack>
        </Maybe>
      </DocumentsModal>
      <StatusModal
        open={openStatusDialog}
        id={firstValue?.id as uuid}
        relatedKey={firstValue?.uiKey}
        updating={updatingTradeStatus}
        bookBreached={bookBreached}
        blockStatusUpdates={blockStatusUpdates}
        type={firstValue?.type}
        status={selectedStatus as TransactionUpdateStatus}
        currentStatus={currentStatus}
        executeStep={executeStep}
        deliveryDate={deliveryDate}
        executeStepOrder={firstValue?.updatableStatusOrder}
        documentType={currentDocumentType}
        counterpartyId={firstValue?.trade?.counterparties?.find(({ isPrimary }) => isPrimary)?.counterparty?.id as uuid}
        onExecuteStepChange={onExecuteStepChange}
        onDeliverDateChange={onDeliverDateChange}
        onUploadSuccess={updateTradeStatus}
        onPositiveClick={(callback, { noUploadRequired, tradeBindingPayload }) => {
          if (noUploadRequired || blockStatusUpdates || !currentDocumentType) {
            setTimeout(async () => {
              await updateTradeStatus({
                ...px(
                  {
                    requestBody: tradeBindingPayload,
                  },
                  [undefined, null],
                ),
              });
              callback();
            });
          }
        }}
        onNegativeClick={(callback) => {
          callback();
          cleanupOnStatusDialogClose();
        }}
        onClose={(callback) => {
          callback();
          cleanupOnStatusDialogClose();
        }}
      />
      <AutomatedEmailConfirmationModal
        open={openEmailConfirmationPreview}
        confirmation={tradeEmailConfirmation}
        onClose={handleEmailConfirmationPreviewToggle}
      />
    </>
  );
};

export default Transactions;
