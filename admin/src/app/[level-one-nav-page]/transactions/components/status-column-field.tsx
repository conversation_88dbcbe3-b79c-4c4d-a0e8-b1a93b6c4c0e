import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { AuthContext } from "@providers/auth-provider";
import { JSX, MouseEvent, useContext, useMemo } from "react";
import { adjustShade, calculator, Maybe } from "@rubiconcarbon/frontend-shared";
import InteractiveProgress, {
  InteractiveProgressState,
  InteractiveProgressStep,
} from "@/components/ui/interactive-progress";
import { Avatar, Box, capitalize, Chip, Divider, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import { useAtomValue, useStore } from "jotai";
import { externalAtom } from "@components/ui/generic-table/state";
import { GenericTableExternal } from "@components/ui/generic-table/types/generic-table-external";
import COLORS from "@components/ui/theme/colors";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { CancelRounded } from "@mui/icons-material";
import { useMeasure } from "react-use";
import {
  TransactionStatus,
  TransactionStatusUpdateOrder,
  TransactionUpdateStatus,
} from "@/constants/transaction-status";
import { AllTransactionType, TransactionModel } from "@models/transaction";

type StatusColumnFieldProps = {
  row: GenericTableRowModel<TransactionModel>;
};

type StatusStep = {
  value: number;
  order: number;
  status: TransactionStatus | TransactionUpdateStatus | "paid" | "delivered";
  state: InteractiveProgressState;
  permitted: boolean;
};

type StatusProps = {
  row: GenericTableRowModel<TransactionModel>;
  status: TransactionStatus | TransactionUpdateStatus | "paid" | "delivered";
  state: InteractiveProgressState;
};

const Status = ({ row, status, state }: StatusProps): JSX.Element => {
  const { amending, isRowActive } = useGenericTableRowState(row);
  const store = useStore();

  const { useForm } = useAtomValue<GenericTableExternal<TransactionModel>>(externalAtom, { store }) ?? {};
  const { setValue: sv } = useForm?.() ?? {};
  const setValue = sv as any;

  const editingTableRow = useMemo(() => amending && !isRowActive, [amending, isRowActive]);
  const border = useMemo(() => `solid 2px ${state === "active" ? "#E0E0E0" : "transparent"}`, [state]);
  const color = useMemo(
    () =>
      state === "completed"
        ? "white"
        : state === "active" && !editingTableRow
          ? COLORS.rubiconGreen
          : adjustShade("#E0E0E0", -50),
    [editingTableRow, state],
  );
  const bgcolor = useMemo(
    () => (state === "completed" ? COLORS.rubiconGreen : state === "inactive" || editingTableRow ? "#E0E0E0" : "white"),
    [editingTableRow, state],
  );

  const handleStatusClick = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    event?.stopPropagation();

    setValue?.("amends.0.id", row?.id);
    setValue?.("amends.0.inStatusAmend", true);
    setValue?.("amends.0.uiKey", row?.uiKey);
    setValue?.("amends.0.currentStatus", row?.currentStatus);
    setValue?.("amends.0.status", status as any);
    setValue?.("amends.0.type", row?.type);
    setValue?.("amends.0.updatableStatusOrder", row?.updatableStatusOrder);
    setValue?.("amends.0.assets.0.rawPrice", row?.assets.at(0)?.rawPrice);
    setValue?.(
      "amends.0.assets.0.projectVintage.riskBufferPercentage",
      row?.assets?.at(0).projectVintage?.riskBufferPercentage,
    );
    setValue?.(
      "amends.0.assets.0.projectVintage.project.rctStandard",
      row?.assets?.at(0).projectVintage?.project?.rctStandard,
    );

    if (row?.type !== AllTransactionType.PURCHASE) {
      setValue?.("amends.0.assets.0.source", row?.assets?.at(0)?.source);
      setValue?.("amends.0.trade.counterparties", row?.trade?.counterparties);
    }

    if (row?.type === AllTransactionType.PURCHASE) {
      setValue?.("amends.0.sale.customerPortfolio", row?.sale?.customerPortfolio);
      setValue?.("amends.0.sale.flowType", row?.sale?.flowType);
      setValue?.("amends.0.sale.productType", row?.sale?.productType);
      setValue?.("amends.0.sale.paymentDueDate", row?.sale?.paymentDueDate);
    }
  };

  return (
    <IconButton
      sx={{
        padding: 0,
        border,
        boxSizing: "border-box",
        boxShadow:
          state === "active"
            ? "rgba(0, 0, 0, 0.2) 0px 3px 1px -2px, rgba(0, 0, 0, 0.14) 0px 2px 2px 0px, rgba(0, 0, 0, 0.12) 0px 1px 5px 0px"
            : "unset",
      }}
      disabled={["completed", "inactive"].includes(state) || editingTableRow}
      onClick={handleStatusClick}
    >
      <Avatar sx={{ color, bgcolor, width: 24, height: 24, fontSize: 16 }}>{capitalize(status?.split("")?.[0])}</Avatar>
    </IconButton>
  );
};

const StatusColumnField = ({ row }: StatusColumnFieldProps): JSX.Element => {
  const [ref, { width }] = useMeasure();
  const { user } = useContext(AuthContext);

  const isSale = row?.type === AllTransactionType.PURCHASE;
  const [firmable, bindable, executable, payable, deliverable, cancelable] = useMemo(
    () => [
      isSale || user.hasPermission(PermissionEnum.TRADES_CONFIRM_FIRM),
      user.hasPermission(PermissionEnum[isSale ? "CUSTOMER_SALES_SET_BINDING" : "TRADES_CONFIRM_BINDING"]),
      user.hasPermission(PermissionEnum[isSale ? "CUSTOMER_SALES_SET_EXECUTED" : "TRADES_CONFIRM_EXECUTED"]),
      user.hasPermission(PermissionEnum[isSale ? "CUSTOMER_SALES_UPDATE_PAYMENT" : "TRADES_CONFIRM_PAYMENT"]),
      user.hasPermission(PermissionEnum[isSale ? "CUSTOMER_SALES_SET_DELIVERED" : "TRADES_CONFIRM_DELIVERY"]),
      user.hasPermission(PermissionEnum[isSale ? "CUSTOMER_SALES_CANCEL" : "TRADES_CANCEL"]),
    ],
    [isSale, user],
  );
  const { currentStatus: current, isDelivered = false, isPaid = false, updatableStatusOrder = [] } = row || {};
  const deliveryFirst = updatableStatusOrder?.[0] === "pending_delivery";

  const firm: StatusStep = {
    value: 2,
    order: TransactionStatusUpdateOrder.firm,
    status: TransactionStatus.FIRM,
    state:
      TransactionStatusUpdateOrder[current] >= TransactionStatusUpdateOrder.firm
        ? "completed"
        : !firmable ||
            !calculator(TransactionStatusUpdateOrder[current], { treatNothingAsNaN: true }).isLessThanOrEqualTo(
              TransactionStatusUpdateOrder.firm,
            )
          ? "inactive"
          : "active",
    permitted: firmable,
  };

  const bind: StatusStep = {
    value: 23,
    order: TransactionStatusUpdateOrder.bind,
    status:
      TransactionStatusUpdateOrder[current] >= TransactionStatusUpdateOrder.bind
        ? TransactionStatus.BINDING
        : TransactionUpdateStatus.BIND,
    state:
      TransactionStatusUpdateOrder[current] >= TransactionStatusUpdateOrder.bind
        ? "completed"
        : !bindable ||
            !calculator(TransactionStatusUpdateOrder[current]).isBetween(
              TransactionStatusUpdateOrder.firm,
              TransactionStatusUpdateOrder.binding,
            )
          ? "inactive"
          : "active",
    permitted: bindable,
  };

  const execute: StatusStep = {
    value: 44,
    order: TransactionStatusUpdateOrder.execute,
    status:
      TransactionStatusUpdateOrder[current] >= TransactionStatusUpdateOrder.execute
        ? TransactionStatus.EXECUTED
        : TransactionUpdateStatus.EXECUTE,
    state:
      TransactionStatusUpdateOrder[current] >= TransactionStatusUpdateOrder.execute
        ? "completed"
        : !executable ||
            !calculator(TransactionStatusUpdateOrder[current]).isBetween(
              TransactionStatusUpdateOrder.binding,
              TransactionStatusUpdateOrder.executed,
            )
          ? "inactive"
          : "active",
    permitted: executable,
  };

  const pay: StatusStep = {
    value: deliveryFirst ? 77 : 61,
    order: TransactionStatusUpdateOrder.pay,
    status: isPaid ? "paid" : TransactionUpdateStatus.PAY,
    state: isPaid
      ? "completed"
      : !payable ||
          !calculator(TransactionStatusUpdateOrder[current]).isBetween(
            TransactionStatusUpdateOrder.execute,
            TransactionStatusUpdateOrder.pay,
          )
        ? "inactive"
        : "active",
    permitted: payable,
  };

  const deliver: StatusStep = {
    value: deliveryFirst ? 61 : 77,
    order: TransactionStatusUpdateOrder.deliver,
    status: isDelivered ? "delivered" : TransactionUpdateStatus.DELIVER,
    state: isDelivered
      ? "completed"
      : !deliverable ||
          !calculator(TransactionStatusUpdateOrder[current]).isBetween(
            TransactionStatusUpdateOrder.execute,
            TransactionStatusUpdateOrder.deliver,
          )
        ? "inactive"
        : "active",
    permitted: deliverable,
  };

  const settle: StatusStep = {
    value: 98,
    order: TransactionStatusUpdateOrder.settle,
    status:
      TransactionStatusUpdateOrder[current] === TransactionStatusUpdateOrder.settle
        ? TransactionStatus.SETTLED
        : TransactionUpdateStatus.SETTLE,
    state: TransactionStatusUpdateOrder[current] === TransactionStatusUpdateOrder.settle ? "completed" : "inactive",
    permitted: true,
  };

  const steps: InteractiveProgressStep[] = [
    firm,
    bind,
    execute,
    ...(deliveryFirst ? [deliver, pay] : [pay, deliver]),
    settle,
  ].map(({ value, status, state, permitted }) => ({
    value,
    state,
    node: (
      <Tooltip title={!permitted && state !== "completed" && "Insufficient permissions"}>
        <Box component="span" bgcolor="white">
          <Status row={row} status={status} state={state} />
        </Box>
      </Tooltip>
    ),
  }));

  return (
    <Stack ref={ref} component={Box} direction="row" alignItems="center">
      <Maybe condition={row?.currentStatus !== TransactionStatus.CANCELED}>
        <InteractiveProgress
          length={width - 115}
          separator={0}
          baseStepSize={30}
          steps={steps}
          connectorColor={adjustShade("gray", 100)}
          baseColors={{
            completed: COLORS.rubiconGreen,
            active: COLORS.rubiconGreen,
          }}
        />
        <Divider sx={{ height: 24, m: 2 }} orientation="vertical" />
        <Tooltip
          title={!cancelable && (!isDelivered || current !== TransactionStatus.SETTLED) && "Insufficient permissions"}
        >
          <Box>
            <Status
              row={row}
              status={TransactionUpdateStatus.CANCEL}
              state={!cancelable || isDelivered || current === TransactionStatus.SETTLED ? "inactive" : "active"}
            />
          </Box>
        </Tooltip>
      </Maybe>
      <Maybe condition={row?.currentStatus === TransactionStatus.CANCELED}>
        <Chip
          variant="outlined"
          icon={<CancelRounded color="error" sx={{ fontSize: 24 }} />}
          label={
            <Typography color={COLORS.rubiconGreen} fontSize={16}>
              Canceled
            </Typography>
          }
          sx={{ border: "unset" }}
        />
      </Maybe>
    </Stack>
  );
};

export default StatusColumnField;
