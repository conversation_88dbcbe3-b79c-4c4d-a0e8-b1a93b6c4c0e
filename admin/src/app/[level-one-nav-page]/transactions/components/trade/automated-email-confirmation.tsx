import { <PERSON>, Divider, <PERSON>rid, <PERSON><PERSON>, Sx<PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { classcat, Maybe } from "@rubiconcarbon/frontend-shared";
import { ChangeEvent, Dispatch, Fragment, PropsWithChildren, SetStateAction, useMemo, useState } from "react";
import usePerformantEffect from "@/hooks/use-performant-effect";
import Awaited from "@components/ui/await/components/awaited";
import { TradeConfirmationResponse } from "@rubiconcarbon/shared-types";

import classes from "../../styles/automated-email-confirmation.module.scss";

type SectionProps = {
  header?: string;
  divide?: boolean;
  className?: string;
  sx?: SxProps;
};

type AutomatedEmailConfirmationProps = {
  loading: boolean;
  confirmation: TradeConfirmationResponse;
  ignoreReceipients?: boolean;
  sx?: SxProps;
  updateDeliveryTerms?: Dispatch<SetStateAction<string>>;
};

const Section = ({
  header,
  children,
  divide = true,
  className = "",
  sx = {},
}: PropsWithChildren<SectionProps>): JSX.Element => (
  <>
    <Stack gap={1} className={classcat([classes.Section, { [className]: !!className }, "section-override"])} sx={sx}>
      <Maybe condition={!!header}>
        <Typography classes className={classcat([classes.SectionHeader, "header-override"])}>
          {header}
        </Typography>
      </Maybe>
      {children}
    </Stack>
    <Maybe condition={divide}>
      <Divider />
    </Maybe>
  </>
);

const AutomatedEmailConfirmation = ({
  loading,
  confirmation,
  ignoreReceipients = false,
  sx = {},
  updateDeliveryTerms,
}: AutomatedEmailConfirmationProps): JSX.Element => {
  const isMultipleEmails = useMemo(
    () => confirmation?.recipientEmails?.length > 1,
    [confirmation?.recipientEmails?.length],
  );

  const [deliveryTerms, setDeliverTerms] = useState<string>();

  usePerformantEffect(() => {
    if (updateDeliveryTerms) updateDeliveryTerms(deliveryTerms);
  }, [deliveryTerms]);

  return (
    <Maybe
      condition={!loading}
      fallback={<Awaited variant="rounded" repeat={4} sx={{ gap: 2 }} itemSx={{ height: 150 }} />}
    >
      <Stack className={classes.Details} gap={2} sx={sx}>
        <Maybe condition={!ignoreReceipients}>
          <Section header={`Receipient${isMultipleEmails ? "(s)" : ""}`}>
            <Typography className={classes.KeyValueText} variant="body2">
              <span className={classes.Key}>To: </span> <span>{confirmation?.recipientEmails?.join(", ")}</span>
            </Typography>
          </Section>
        </Maybe>

        <Section header="Trade Confirmation">
          <Typography className={classes.KeyValueText} variant="body2">
            <span className={classes.Key}>Date: </span> <span>{confirmation?.formattedDate}</span>
          </Typography>
          <Typography className={classes.KeyValueText} variant="body2">
            <span className={classes.Key}>Rubicon Transaction ID: </span> <span>{confirmation?.uiKey}</span>
          </Typography>
        </Section>

        <Section header="Commercial Terms">
          <Typography className={classes.KeyValueText} variant="body2">
            <span className={classes.Key}>Seller: </span> <span>{confirmation?.seller}</span>
          </Typography>
          <Typography className={classes.KeyValueText} variant="body2">
            <span className={classes.Key}>Buyer: </span> <span>{confirmation?.buyer}</span>
          </Typography>
        </Section>

        <Section divide={false}>
          <Grid className={classes.Table} container gap={0.5}>
            <Grid className={classes.Header} item container gap={1}>
              <Grid className={classes.Label} item xs={4}>
                Product
              </Grid>
              <Grid className={classes.Label} item xs={3}>
                Quantity in Tonnes
              </Grid>
              <Grid className={classes.Label} item container xs={2.5}>
                Price per Tonne
              </Grid>
              <Grid className={classes.Label} item xs={2}>
                Total
              </Grid>
            </Grid>
            <Grid className={classes.Body} item container gap={1}>
              {confirmation?.products?.map((product) => (
                <Fragment key={product?.productId}>
                  <Grid className={classes.Value} item xs={4}>
                    <Stack gap={1}>
                      <Box component="span">{product?.projectName}</Box>
                      <Box component="span" color="gray">
                        {product?.registryProjectId}
                        {product?.vintageName ? ` - ${product?.vintageName}` : ""}
                      </Box>
                    </Stack>
                  </Grid>
                  <Grid className={classes.Value} item xs={3}>
                    {product?.quantity}
                  </Grid>
                  <Grid className={classes.Value} item xs={2.5}>
                    {product?.pricePerTonne}
                  </Grid>
                  <Grid className={classes.Value} item xs={2}>
                    {product?.totalPrice}
                  </Grid>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                </Fragment>
              ))}
            </Grid>
            <Grid className={classes.Footer} item container>
              <Grid item xs={2.5} />
              <Grid className={classes.Value} item container xs={3} alignItems="center" gap={0.5}>
                <Typography className={classes.KeyValueText} variant="body2">
                  <span className={classes.Key}>Total Quantity: </span> <span>{confirmation?.totalQuantity}</span>
                </Typography>
              </Grid>
              <Grid item xs={2.85} />
              <Grid className={classes.Value} item container xs={3.5} alignItems="center" gap={0.5}>
                <Typography className={classes.KeyValueText} variant="body2">
                  <span className={classes.Key}>Total Amount: </span> <span>{confirmation?.totalPrice}</span>
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Section>

        <Maybe condition={!!confirmation?.paymentTerms}>
          <Section
            header="Payment Terms:"
            divide={false}
            sx={{
              ".header-override": {
                fontSize: 14,
                textTransform: "none !important",
              },
            }}
          >
            <Typography className={classes.KeyValueText} variant="body2">
              {confirmation?.paymentTerms}
            </Typography>
          </Section>
        </Maybe>

        <Maybe condition={!!updateDeliveryTerms || !!confirmation?.deliveryTerms}>
          <Section
            header="Delivery Terms:"
            divide={false}
            sx={{
              ".header-override": {
                fontSize: 14,
                textTransform: "none !important",
              },
            }}
          >
            <Maybe condition={!!updateDeliveryTerms}>
              <TextField
                multiline
                minRows={3}
                InputProps={{
                  sx: {
                    whiteSpace: "pre-wrap",
                  },
                }}
                onChange={(event: ChangeEvent<HTMLTextAreaElement>) => setDeliverTerms(event?.target?.value)}
              />
            </Maybe>
            <Maybe condition={!updateDeliveryTerms && !!confirmation?.deliveryTerms}>
              <Typography
                className={classes.KeyValueText}
                variant="body2"
                sx={{
                  whiteSpace: "pre-wrap",
                  wordWrap: "break-word",
                  border: "solid lightgray thin",
                  borderRadius: 1,
                  padding: 1,
                  minHeight: 100,
                }}
              >
                {confirmation?.deliveryTerms}
              </Typography>
            </Maybe>
          </Section>
        </Maybe>
      </Stack>
    </Maybe>
  );
};

export default AutomatedEmailConfirmation;
