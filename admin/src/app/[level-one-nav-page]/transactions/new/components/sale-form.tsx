import { useLogger } from "@providers/logging";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import {
  Autocomplete,
  Button,
  Checkbox,
  CircularProgress,
  Container,
  FormControl,
  FormControlLabel,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import {
  AdminPurchaseRequest,
  AssetType,
  AssetWithNestedAllocationQuery,
  AssetWithNestedAllocationQueryResponse,
  AdminBookQueryResponse,
  AdminBookResponse,
  uuid,
  GroupingParentQueryResponse,
  ModelPortfolioResponse,
  PermissionEnum,
  TrimmedBookResponse,
  ModelPortfolioStatus,
  CreateModelPortfolioRequest,
} from "@rubiconcarbon/shared-types";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { DatePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import { PurchaseFlowTypeOptions } from "@constants/purchase-flows";
import { KeyboardArrowRightRounded } from "@mui/icons-material";
import useNavigation from "@/hooks/use-navigation";
import ProductDetails from "../../components/sale/product-details";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { useBoolean, useMount, usePrevious, useUnmount } from "react-use";
import { JSX, useContext, useEffect, useMemo, useState } from "react";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import Confirmation from "../../components/sale/confirmation";
import { ProductTypeOptions } from "@constants/products";
import { px, toBoolean, toDecimal, toNumber, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { CALCULATOR_BLACK_LIST } from "../../constants/calculator";
import { TransactionModel } from "@models/transaction";
import { getNewPurchaseModel } from "@utils/helpers/transaction/get-new-transaction-models";
import { useStoreProvider } from "@providers/store-provider";
import { combineByParentBookType } from "@utils/helpers/portfolio/grouping-parent-book-transformations";
import { AuthContext } from "@providers/auth-provider";
import { isEmpty } from "lodash";
import { buildPrefilledProducts } from "../utilities/prefill-sale";

import classes from "../styles/new-purchase-form.module.scss";

const PurchaseModelResolver = classValidatorResolver(TransactionModel);

const NewPurchaseForm = ({
  booksByParentResponse,
  customerPortfoliosResponse,
  mockBasketResponse,
}: {
  booksByParentResponse: GroupingParentQueryResponse;
  customerPortfoliosResponse?: AdminBookQueryResponse;
  mockBasketResponse?: ModelPortfolioResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { user: loginUser } = useContext(AuthContext);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { updateEphemeralState } = useStoreProvider();

  const isTraderPreFill: boolean = useMemo(
    () => loginUser.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_ADVANCED_VIEW) && !!mockBasketResponse?.id,
    [loginUser, mockBasketResponse?.id],
  );

  const [canConfirm, setCanConfirm] = useBoolean(false);

  const model = getNewPurchaseModel(isTraderPreFill);

  const {
    control,
    formState: { errors },
    trigger,
    setValue,
    watch,
    resetField,
    handleSubmit,
  } = useForm<TransactionModel>({
    resolver: PurchaseModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: lineItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "assets",
  });

  useMount(() => {
    setValue("assets.0.isASale" as any, true as any);
    setValue("assets.0.isAPurchase" as any, true as any);
  });

  useUnmount(() => {
    updateEphemeralState("transactions.viewing", "sales");
  });

  const data = watch();
  const customerPortfolio = data?.sale?.customerPortfolio;

  const previousSelectedCustomerPortfolioId = usePrevious(customerPortfolio?.id);

  const {
    data: assets,
    trigger: getAssets,
    isMutating: loadingAssets,
  } = useTriggerRequest<AssetWithNestedAllocationQueryResponse, object, { id: uuid }, AssetWithNestedAllocationQuery>({
    url: "admin/books/{id}/assets/receivables",
    pathParams: {
      id: customerPortfolio?.id,
    },
    queryParams: {
      assetTypes: [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
      limit: SERVER_PAGINATION_LIMIT,
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch assets for ${customerPortfolio?.name}`);
        logger.error(`Unable to load assets for ${customerPortfolio?.name}: ${error?.message}`, {});
      },
    },
    condition: !!customerPortfolio?.id,
  });

  const { trigger: updateSaleStatus } = useTriggerRequest<object, CreateModelPortfolioRequest>({
    url: `admin/model-portfolios/${mockBasketResponse?.id}`,
    method: "patch",
    requestBody: {
      status: ModelPortfolioStatus.ORDER_CREATED,
    },
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Sale status was updated successfully.");
      },
      onError: (error: any): void => {
        enqueueError("Unable to update sale status.");
        logger.error(`Unable to update sale status.: ${error?.message}`, {});
      },
    },
  });

  const { trigger: commitPurchase, isMutating: commitingPurchase } = useTriggerRequest<object, AdminPurchaseRequest>({
    url: "admin/purchases",
    method: "post",
    requestBody: {
      customerPortfolioId: data?.sale?.customerPortfolio?.id,
      assetType: data?.sale?.productType,
      flowType: data?.sale?.flowType,
      needsRiskAdjustment: toBoolean(data?.sale?.needsRiskAdjustment),
      assets: data?.assets?.map((item) => ({
        assetId: item?.supplementaryAssetDetails?.id,
        sourceId: item?.source?.id,
        amount: toNumber(item?.amount, { parserBlacklist: CALCULATOR_BLACK_LIST }),
        rawPrice: toDecimal(item?.rawPrice, { parserBlacklist: CALCULATOR_BLACK_LIST }),
      })),
      ...px(
        {
          paymentDueDate: !!data?.sale?.paymentDueDate && new Date(data?.sale?.paymentDueDate),
          memo: !!data?.memo?.trim() && data?.memo,
        },
        [false],
      ),
    },
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Sale created successfully.");
        if (mockBasketResponse?.id) {
          updateSaleStatus();
        }
        popFromPath(1);
      },
      onError: (error: any): void => {
        enqueueError("Unable to create purchase.");
        logger.error(`Unable to create purchase: ${error?.message}`, {});
      },
    },
  });

  const parentBookRelatedInfoByType = combineByParentBookType(booksByParentResponse?.data, ["parent_book"]);

  const books = useMemo(
    () => Object.values(parentBookRelatedInfoByType || {})?.map(({ parentBook }) => parentBook),
    [parentBookRelatedInfoByType],
  );

  const allocations = useMemo(() => assets?.data?.flatMap(({ allocations }) => allocations), [assets?.data]);

  useEffect(() => {
    const customerPortfolioId = customerPortfolio?.id;
    if (!!customerPortfolioId && previousSelectedCustomerPortfolioId !== customerPortfolioId && !loadingAssets)
      setTimeout(async () => await getAssets());
  }, [customerPortfolio?.id, getAssets, loadingAssets, previousSelectedCustomerPortfolioId]);

  const hasPortfolioProducts = useMemo(
    () => assets?.data?.some(({ asset }) => asset?.type === AssetType.RCT),
    [assets?.data],
  );

  const hasVintageProducts = useMemo(
    () => assets?.data?.some(({ asset }) => asset?.type === AssetType.REGISTRY_VINTAGE),
    [assets?.data],
  );

  const customerPortfolioOptions = useAutoCompleteOptions<AdminBookResponse, AdminBookResponse>({
    data: customerPortfoliosResponse?.data || [],
    keys: ["id", "name", "organization"],
    label: (entry: AdminBookResponse) => entry?.organization?.name,
    value: (entry: AdminBookResponse) => entry,
    preTransform: (data: AdminBookResponse[]) => data?.filter(({ isEnabled }) => isEnabled),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const productTypeOptions = useMemo(
    () =>
      ProductTypeOptions.filter((option) =>
        option.value === AssetType.RCT
          ? hasPortfolioProducts
          : option.value === AssetType.REGISTRY_VINTAGE
            ? hasVintageProducts
            : false,
      ),
    [hasPortfolioProducts, hasVintageProducts],
  );

  const onSubmit = (): void => setCanConfirm(true);

  useEffect(() => {
    if (mockBasketResponse) {
      setValue("sale.needsRiskAdjustment", mockBasketResponse?.includeRiskAdjustment as any);
    }
  }, [mockBasketResponse, setValue]);

  useEffect(() => {
    if (customerPortfoliosResponse?.data) {
      //pre-fill organization only if the portfolio was shared with a customer
      const selectedOrganization = mockBasketResponse?.organization?.id
        ? (customerPortfolioOptions.find(
            (entry) => entry?.value?.organization?.id === mockBasketResponse?.organization?.id,
          )?.value ?? null)
        : null;
      setValue("sale.customerPortfolio", selectedOrganization as unknown as TrimmedBookResponse);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerPortfoliosResponse?.data, setValue, mockBasketResponse?.showCustomer]);

  useEffect(() => {
    if (isTraderPreFill && productTypeOptions?.length > 0) {
      const selectedProductType = productTypeOptions.find((p) => p.value === AssetType.REGISTRY_VINTAGE)?.value ?? null;
      if (selectedProductType) {
        setValue("sale.productType", selectedProductType as any);
      }
    }
  }, [isTraderPreFill, productTypeOptions, setValue]);

  useEffect(() => {
    if (
      !!assets &&
      !!booksByParentResponse &&
      !isEmpty(mockBasketResponse?.modelPortfolioComponents) &&
      isTraderPreFill &&
      !isInitialized &&
      !isEmpty(books)
    ) {
      setValue("assets", buildPrefilledProducts(mockBasketResponse, books));
      setIsInitialized(true);
    }
  }, [
    mockBasketResponse,
    isTraderPreFill,
    setValue,
    append,
    data?.assets?.length,
    assets,
    booksByParentResponse,
    isInitialized,
    loadingAssets,
    books,
  ]);
  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="sale.customerPortfolio"
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = mockBasketResponse?.showCustomer
              ? (customerPortfolioOptions.find(
                  (entry) => entry?.value?.organization?.id === mockBasketResponse?.organization?.id,
                ) ?? null)
              : (customerPortfolioOptions.find((entry) => entry?.value?.id === value?.id) ?? null);
            return (
              <Autocomplete
                options={customerPortfolioOptions}
                value={selectedOption}
                disabled={mockBasketResponse?.showCustomer}
                onChange={(_, selection) => onChange(selection?.value)}
                id="organization"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.value?.id}
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.label}
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ref,
                      ...InputProps,
                    }}
                    label="Organization"
                    {...otherProps}
                    error={!!errors?.sale?.customerPortfolio}
                    helperText={errors?.sale?.customerPortfolio?.message}
                    fullWidth
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <Controller
          name="sale.flowType"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Settlement Type"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.sale?.flowType}
              helperText={errors?.sale?.flowType?.message}
              {...otherProps}
              fullWidth
            >
              {PurchaseFlowTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <Controller
          name="sale.productType"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select={!loadingAssets}
              label="Product Type"
              value={value ?? ""}
              InputProps={{
                ref,
                endAdornment: loadingAssets && (
                  <CircularProgress variant="indeterminate" size={20} sx={{ color: "gray" }} />
                ),
              }}
              error={!!errors?.sale?.productType}
              helperText={
                !customerPortfolio
                  ? "Please select Organization"
                  : !loadingAssets && !productTypeOptions?.length
                    ? "No Products Available to Sell"
                    : errors?.sale?.productType?.message
              }
              {...otherProps}
              fullWidth
              disabled={loadingAssets || !customerPortfolio || !productTypeOptions?.length || isTraderPreFill}
            >
              {productTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
        <Controller
          name="sale.needsRiskAdjustment"
          control={control}
          render={({ field: { value, onChange, ...otherProps }, formState: { errors } }): JSX.Element => (
            <FormControl error={!!errors?.sale?.needsRiskAdjustment}>
              <FormControlLabel
                label={<Typography color="#094436">Add risk adjustment</Typography>}
                control={<Checkbox checked={value ?? false} onChange={onChange} {...otherProps} />}
              />
            </FormControl>
          )}
        />
        <ProductDetails
          allocations={allocations}
          books={books}
          loadingAssets={loadingAssets}
          control={control}
          errors={errors}
          lineItems={lineItems}
          trigger={trigger}
          setValue={setValue}
          resetField={resetField}
          watch={watch}
          append={append}
          remove={remove}
        />
        <Controller
          control={control}
          name="sale.paymentDueDate"
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            return (
              <DatePicker
                format="MM/DD/YYYY"
                value={value ? dayjs(value as any) : null}
                disablePast
                onChange={(newValue) => onChange(newValue ? newValue.toDate() : null)}
                slotProps={{
                  textField: {
                    label: "Payment Due Date",
                    fullWidth: true,
                    InputProps: {
                      inputRef: ref,
                    },
                    error: !!errors?.sale?.paymentDueDate,
                    FormHelperTextProps: {
                      component: "div",
                    },
                    helperText: errors?.sale?.paymentDueDate?.message,
                    ...otherProps,
                  },
                  popper: {
                    placement: "bottom-end",
                  },
                }}
              />
            );
          }}
        />
        <Controller
          name="memo"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Memo (Optional)"
              value={value ?? ""}
              InputProps={{ ref, sx: { whiteSpace: "pre-wrap" } }}
              minRows={4}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
            disabled={loadingAssets}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        title="Review and Confirm Sale"
        open={canConfirm}
        onClose={() => setCanConfirm(false)}
        positiveAction={{
          buttonText: "CONFIRM SALE",
          loading: commitingPurchase,
          onClick: async () => await commitPurchase(),
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setCanConfirm(false),
        }}
        classes={{
          root: classes.Dialog,
          title: classes.Title,
          content: classes.Content,
          actions: classes.Actions,
        }}
      >
        <Confirmation data={data} />
      </GenericDialog>
    </Container>
  );
};

export default NewPurchaseForm;
