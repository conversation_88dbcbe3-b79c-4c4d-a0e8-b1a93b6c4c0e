"use client";

import { Maybe } from "@rubiconcarbon/frontend-shared";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useEffect } from "react";
import NewPurchaseForm from "./sale-form";
import TradeForm from "../../components/trade/form";
import {
  AdminBookQueryResponse,
  CounterpartyQueryResponse,
  GroupingParentQueryResponse,
  ModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";

export default function NewTransaction({
  type,
  counterpartiesResponse,
  booksByParentResponse,
  customerPortfoliosResponse,
  mockBasketResponse,
}: {
  type: string;
  counterpartiesResponse?: CounterpartyQueryResponse;
  booksByParentResponse: GroupingParentQueryResponse;
  customerPortfoliosResponse?: AdminBookQueryResponse;
  mockBasketResponse?: ModelPortfolioResponse;
}): JSX.Element {
  const { updateBreadcrumbName } = useBreadcrumbs();

  useEffect(() => {
    updateBreadcrumbName?.("New", type === "sale" ? "New Customer Sale" : "New Trade");
  }, [type, updateBreadcrumbName]);

  return (
    <>
      <Maybe condition={type === "sale"}>
        <NewPurchaseForm
          booksByParentResponse={booksByParentResponse}
          customerPortfoliosResponse={customerPortfoliosResponse}
          mockBasketResponse={mockBasketResponse}
        />
      </Maybe>
      <Maybe condition={type === "trade"}>
        <TradeForm counterpartiesResponse={counterpartiesResponse} booksByParentResponse={booksByParentResponse} />
      </Maybe>
    </>
  );
}
