import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookQueryResponse,
  BookRelations,
  BookAction,
  BookType,
  CounterpartyQueryResponse,
  GroupingParentQueryResponse,
  PermissionEnum,
  ModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";
import NewTransaction from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * New Transaction Page
 *
 * This is a server component that renders the New Transaction page
 */
export default async function NewTransactionPage({
  searchParams,
}: {
  searchParams: Promise<{ type: string; id?: string }>;
}): Promise<JSX.Element> {
  const { type, id } = await searchParams;
  const isTrade = type === "trade";
  const isSale = type === "sale";

  const counterpartiesResponse = isTrade
    ? await withErrorHandling(async () =>
        baseApiRequest<CounterpartyQueryResponse>(
          `admin/counterparties?${generateQueryParams({
            isEnabled: true,
            includeTotalCount: false,
            limit: SERVER_PAGINATION_LIMIT,
          })}`,
        ),
      )
    : undefined;

  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<GroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  const customerPortfoliosResponse = isSale
    ? await withErrorHandling(async () =>
        baseApiRequest<AdminBookQueryResponse>(
          `admin/books?${generateQueryParams({
            offset: 0,
            limit: SERVER_PAGINATION_LIMIT,
            isEnabled: true,
            includeTotalCount: false,
            includeRelations: [BookRelations.ORGANIZATION],
            types: [BookType.PORTFOLIO_CUSTOMER],
            allowedAction: BookAction.PURCHASE,
          })}`,
        ),
      )
    : undefined;

  const mockBasketResponse =
    isSale && id
      ? await withErrorHandling(async () => baseApiRequest<ModelPortfolioResponse>(`admin/books/${id}`))
      : undefined;

  // Check if the result is a server error
  if (isValidElement(counterpartiesResponse)) return counterpartiesResponse;
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;
  if (isValidElement(customerPortfoliosResponse)) return customerPortfoliosResponse;
  if (isValidElement(mockBasketResponse)) return mockBasketResponse;

  return (
    <AuthorizeServer
      permissions={[type === "sale" ? PermissionEnum.CUSTOMER_SALES_CREATE : PermissionEnum.TRADES_CONFIRM_INDICATIVE]}
    >
      <NewTransaction
        type={type}
        counterpartiesResponse={counterpartiesResponse as CounterpartyQueryResponse}
        booksByParentResponse={booksByParentResponse as GroupingParentQueryResponse}
        customerPortfoliosResponse={customerPortfoliosResponse as AdminBookQueryResponse}
        mockBasketResponse={mockBasketResponse as ModelPortfolioResponse}
      />
    </AuthorizeServer>
  );
}
