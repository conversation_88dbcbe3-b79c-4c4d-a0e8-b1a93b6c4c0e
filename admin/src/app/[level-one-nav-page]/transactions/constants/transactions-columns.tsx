import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { Typography } from "@mui/material";
import { AssetType, TransactionType } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { Maybe, px } from "@rubiconcarbon/frontend-shared";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import Link from "next/link";
import COLORS from "@components/ui/theme/colors";
import ProductName from "@components/ui/product-name/product-name";
import { TransactionStatusToLabel } from "@constants/transaction-status";
import StatusChip from "@components/ui/status-chip/StatusChip";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import { AssetOrder, TrimmedTransactionModel } from "@models/transaction";

import statusClasses from "../styles/status-column-label.module.scss";

export const TRANSACTION_COLUMNS: GenericTableColumn<TrimmedTransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TrimmedTransactionModel>) => (
      <Link
        href={`/trading/transactions/${row?.id}?type=${row?.type === TransactionType.TRADE ? row?.subtype : row?.type}`}
        style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}
      >
        <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "typeF",
    label: "Type",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "counterpartyName",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TrimmedTransactionModel): JSX.Element => (
      // currently, only supports customer sales and trades
      <>
        <Maybe condition={row?.type === TransactionType.PURCHASE}>
          <CustomerPortfolio
            portfolio={{ name: row?.counterpartyName, organization: { id: row?.counterpartyId } } as any}
            style={{ fontSize: 14, fontWeight: 300 }}
          />
        </Maybe>
        <Maybe condition={row?.type !== TransactionType.PURCHASE}>
          <Typography fontSize={14} fontWeight={300}>
            {row?.counterpartyName}
          </Typography>
        </Maybe>
      </>
    ),
  },
  {
    field: "productF",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TrimmedTransactionModel): JSX.Element => {
      const assets = row?.assetFlows?.map((flow) => ({
        ...px({ rct: flow?.asset?.type === AssetType.RCT && { id: flow?.asset?.id, name: flow?.asset?.name } }, [
          undefined,
          null,
          false,
        ]),
        projectVintage: {
          name: flow?.asset?.label,
          project: {
            id: flow?.asset?.projectId,
            name: flow?.asset?.name,
            registryProjectId: flow?.asset?.registryProjectId,
          },
        },
      })) as AssetOrder[];

      return <ProductName assets={assets} addTags={{ rct: false, suspended: false }} style={{ fontSize: 14 }} />;
    },
  },
  {
    field: "totalQuantity",
    label: "Volume",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "totalPrice",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "updatedAt",
    label: "Updated Date",
    type: "date",
    width: 130,
    fixedWidth: true,
  },
  {
    field: "status",
    label: "Status",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => TransactionStatusToLabel[row?.status],
    renderDataCell: (row) => <StatusChip status={row?.status} />,
  },
];
