import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import ProductName from "@components/ui/product-name/product-name";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import StatusColumnLabel from "../components/status-column-label";
import StatusColumnField from "../components/status-column-field";
import { TransactionStatus, TransactionStatusToLabel } from "@constants/transaction-status";
import Link from "next/link";
import { Typography } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import GenericTableDocumentsButton from "@components/ui/generic-table/components/generic-row-document-button";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import { TransactionModel } from "@/models/transaction";

import statusClasses from "../styles/status-column-label.module.scss";

export const SALE_COLUMNS: GenericTableColumn<TransactionModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TransactionModel>) => (
      <Link
        href={`/trading/transactions/${row?.id}?type=${row?.type}`}
        style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}
      >
        <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "sale.customerPortfolio.name",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TransactionModel): JSX.Element => (
      <CustomerPortfolio portfolio={row?.sale?.customerPortfolio} style={{ fontSize: 14, fontWeight: 300 }} />
    ),
  },
  {
    field: "product" as any,
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row) => <ProductName assets={row?.assets} style={{ fontSize: 14 }} />,
  },
  {
    field: "documents" as any,
    label: "Documents",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    exportable: false,
    sortable: false,
    renderDataCell: (row): JSX.Element => <GenericTableDocumentsButton row={row} />,
  },
  {
    field: "sale.totalValue",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "status",
    label: "Status",
    displayLabel: <StatusColumnLabel />,
    type: "custom",
    exportable: true,
    width: 650,
    maxWidth: 750,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => {
      const status = row?.currentStatus;

      if (status === TransactionStatus.EXECUTED)
        return `${row?.isDelivered ? "Delivered" : row?.isPaid ? "Paid" : TransactionStatusToLabel[status]}`;
      return TransactionStatusToLabel[status];
    },
    renderDataCell: (row) => <StatusColumnField row={row} />,
  },
  {
    field: "updatedAt",
    label: "Updated Date",
    type: "date",
    width: 130,
    fixedWidth: true,
  },
];
