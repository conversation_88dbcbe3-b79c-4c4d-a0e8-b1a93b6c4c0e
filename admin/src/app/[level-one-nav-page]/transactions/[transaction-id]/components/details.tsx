import useBreadcrumbs from "@providers/breadcrumb-provider";
import useAuth from "@providers/auth-provider";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import { useMount, useToggle } from "react-use";
import { currencyFormat, Maybe, numberFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  PermissionEnum,
  PurchaseQuery,
  PurchaseRelations,
  PurchaseResponse,
  TradeConfirmationResponse,
  TradeQuery,
  TradeRelation,
  TradeResponse,
  uuid,
  VintagePricingResponse,
} from "@rubiconcarbon/shared-types";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { Box, Button, Divider, Grid, Paper, Stack, SxProps, Tooltip, Typography } from "@mui/material";
import { Fragment, JSX, PropsWithChildren, ReactNode, useMemo } from "react";
import { MISSING_DATA } from "@constants/constants";
import { capitalize } from "lodash";
import ProductName from "@components/ui/product-name/product-name";
import { BookCell, ProjectCell } from "../../constants/trades-columns";
import { PurchaseFlowTypeUILabel } from "@constants/purchase-flows";
import { TransactionStatus } from "@constants/transaction-status";
import useDocumentsApi from "@hooks/use-documents-api";
import { DocumentTypeUILabel } from "@constants/documents";
import AttachmentList from "@components/attachment-list/attachment-list";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { PriceSourceEnum } from "@constants/state";
import SaleDetailTable from "../../components/sale/sale-details-table";
import { AllTransactionType, AssetOrder } from "@models/transaction";
import { toPurchaseModel, toTradeModel } from "@utils/helpers/transaction/to-transaction-models";
import StatusChip from "@components/ui/status-chip/StatusChip";
import { CounterpartyRoleToLabel } from "@constants/counterparty-ui-role.enum";
import AutomatedEmailConfirmationModal from "../../components/trade/automated-email-confirmation-modal";

import classes from "../styles/details.module.scss";

type LabelValuePairProps = {
  label: ReactNode;
  orientation?: "horizontal" | "vertical";
  sx?: SxProps;
};

const LabelValuePair = ({
  label,
  orientation = "horizontal",
  children,
  sx,
}: PropsWithChildren<LabelValuePairProps>): JSX.Element => (
  <Stack
    direction={orientation === "horizontal" ? "row" : "column"}
    justifyContent={orientation === "horizontal" ? "flex-start" : "center"}
    alignItems="flex-start"
    gap={3}
    sx={sx}
  >
    <Box>{label}</Box>
    <Box> {children}</Box>
  </Stack>
);

const TransactionDetails = ({
  assetResponse: serverAssetResponse,
  tradeConfirmationResponse: serverTradeConfirmationResponse,
  type,
}: {
  assetResponse: PurchaseResponse | TradeResponse;
  tradeConfirmationResponse?: TradeConfirmationResponse;
  type: string;
}): JSX.Element => {
  const { user } = useAuth();
  const { logger } = useLogger();
  const { popFromPath, replacePathFromSegment } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { updateBreadcrumbName } = useBreadcrumbs();

  const [openEmailConfirmationPreview, toggleOpenEmailConfirmationPreview] = useToggle(false);

  const isSale = type === "purchase";

  useMount(() => {
    updateBreadcrumbName("Transaction Details", isSale ? "Customer Sale Detail" : "Trade Detail");
  });

  const { data: asset } = useTriggerRequest<
    PurchaseResponse | TradeResponse,
    object,
    { id: uuid },
    PurchaseQuery | TradeQuery
  >({
    url: `/admin/${isSale ? "purchases" : "trades"}/{id}`,
    pathParams: { id: serverAssetResponse?.id },
    queryParams: {
      includeRelations: isSale
        ? [PurchaseRelations.ASSETS, PurchaseRelations.CUSTOMER_PORTFOLIO]
        : [TradeRelation.BOOK, TradeRelation.PROJECT_VINTAGE],
    } as PurchaseQuery | TradeQuery,
    optimisticData: serverAssetResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch ${isSale ? "customer sale" : "trade"}.`);
        logger.error(
          `[${isSale ? "Customer Sale Details" : "Trade Details"}] Unable to fetch ${isSale ? "customer sale" : "trade"}: ${error?.message}.`,
          {},
        );
      },
    },
  });

  const { data: tradeEmailConfirmation } = useTriggerRequest<TradeConfirmationResponse>({
    url: "admin/trades/{id}/confirmation",
    pathParams: { id: serverAssetResponse?.id },
    optimisticData: serverTradeConfirmationResponse,
    swrOptions: {
      onError: (error: any) => {
        if (error?.status !== 404) {
          enqueueError("Unable to fetch trade email confirmation.");
          logger.error(`Unable to fetch trade email confirmation. Error: ${error?.message}`, {});
        }
      },
    },
  });

  const model = isSale ? toPurchaseModel(asset as PurchaseResponse) : toTradeModel(asset as TradeResponse);
  const organization = isSale ? (asset as PurchaseResponse)?.customerPortfolio : null;

  const { data: pricing, trigger: getVintagePricing } = useTriggerRequest<
    VintagePricingResponse,
    { vintage_ids: uuid[] },
    object,
    { source: PriceSourceEnum; limit: number }
  >({
    url: "reporting/vintage-pricing",
    method: "post",
    requestBody: {
      vintage_ids: [model?.assets?.at(0)?.projectVintage?.id],
    },
    queryParams: {
      source: PriceSourceEnum.RUBICON,
      limit: 1,
    },
  });

  const { documents, fetching, fetch } = useDocumentsApi({
    query: {
      relatedKey: model?.uiKey,
    },
  });

  // there should be only 1
  const counterparties = useMemo(
    () => (!isSale ? model?.trade?.counterparties?.filter(({ isPrimary }) => isPrimary) : []),
    [isSale, model?.trade?.counterparties],
  );

  const intermediairies = useMemo(
    () => (!isSale ? model?.trade?.counterparties?.filter(({ isPrimary }) => !isPrimary) : []),
    [isSale, model?.trade?.counterparties],
  );

  usePerformantEffect(() => {
    if (model?.assets?.at(0)?.projectVintage?.id) setTimeout(async () => await getVintagePricing());
  }, [model?.assets?.at(0)?.projectVintage?.id]);

  usePerformantEffect(() => {
    if (!fetching && !!model?.uiKey) {
      setTimeout(async () => await fetch());
    }
  }, [model?.uiKey]);

  const handleDeletionSuccess = async (): Promise<void> => {
    if (isSale) {
      enqueueSuccess(`Successfully deleted document for ${organization?.name}`);
      await fetch();
    }
  };

  const handleDeletionError = async (error: any): Promise<void> => {
    if (isSale) {
      enqueueError(`Failed to delete document for ${organization?.name}`);
      logger.error(`Failed to delete document for ${organization?.name}: ${error?.message}`, {});
    }
  };

  const handleEmailConfirmationPreviewToggle = (): void => toggleOpenEmailConfirmationPreview();

  return (
    <Stack gap={2}>
      <Stack component={Paper} padding={2} gap={2}>
        <Grid container gap={0.5}>
          <Grid item xs={12} md={3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Status:{" "}
                </Typography>
              }
            >
              <Maybe condition={!!model?.status} fallback={MISSING_DATA}>
                <StatusChip status={model?.status} />
              </Maybe>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={5}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Product:{" "}
                </Typography>
              }
            >
              <Maybe condition={isSale}>
                <ProductName
                  assets={(asset as PurchaseResponse)?.assets as unknown as AssetOrder[]}
                  style={{ fontWeight: 500, fontSize: "0.875rem", color: "#094436" }}
                />
              </Maybe>
              <Maybe condition={!isSale}>
                <ProjectCell row={model} style={{ fontSize: "0.875rem" }} />
              </Maybe>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Date/Time Requested:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {model?.dateStarted ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>
        </Grid>

        <Grid container gap={0.5}>
          <Grid item xs={12} md={3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Transaction Key:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {model?.uiKey}
              </Typography>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={5}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  {isSale ? "Amount of Credits" : "Trade Type"}:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {capitalize(
                  isSale
                    ? numberFormat(model?.amount, { fallback: MISSING_DATA })
                    : (model?.type?.toString() ?? MISSING_DATA),
                )}
              </Typography>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Date/Time Completed:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {model?.dateFinished ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>
        </Grid>

        <Grid container gap={0.5}>
          <Maybe condition={isSale}>
            <Grid item xs={12} md={3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Counterparty:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {model?.sale?.customerPortfolio?.name || MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>
          </Maybe>

          <Maybe condition={isSale}>
            <Grid item xs={12} md={5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Total Value:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {currencyFormat(model?.sale?.totalValue, { fallback: MISSING_DATA })}
                </Typography>
              </LabelValuePair>
            </Grid>

            <Grid item xs={12} md={3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Settlement Type:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {PurchaseFlowTypeUILabel[model?.sale?.flowType] ?? MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>
          </Maybe>
        </Grid>

        <Maybe condition={!isSale}>
          <Grid container gap={0.5}>
            <Grid item xs={12} md={3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Book:{" "}
                  </Typography>
                }
              >
                <BookCell row={model} />
              </LabelValuePair>
            </Grid>
          </Grid>
        </Maybe>

        <Grid container gap={0.5}>
          <Grid item xs={12} md={3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Memo:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {model?.memo || MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>

          <Maybe condition={isSale}>
            <Grid item xs={12} md={5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Risk Adjustment Included:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {typeof model?.sale?.needsRiskAdjustment === "boolean"
                    ? model?.sale?.needsRiskAdjustment === true
                      ? "Yes"
                      : "No"
                    : MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>

            <Grid item xs={12} md={3}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Payment Due Date:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary">
                  {model?.sale?.paymentDueDate ?? MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>
          </Maybe>
        </Grid>

        <Grid container gap={0.5}>
          <Grid item xs={12}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={100} fontWeight="bold">
                  Attachments:{" "}
                </Typography>
              }
              sx={{
                alignItems: "baseline",
              }}
            >
              <Maybe condition={documents.length > 0}>
                <AttachmentList
                  attachments={documents?.map((doc) => ({ ...doc, canDelete: isSale }))}
                  deleteConfirmation={{
                    title: "Confirm document deletion",
                    content: ({ document }) => (
                      <span>
                        Are you sure you want to delete <strong>{DocumentTypeUILabel[document.type]}</strong> for{" "}
                        <strong>{organization?.name}</strong>?
                      </span>
                    ),
                  }}
                  style={{ fontSize: "0.875rem" }}
                  onRemoveSuccess={handleDeletionSuccess}
                  onRemoveError={handleDeletionError}
                />
              </Maybe>
              <Maybe condition={documents.length === 0}>{MISSING_DATA}</Maybe>
            </LabelValuePair>
          </Grid>
        </Grid>

        <Stack direction="row" gap={1} paddingTop={2}>
          <Maybe condition={!isSale}>
            <Button
              variant="contained"
              sx={{ width: 80, height: 30 }}
              disabled={
                !user?.hasSomePermissions([PermissionEnum.TRADES_UPDATE, PermissionEnum.TRADES_UPDATE_AMOUNTS]) ||
                [TransactionStatus.SETTLED, TransactionStatus.CANCELED].includes(asset?.status as any)
              }
              onClick={() => replacePathFromSegment(1, `/${asset?.id}/edit?type=trade`)}
            >
              Edit
            </Button>
          </Maybe>
          <Button variant="contained" sx={{ width: 80, height: 30 }} onClick={() => popFromPath(1)}>
            Back
          </Button>
        </Stack>
      </Stack>

      <Maybe condition={!isSale}>
        <Stack component={Paper} gap={1}>
          <Typography variant="body2" fontWeight="bold" padding={1} bgcolor="lightgray">
            Trade Counterparty
          </Typography>
          <Grid className={classes.Table} container gap={0.5}>
            <Grid className={classes.Header} item container gap={1}>
              <Grid className={classes.Label} item xs={8}>
                Counterparty
              </Grid>
              <Grid className={classes.Label} item xs={3.5}>
                Role
              </Grid>
            </Grid>
            <Maybe condition={!!counterparties?.length}>
              <Grid className={classes.Body} item container gap={1} rowGap={1}>
                {counterparties?.map((party, index) => (
                  <Fragment key={`${party?.counterparty?.id}-${index}`}>
                    <Grid className={classes.Value} item xs={8}>
                      {party?.counterparty?.name}
                    </Grid>
                    <Grid className={classes.Value} item xs={3.5}>
                      {CounterpartyRoleToLabel[party?.role]}
                    </Grid>
                    <Maybe condition={!!party?.comments}>
                      <Stack gap={0.2}>
                        <Typography variant="caption" fontWeight="bold">
                          Comments:
                        </Typography>
                        <Grid className={classes.Value} item xs={12}>
                          {party?.comments}
                        </Grid>
                      </Stack>
                    </Maybe>
                    <Maybe condition={index !== (counterparties?.length || 0) - 1}>
                      <Grid item xs={12}>
                        <Divider />
                      </Grid>
                    </Maybe>
                  </Fragment>
                ))}
              </Grid>
            </Maybe>
            <Maybe condition={!counterparties?.length}>
              <Box width="100%" padding="5px 0">
                <Typography textAlign="center">No Trade Counterparty Added</Typography>
              </Box>
            </Maybe>
          </Grid>
        </Stack>

        <Maybe condition={!!intermediairies?.length}>
          <Stack component={Paper} gap={1}>
            <Typography variant="body2" fontWeight="bold" padding={1} bgcolor="lightgray">
              Trade Intermediaries
            </Typography>
            <Grid className={classes.Table} container gap={0.5}>
              <Grid className={classes.Header} item container gap={1}>
                <Grid className={classes.Label} item xs={8}>
                  Counterparty
                </Grid>
                <Grid className={classes.Label} item xs={3.5}>
                  Role
                </Grid>
              </Grid>
              <Grid className={classes.Body} item container gap={1} rowGap={1}>
                {intermediairies?.map((party, index) => (
                  <Fragment key={`${party?.counterparty?.id}-${index}`}>
                    <Grid className={classes.Value} item xs={8}>
                      {party?.counterparty?.name}
                    </Grid>
                    <Grid className={classes.Value} item xs={3.5}>
                      {CounterpartyRoleToLabel[party?.role]}
                    </Grid>
                    <Maybe condition={!!party?.comments}>
                      <Stack gap={0.2}>
                        <Typography variant="caption" fontWeight="bold">
                          Comments:
                        </Typography>
                        <Grid className={classes.Value} item xs={12}>
                          {party?.comments}
                        </Grid>
                      </Stack>
                    </Maybe>
                    <Maybe condition={index !== (intermediairies?.length || 0) - 1}>
                      <Grid item xs={12}>
                        <Divider />
                      </Grid>
                    </Maybe>
                  </Fragment>
                ))}
              </Grid>
            </Grid>
          </Stack>
        </Maybe>

        <Stack component={Paper} gap={1}>
          <Typography variant="body2" fontWeight="bold" padding={1} bgcolor="lightgray">
            Order Entry
          </Typography>
          <Grid container gap={1} padding={2}>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Price:{" "}
                  </Typography>
                }
              >
                <Stack direction="row" gap={0.5} alignItems="center">
                  <Typography variant="body2" color="primary" height={18}>
                    {currencyFormat(model?.assets?.at(0)?.unitPrice)}
                  </Typography>
                  <Tooltip title="Price (net of fees)" sx={{ height: 18, cursor: "pointer" }}>
                    <Box>
                      <MatIcon value="info" variant="round" size={18} color="action" />
                    </Box>
                  </Tooltip>
                </Stack>
              </LabelValuePair>
            </Grid>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Quantity:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {numberFormat(model?.assets?.at(0)?.amount)}
                </Typography>
              </LabelValuePair>
            </Grid>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    {model?.type === AllTransactionType.SELL ? "Gross Revenue" : "Sub Total"}:{" "}
                  </Typography>
                }
              >
                <Stack direction="row" gap={0.5} alignItems="center">
                  <Typography variant="body2" color="primary" height={18}>
                    {currencyFormat(model?.assets?.at(0)?.rawPrice)}
                  </Typography>
                  <Tooltip title="Price x Quantity" sx={{ height: 18, cursor: "pointer" }}>
                    <Box>
                      <MatIcon value="info" variant="round" size={18} color="action" />
                    </Box>
                  </Tooltip>
                </Stack>
              </LabelValuePair>
            </Grid>
          </Grid>
          <Grid container gap={1} padding={2}>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Service Fee:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {currencyFormat(model?.assets?.at(0)?.serviceFee, { fallback: MISSING_DATA })}
                </Typography>
              </LabelValuePair>
            </Grid>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Other Fee:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {currencyFormat(model?.assets?.at(0)?.otherFee, { fallback: MISSING_DATA })}
                </Typography>
              </LabelValuePair>
            </Grid>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Fee Total:{" "}
                  </Typography>
                }
              >
                <Stack direction="row" gap={0.5} alignItems="center">
                  <Typography variant="body2" color="primary" height={18}>
                    {currencyFormat(model?.assets?.at(0)?.feeTotal)}
                  </Typography>
                  <Tooltip title="Service Fee + Other Fee" sx={{ height: 18, cursor: "pointer" }}>
                    <Box>
                      <MatIcon value="info" variant="round" size={18} color="action" />
                    </Box>
                  </Tooltip>
                </Stack>
              </LabelValuePair>
            </Grid>
          </Grid>
          <Grid container padding={2} justifyContent={{ xs: "flex-start", md: "flex-end" }}>
            <Grid item xs={12} sm={4} md={4.85}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    {model?.type === AllTransactionType.SELL ? "Net Revenue" : "Grand Total"}:{" "}
                  </Typography>
                }
              >
                <Stack direction="row" gap={0.5} alignItems="center">
                  <Typography variant="body2" color="primary" height={18}>
                    {currencyFormat(model?.assets?.at(0)?.grandTotal)}
                  </Typography>
                  <Tooltip
                    title={`${model?.type === AllTransactionType.SELL ? "Gross Revenue -" : "Sub Total +"} Fee Total`}
                    sx={{ height: 18, cursor: "pointer" }}
                  >
                    <Box>
                      <MatIcon value="info" variant="round" size={18} color="action" />
                    </Box>
                  </Tooltip>
                </Stack>
              </LabelValuePair>
            </Grid>
          </Grid>
          <Grid container padding={2}>
            <Grid item xs={12}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    TIF:{" "}
                  </Typography>
                }
              >
                <Stack direction="row" gap={0.5} alignItems="center">
                  <Typography variant="body2" color="primary" height={18}>
                    {model?.trade?.tif}
                  </Typography>
                  <Tooltip title="Time in force" sx={{ height: 18, cursor: "pointer" }}>
                    <Box>
                      <MatIcon value="info" variant="round" size={18} color="action" />
                    </Box>
                  </Tooltip>
                </Stack>
              </LabelValuePair>
            </Grid>
          </Grid>
          <Divider sx={{ marginBottom: 1 }} />
          <Grid container gap={1} padding={2}>
            <Grid item xs={12} md={3.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    MTM:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {currencyFormat(pricing?.price?.toString(), { fallback: MISSING_DATA })}
                </Typography>
              </LabelValuePair>
            </Grid>
            <Maybe
              condition={model?.type !== AllTransactionType.BUY && model?.currentStatus !== TransactionStatus.SETTLED}
            >
              <Grid item xs={12} md={3.5}>
                <LabelValuePair
                  label={
                    <Typography variant="body2" width={100} fontWeight="bold">
                      Inventory Cost Basis:{" "}
                    </Typography>
                  }
                >
                  <Typography variant="body2" color="primary" height={18}>
                    {currencyFormat(model?.assets?.at(0)?.projectVintage?.averageCostBasis?.toString(), {
                      fallback: MISSING_DATA,
                    })}
                  </Typography>
                </LabelValuePair>
              </Grid>
            </Maybe>
          </Grid>
        </Stack>

        <Stack component={Paper} gap={1}>
          <Typography variant="body2" fontWeight="bold" padding={1} bgcolor="lightgray">
            Optional Order Info
          </Typography>
          <Grid container gap={1} padding={2}>
            <Grid item xs={12} md={5.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    External TXN ID:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {model?.trade?.poid || MISSING_DATA}
                </Typography>
              </LabelValuePair>
            </Grid>
          </Grid>
          <Grid container gap={1} padding={2}>
            <Grid item xs={12} md={5.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Project Type:{" "}
                  </Typography>
                }
              >
                <Typography variant="body2" color="primary" height={18}>
                  {model?.assets?.at(0)?.projectVintage?.project?.projectType?.type}
                </Typography>
              </LabelValuePair>
            </Grid>
            <Grid item xs={12} md={5.5}>
              <LabelValuePair
                label={
                  <Typography variant="body2" width={100} fontWeight="bold">
                    Settlement Date:{" "}
                  </Typography>
                }
              >
                <Maybe condition={!!model?.dateFinished} fallback={MISSING_DATA}>
                  <Typography variant="body2" color="primary" height={18}>
                    {model?.dateFinished}
                  </Typography>
                </Maybe>
              </LabelValuePair>
            </Grid>
          </Grid>
        </Stack>

        <Maybe condition={!isSale && !!tradeEmailConfirmation}>
          <Stack component={Paper} gap={1}>
            <Typography variant="body2" fontWeight="bold" padding={1} bgcolor="lightgray">
              Automated Email Confirmation
            </Typography>
            <Grid container gap={1} padding={2}>
              <Grid item xs={12} md={5.5}>
                <LabelValuePair
                  label={
                    <Typography variant="body2" width={100} fontWeight="bold">
                      Reciepient(s):{" "}
                    </Typography>
                  }
                >
                  <Typography variant="body2" color="primary" height={18}>
                    {tradeEmailConfirmation?.recipientEmails?.join(", ")}
                  </Typography>
                </LabelValuePair>
              </Grid>
            </Grid>
            <Grid container padding="0 16px 16px 16px">
              <Button
                variant="contained"
                size="small"
                sx={{ width: "fit-content" }}
                onClick={handleEmailConfirmationPreviewToggle}
              >
                Preview Confirmation Details
              </Button>
            </Grid>
          </Stack>
        </Maybe>
      </Maybe>

      <Maybe condition={isSale}>
        <Stack component={Paper}>
          <SaleDetailTable assets={model?.assets} />
        </Stack>
      </Maybe>

      <AutomatedEmailConfirmationModal
        open={openEmailConfirmationPreview}
        confirmation={tradeEmailConfirmation}
        onClose={handleEmailConfirmationPreviewToggle}
      />
    </Stack>
  );
};

export default TransactionDetails;
