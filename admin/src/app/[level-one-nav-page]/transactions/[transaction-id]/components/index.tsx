"use client";

import { PurchaseResponse, TradeResponse, TradeConfirmationResponse } from "@rubiconcarbon/shared-types";
import TransactionDetailsComponent from "./details";

export default function TransactionDetails({
  assetResponse,
  tradeConfirmationResponse,
  type,
}: {
  assetResponse: PurchaseResponse | TradeResponse;
  tradeConfirmationResponse?: TradeConfirmationResponse;
  type: string;
}): JSX.Element {
  return (
    <TransactionDetailsComponent
      assetResponse={assetResponse}
      tradeConfirmationResponse={tradeConfirmationResponse}
      type={type}
    />
  );
}
