import { AuthorizeServer } from "@app/authorize-server";
import { HttpStatusLabels } from "@constants/http";
import {
  PermissionEnum,
  PurchaseRelations,
  PurchaseResponse,
  TradeConfirmationResponse,
  TradeRelation,
  TradeResponse,
} from "@rubiconcarbon/shared-types";
import TransactionDetails from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement } from "react";
import { Undefinable } from "@rubiconcarbon/frontend-shared";
import ErrorComponent from "@app/error";

/**
 * Transaction Details Page
 *
 * This is a server component that renders the Transaction Details page
 */
export default async function TransactionDetailsPage({
  params,
  searchParams,
}: {
  params: Promise<{ "transaction-id": string }>;
  searchParams: Promise<{ type: string }>;
}): Promise<JSX.Element> {
  const { "transaction-id": id } = await params;
  const { type } = await searchParams;

  const assetResponse = await withErrorHandling(async () =>
    baseApiRequest<PurchaseResponse | TradeResponse>(
      `admin/${type === "purchase" ? "purchases" : "trades"}/${id}?${generateQueryParams({
        includeRelations:
          type === "purchase"
            ? [PurchaseRelations.ASSETS, PurchaseRelations.CUSTOMER_PORTFOLIO]
            : [TradeRelation.BOOK, TradeRelation.PROJECT_VINTAGE],
      })}`,
    ),
  );

  const tradeConfirmationResponse =
    type === "purchase"
      ? undefined
      : await withErrorHandling(
          async () => baseApiRequest<TradeConfirmationResponse>(`admin/trades/${id}/confirmation`),
          (error: any) => {
            if (error?.message === HttpStatusLabels.NOTFOUND) return undefined;
            return <ErrorComponent error={error} />;
          },
        );

  // Check if the result is a server error
  if (isValidElement(assetResponse)) return assetResponse;
  if (isValidElement(tradeConfirmationResponse)) return tradeConfirmationResponse;

  return (
    <AuthorizeServer
      permissions={[type === "purchase" ? PermissionEnum.CUSTOMER_SALES_READ : PermissionEnum.TRADES_READ]}
    >
      <TransactionDetails
        assetResponse={assetResponse as PurchaseResponse | TradeResponse}
        tradeConfirmationResponse={tradeConfirmationResponse as Undefinable<TradeConfirmationResponse>}
        type={type}
      />
    </AuthorizeServer>
  );
}
