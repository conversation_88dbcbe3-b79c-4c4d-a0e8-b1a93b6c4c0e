"use client";

import { CounterpartyQueryResponse, GroupingParentQueryResponse, TradeResponse } from "@rubiconcarbon/shared-types";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useEffect } from "react";
import TradeForm from "../../../components/trade/form";
import { Nullable } from "@rubiconcarbon/frontend-shared";

export default function EditTransaction({
  tradeResponse,
  counterpartiesResponse,
  booksByParentResponse,
  type,
}: {
  tradeResponse: TradeResponse;
  counterpartiesResponse: CounterpartyQueryResponse;
  booksByParentResponse: GroupingParentQueryResponse;
  type: string;
}): Nullable<JSX.Element> {
  const { updateBreadcrumbName } = useBreadcrumbs();

  useEffect(() => {
    updateBreadcrumbName?.("Edit", type === "trade" ? "Edit Trade" : "");
  }, [type, updateBreadcrumbName]);

  return type === "trade" ? (
    <TradeForm
      tradeResponse={tradeResponse}
      counterpartiesResponse={counterpartiesResponse}
      booksByParentResponse={booksByParentResponse}
    />
  ) : null;
}
