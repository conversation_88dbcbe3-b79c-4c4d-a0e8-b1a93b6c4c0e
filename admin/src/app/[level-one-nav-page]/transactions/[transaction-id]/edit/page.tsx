import { AuthorizeServer } from "@app/authorize-server";
import {
  BookRelations,
  CounterpartyQueryResponse,
  GroupingParentQueryResponse,
  PermissionEnum,
  TradeRelation,
  TradeResponse,
} from "@rubiconcarbon/shared-types";
import EditTransaction from "./components";
import ErrorComponent from "@app/error";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { HttpStatusLabels } from "@constants/http";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Edit Transaction Page
 *
 * This is a server component that renders the Edit Transaction page
 */
export default async function EditTransactionPage({
  params,
  searchParams,
}: {
  params: Promise<{ "transaction-id": string }>;
  searchParams: Promise<{ type: string }>;
}): Promise<JSX.Element> {
  const { "transaction-id": id } = await params;
  const { type } = await searchParams;
  const isTrade = type === "trade";

  const tradeResponse = isTrade
    ? await withErrorHandling(async () =>
        baseApiRequest<TradeResponse>(
          `admin/trades/${id}?${generateQueryParams({
            includeRelations: [TradeRelation.BOOK, TradeRelation.PROJECT_VINTAGE],
          })}`,
        ),
      )
    : undefined;

  const counterpartiesResponse = isTrade
    ? await withErrorHandling(async () =>
        baseApiRequest<CounterpartyQueryResponse>(
          `admin/counterparties?${generateQueryParams({
            isEnabled: true,
            includeTotalCount: false,
            limit: SERVER_PAGINATION_LIMIT,
          })}`,
        ),
      )
    : undefined;

  const booksByParentResponse = isTrade
    ? await withErrorHandling(async () =>
        baseApiRequest<GroupingParentQueryResponse>(
          `admin/books/parents?${generateQueryParams({
            limit: SERVER_PAGINATION_LIMIT,
            isEnabled: true,
            includeTotalCount: true,
            includeRelations: [
              BookRelations.OWNER_ALLOCATIONS,
              BookRelations.OWNER_ALLOCATIONS_NESTED,
              BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
              BookRelations.PRICES,
            ],
          })}`,
        ),
      )
    : undefined;

  // Check if the result is a server error
  if (isValidElement(tradeResponse)) return tradeResponse;
  if (isValidElement(counterpartiesResponse)) return counterpartiesResponse;
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;

  return (
    <AuthorizeServer
      partiallyAuthorize
      permissions={[PermissionEnum.TRADES_UPDATE, PermissionEnum.TRADES_UPDATE_AMOUNTS]}
    >
      {tradeResponse ? (
        <EditTransaction
          tradeResponse={tradeResponse as TradeResponse}
          counterpartiesResponse={counterpartiesResponse as CounterpartyQueryResponse}
          booksByParentResponse={booksByParentResponse as GroupingParentQueryResponse}
          type={type}
        />
      ) : (
        <ErrorComponent error={{ message: HttpStatusLabels.NOTFOUND }} />
      )}
    </AuthorizeServer>
  );
}
