import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import Reconciliation from "./components";
import { reportingApiRequest } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import { Data } from "./types/general";
import { ReconTransactionsType } from "./types/transactions";
import { ReconRetirementsType } from "./types/retirements";
import { isValidElement } from "react";

const onResponse = async (response: Response): Promise<Response> =>
  new Response(
    JSON.stringify({
      data: await response.json(),
      // recon reporting api adds these headers to every response
      metadata: {
        asOf: new Date(Number(response.headers.get("x-api-data-timestamp-as-of"))),
        now: new Date(Number(response.headers.get("x-api-data-timestamp"))),
      },
    }),
    {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    },
  );

/**
 * Reconciliation Page
 *
 * This is a server component that renders the Reconciliation page
 */
export default async function ReconciliationPage(): Promise<JSX.Element> {
  const transactionsResponse = await withErrorHandling(async () =>
    reportingApiRequest<Data<ReconTransactionsType[]>>("recon/transactions", { onResponse }),
  );

  const retirementsResponse = await withErrorHandling(async () =>
    reportingApiRequest<Data<ReconRetirementsType[]>>("recon/retirements", { onResponse }),
  );

  // Check if the result is a server error
  if (isValidElement(transactionsResponse)) return transactionsResponse;
  if (isValidElement(retirementsResponse)) return retirementsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.REPORTING_RECON]}>
      <Reconciliation
        transactionsResponse={transactionsResponse as Data<ReconTransactionsType[]>}
        retirementsResponse={retirementsResponse as Data<ReconRetirementsType[]>}
      />
    </AuthorizeServer>
  );
}
