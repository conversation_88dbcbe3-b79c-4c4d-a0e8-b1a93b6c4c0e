"use client";

import Page from "@components/layout/containers/page";
import ReconRetirements from "./retirements";
import ReconTransactions from "./transactions";
import { Data } from "../types/general";
import { ReconTransactionsType } from "../types/transactions";
import { ReconRetirementsType } from "../types/retirements";

export default function Reconciliation({
  transactionsResponse,
  retirementsResponse,
}: {
  transactionsResponse: Data<ReconTransactionsType[]>;
  retirementsResponse: Data<ReconRetirementsType[]>;
}): JSX.Element {
  return (
    <Page>
      <ReconTransactions transactionsResponse={transactionsResponse} />
      <ReconRetirements retirementsResponse={retirementsResponse} />
    </Page>
  );
}
