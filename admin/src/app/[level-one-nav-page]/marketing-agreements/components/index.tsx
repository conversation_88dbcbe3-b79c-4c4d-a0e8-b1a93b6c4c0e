"use client";

import Page from "@components/layout/containers/page";
import { MarketingAgreementQueryResponse } from "@rubiconcarbon/shared-types";
import MarketingAgreementsComponent from "./marketing-agreements";

const MarketingAgreementsPage = ({
  marketingAgreementsResponse,
}: {
  marketingAgreementsResponse: MarketingAgreementQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <MarketingAgreementsComponent marketingAgreementsResponse={marketingAgreementsResponse} />
    </Page>
  );
};

export default MarketingAgreementsPage;
