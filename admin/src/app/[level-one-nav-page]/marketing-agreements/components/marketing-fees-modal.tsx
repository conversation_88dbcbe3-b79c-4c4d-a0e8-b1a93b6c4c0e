import { Values } from "@components/ui/generic-table/hooks/use-generic-table-utility";
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldError,
  FieldErrorsImpl,
  Merge,
  UseFieldArrayRemove,
} from "react-hook-form";
import { FeeStructure, MarketingAgreementModel } from "../models/marketing-agreement";
import { Box, Divider, IconButton, Stack, TextField, Typography } from "@mui/material";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { Fragment, useLayoutEffect } from "react";
import { NumericFormat } from "react-number-format";
import {
  classcat,
  currencyFormat,
  isNothing,
  Maybe,
  percentageFormat,
  toDecimal,
} from "@rubiconcarbon/frontend-shared";

import classes from "../styles/marketing-fees-modal.module.scss";

type MarketingFeesModalProps = {
  fees: FeeStructure[];
  control: Control<Values<MarketingAgreementModel>, any>;
  fields: FieldArrayWithId<Values<MarketingAgreementModel>, "amends.0.feeStructure", "id">[];
  feeStructureErrors: Merge<FieldError, Merge<FieldError, FieldErrorsImpl<FeeStructure>>[]>;
  readOnly: boolean;
  editing: boolean;
  remove: UseFieldArrayRemove;
};

const MarketingFeesModal = ({
  fees,
  control,
  fields,
  feeStructureErrors,
  readOnly = true,
  editing = false,
  remove,
}: MarketingFeesModalProps): JSX.Element => {
  const size = fees?.length;
  const lastEntry = fees?.at?.(size - 1);

  useLayoutEffect(() => {
    if (editing && isNothing(lastEntry?.minBracket, ["string"])) remove(size - 1);
  }, [editing, lastEntry, remove, size]);

  return (
    <Stack className={classes.Table} gap={2}>
      <Stack gap={1}>
        <Stack className={classes.TableHeader} direction="row" gap={1}>
          <Typography className={classes.TableHeaderColumn} width={readOnly ? "50%" : "65%"}>
            Sales Price Brackets (min - max)
          </Typography>
          <Typography className={classes.TableHeaderColumn} width={readOnly ? "50%" : "15%"}>
            Marketing Fees
          </Typography>
          <Maybe condition={!readOnly}>
            <Typography className={classes.TableHeaderColumn} width="15%">
              Actions
            </Typography>
          </Maybe>
        </Stack>
        <Divider flexItem />
      </Stack>
      <Stack className={classes.TableBody} gap={2}>
        {(readOnly ? fees : fields).map(
          ({ _id, id, order }: { _id?: string; id?: string; order?: number }, index: number) => (
            <Fragment key={id || _id}>
              <Stack
                className={classes.TableRow}
                direction="row"
                alignItems={readOnly ? "center !important" : "flext-start"}
                gap={1}
              >
                <Stack
                  direction="row"
                  className={classes.TableBodyColumn}
                  width={readOnly ? "50%" : "65%"}
                  gap={readOnly ? 3 : 0}
                >
                  <Box
                    minWidth={readOnly ? 0 : 145}
                    width={readOnly ? "auto" : "45%"}
                    alignSelf={readOnly ? "center" : "flex-start"}
                  >
                    <Maybe condition={readOnly}>
                      <Typography>{currencyFormat(fees?.at(index)?.minBracket)}</Typography>
                    </Maybe>
                    <Maybe condition={!readOnly}>
                      {() => (
                        <Controller
                          control={control}
                          name={`amends.0.feeStructure.${index}.minBracket`}
                          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
                            <NumericFormat
                              allowNegative={false}
                              thousandSeparator
                              decimalScale={2}
                              fixedDecimalScale={true}
                              prefix="$"
                              value={value}
                              onValueChange={({ floatValue }) => onChange(floatValue)}
                              customInput={TextField}
                              InputProps={{
                                ref,
                                classes: {
                                  root: classcat([classes.InputRoot, classes.InputRootMinMax]),
                                  input: classes.Input,
                                },
                              }}
                              InputLabelProps={{
                                classes: {
                                  root: classes.InputLabel,
                                },
                              }}
                              {...otherProps}
                              error={!!feeStructureErrors?.[index]?.minBracket}
                              helperText={feeStructureErrors?.[index]?.minBracket?.message}
                              fullWidth
                            />
                          )}
                        />
                      )}
                    </Maybe>
                  </Box>
                  <Typography
                    width={readOnly ? "auto" : "5%"}
                    textAlign="center"
                    alignSelf={readOnly ? "center" : "flex-start"}
                    marginTop={readOnly ? "0" : "8px"}
                  >
                    {readOnly && isNothing(fees?.at?.(index)?.maxBracket, ["string"]) ? "and" : "to"}
                  </Typography>
                  <Box
                    minWidth={readOnly ? 0 : 145}
                    width={readOnly ? "auto" : "45%"}
                    alignSelf={readOnly ? "center" : "flex-start"}
                  >
                    <Maybe condition={readOnly}>
                      <Typography>
                        {isNothing(fees?.at?.(index)?.maxBracket, ["string"])
                          ? "Above"
                          : currencyFormat(fees?.at(index)?.maxBracket)}
                      </Typography>
                    </Maybe>
                    <Maybe condition={!readOnly}>
                      {() => (
                        <Controller
                          control={control}
                          name={`amends.0.feeStructure.${index}.maxBracket`}
                          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
                            const item = fees?.at(index);
                            const isAbove = item?.isAbove;

                            return (
                              <NumericFormat
                                allowNegative={false}
                                thousandSeparator
                                decimalScale={2}
                                fixedDecimalScale={true}
                                prefix="$"
                                value={isAbove ? "" : value}
                                onValueChange={({ floatValue }) => onChange(floatValue)}
                                customInput={TextField}
                                InputProps={{
                                  ref,
                                  classes: {
                                    root: classcat([classes.InputRoot, classes.InputRootMinMax]),
                                    input: classes.Input,
                                  },
                                }}
                                InputLabelProps={{
                                  classes: {
                                    root: classes.InputLabel,
                                  },
                                }}
                                {...otherProps}
                                error={!!feeStructureErrors?.[index]?.maxBracket}
                                helperText={feeStructureErrors?.[index]?.maxBracket?.message}
                                fullWidth
                              />
                            );
                          }}
                        />
                      )}
                    </Maybe>
                  </Box>
                </Stack>
                <Box
                  className={classes.TableBodyColumn}
                  width={readOnly ? "50%" : "15%"}
                  alignSelf={readOnly ? "center" : "flex-start"}
                >
                  <Maybe condition={readOnly}>
                    <Typography>{percentageFormat(fees?.at(index)?.marketingFee, { scale: 0.01 })}</Typography>
                  </Maybe>
                  <Maybe condition={!readOnly}>
                    {() => (
                      <Controller
                        control={control}
                        name={`amends.0.feeStructure.${index}.marketingFee`}
                        render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
                          <NumericFormat
                            allowNegative={false}
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale={true}
                            suffix="%"
                            value={toDecimal(value, { treatNothingAsNaN: true }).toString()}
                            isAllowed={({ floatValue }) => !floatValue || floatValue <= 100}
                            onValueChange={({ floatValue }) => onChange(floatValue)}
                            customInput={TextField}
                            InputProps={{
                              ref,
                              classes: {
                                root: classes.InputRoot,
                                input: classes.Input,
                              },
                            }}
                            InputLabelProps={{
                              classes: {
                                root: classes.InputLabel,
                              },
                            }}
                            {...otherProps}
                            error={!!feeStructureErrors?.[index]?.marketingFee}
                            helperText={feeStructureErrors?.[index]?.marketingFee?.message}
                            fullWidth
                          />
                        )}
                      />
                    )}
                  </Maybe>
                </Box>
                <Maybe condition={!readOnly}>
                  <Box className={classes.TableBodyColumn} width="15%">
                    <IconButton onClick={() => remove(index)} disabled={size === 1}>
                      <MatIcon value="delete" variant="round" size={25} color={size === 1 ? "disabled" : "primary"} />
                    </IconButton>
                  </Box>
                </Maybe>
              </Stack>
              <Maybe condition={lastEntry?.order !== order}>
                <Divider flexItem />
              </Maybe>
            </Fragment>
          ),
        )}
      </Stack>
      <Maybe
        condition={feeStructureErrors?.type === "validateFeeStructure" && feeStructureErrors?.message !== "Required"}
      >
        <Stack gap={0.5}>
          {feeStructureErrors?.message?.split("; ").map((message, index) => (
            <Typography key={index} className={classes.ErrorMessage}>
              {message}
            </Typography>
          ))}
        </Stack>
      </Maybe>
    </Stack>
  );
};

export default MarketingFeesModal;
