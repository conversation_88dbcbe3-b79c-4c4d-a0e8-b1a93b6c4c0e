import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { Dispatch, SetStateAction } from "react";
import { MarketingAgreementLineItem, MarketingAgreementModel } from "../models/marketing-agreement";

export type MarketingAgreementExtensions = {
  amendingNestedRows: () => boolean;
  toggleMarketingFeesModal: () => void;
  toggleDocumentsModal: () => void;
  toggleCancelAgreementModal: () => void;
  setViewingRow: Dispatch<SetStateAction<GenericTableRowModel<MarketingAgreementModel>>>;
};

export type MarketingAgreementLineItemExtensions = {
  readonlyParent: boolean;
  cancelLineItem: (row: GenericTableRowModel<MarketingAgreementLineItem>) => Promise<void>;
};
