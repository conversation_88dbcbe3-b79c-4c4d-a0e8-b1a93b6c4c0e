import { AuthorizeServer } from "@app/authorize-server";
import { MarketingAgreementQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement } from "react";
import MarketingAgreements from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Marketing Agreements Page
 *
 * This is a server component that renders the Marketing Agreements page
 */
export default async function MarketingAgreementsPage(): Promise<JSX.Element> {
  const marketingAgreementsResponse = await withErrorHandling(async () =>
    baseApiRequest<MarketingAgreementQueryResponse>(
      `admin/marketing-agreements?${generateQueryParams({
        includeTotalCount: true,
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(marketingAgreementsResponse)) return marketingAgreementsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.MARKETING_AGREEMENTS_READ]}>
      <MarketingAgreements
        marketingAgreementsResponse={marketingAgreementsResponse as MarketingAgreementQueryResponse}
      />
    </AuthorizeServer>
  );
}
