import {
  MarketingAgreementFeePercentageStructure,
  MarketingAgreementLineItemResponse,
  MarketingAgreementResponse,
  MarketingAgreementStatus,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsDate, IsNotEmpty, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { PrimitiveTypeProject } from "@models/primitive-type-project";
import { IsNotEmptyString, isNothing, MaxOfField, MinOfField } from "@rubiconcarbon/frontend-shared";
import { ValidateFeeStructure } from "../utilities/fee-structure-validator";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

export class FeeStructure implements Omit<MarketingAgreementFeePercentageStructure, "marketingFee"> {
  constructor() {}

  _id: string = uuid();

  order: number;

  @IsNotEmpty({ message: "Required" })
  marketingFee: string = undefined; // decimal;

  @ValidateIf((o: FeeStructure) => !isNothing(o.maxBracket, ["string"]))
  @MinOfField<FeeStructure>("minBracket", { message: "Max should be more than Min" })
  maxBracket: string;

  @ValidateIf(
    (o: FeeStructure) =>
      isNothing(o.minBracket, ["string"]) ||
      (!isNothing(o.minBracket, ["string"]) && !isNothing(o.maxBracket, ["string"])),
  )
  @MaxOfField<FeeStructure>("maxBracket", { message: "Min should be less than Max" })
  @IsNotEmpty({ message: "Required" })
  minBracket: string;

  isAbove?: boolean;
}

export class MarketingAgreementLineItem
  implements Omit<MarketingAgreementLineItemResponse, "createdAt" | "updatedAt" | "projectVintage">
{
  constructor() {}

  _id: string = undefined; // for auto-generating render ids

  id: uuid;

  lineItemKey: string;

  uiKey: string = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeVintage)
  projectVintage?: PrimitiveTypeVintage;

  @IsNotEmpty({ message: "Required" })
  amount: number;

  amountIssued?: number;

  status: MarketingAgreementStatus;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => o?.creating)
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  originalDeliveryDate: Date;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => !o?.creating)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  lastUpdatedDeliveryDate: Date;

  @ValidateIf((o: GenericTableRowModel<MarketingAgreementLineItem>) => !!o?.expirationDate)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @Type(() => Date)
  expirationDate: Date;
}

export class MarketingAgreementModel
  implements
    Omit<
      MarketingAgreementResponse,
      "floorPrice" | "feeStructure" | "lineItems" | "createdAt" | "updatedAt" | "project"
    >
{
  constructor() {}

  id: uuid = undefined;

  uiKey: string = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeProject)
  project: PrimitiveTypeProject;

  @IsNotEmptyString({ message: "Cannot be an empty string" })
  @IsNotEmpty({ message: "Required" })
  developer: string;

  @IsNotEmpty({ message: "Required" })
  floorPrice: string = undefined; // decimal

  @ValidateNested()
  @Type(() => FeeStructure)
  @ArrayNotEmpty({ message: "Required" })
  @ValidateFeeStructure()
  feeStructure: FeeStructure[];

  @IsNotEmpty({ message: "Required" })
  status: MarketingAgreementStatus;

  lineItems: MarketingAgreementLineItem[];

  createdAt: string = undefined; // date

  updatedAt: string = undefined; // date
}

export class MarketingAgreementFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => MarketingAgreementModel)
  amends: MarketingAgreementModel[];
}

export class MultiMarketingAgreementLineItemFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => MarketingAgreementLineItem)
  amends: MarketingAgreementLineItem[];
}
