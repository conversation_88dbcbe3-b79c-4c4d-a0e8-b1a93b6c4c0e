import React, { useState, BaseSyntheticEvent } from "react";
import { useRouter } from "next/navigation";
import { OrganizationResponse, UserCreateRequest, OrganizationUserRole } from "@rubiconcarbon/shared-types";
import {
  Box,
  Grid,
  Card,
  CardActions,
  CardContent,
  TextField,
  Button,
  Autocomplete,
  FormLabel,
  FormControl,
} from "@mui/material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import RolesGroup from "@components/ui/roles-group/roles-group";
import InviteConfirmationModal from "./invite-confirmation-modal";
import { HttpStatusLabels } from "@/constants/http";
import { HttpStatusLabelsType } from "@/types/http";

export default function UserInvite({
  organizationsResponse,
}: {
  organizationsResponse: OrganizationResponse[];
}): JSX.Element {
  const [submissionInFlight] = useState<boolean>(false);
  const router = useRouter();
  const { popFromPath } = useNavigation();

  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationResponse>();

  const [isDisableOrganizations] = useState<boolean>(false);
  const [email, setEmail] = useState<string>("");
  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [selectedRole, setSelectedRole] = useState<OrganizationUserRole>();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const [userInvite, setUserInvite] = useState<UserCreateRequest>();

  const organizationSelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: OrganizationResponse,
  ): void => {
    setSelectedOrganization(newValue);
  };

  const onSubmit = (event: BaseSyntheticEvent): void => {
    event.preventDefault();

    const payload: UserCreateRequest = {
      email,
      organizationId: selectedOrganization!.id,
      organizationRoles: [selectedRole!],
      firstName: firstName.trim(),
      lastName: lastName.trim(),
    };
    setUserInvite(payload);
    setIsConfirmationDialogOpen(true);
  };

  const organizationDefaultProps = {
    options:
      organizationsResponse
        .filter((organization) => organization.isEnabled)
        ?.sort((a, b) => a.name?.localeCompare(b.name)) ?? [],
    getOptionLabel: (option: OrganizationResponse): string => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: OrganizationResponse, value: OrganizationResponse): boolean => option.id === value.id,
  };

  const roleChangeHandler = (event: React.ChangeEvent<HTMLInputElement>, role: OrganizationUserRole): void => {
    setSelectedRole(role);
  };

  const emailHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setEmail(event.target.value);
  };

  function onSuccessHandler(message: string): void {
    enqueueSuccess(message);
  }

  function closeConfirmationHandler(submitResult?: { result: HttpStatusLabelsType }): void {
    setIsConfirmationDialogOpen(false);

    if (submitResult?.result && submitResult.result === HttpStatusLabels.SUCCESS) popFromPath(1);
  }

  const itemDtailsCard = (
    <React.Fragment>
      <CardContent>
        <form id="permissions-request-form" onSubmit={onSubmit}>
          <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
            <Grid container gap={4} flexDirection="column">
              <FormControl fullWidth>
                <FormLabel component="legend" sx={{ marginTop: 1.5, marginBottom: 2.5, lineHeight: "150%" }}>
                  Create a new user in an organization. We&apos;ll send them an email to setup their password.
                </FormLabel>
                <Autocomplete
                  {...organizationDefaultProps}
                  onChange={organizationSelectionHandler}
                  value={selectedOrganization}
                  loading
                  disabled={isDisableOrganizations}
                  disablePortal
                  id="organization"
                  renderInput={(params) => <TextField required {...params} label="Organization" />}
                />
                <TextField
                  type={"email"}
                  sx={{ marginTop: "20px" }}
                  id="email"
                  label="Email address"
                  value={email}
                  onChange={emailHandler}
                  required
                  inputProps={{ maxLength: 256 }}
                />
                <TextField
                  sx={{ marginTop: "20px" }}
                  type={"text"}
                  id="firstName"
                  label="First name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value.trimStart())}
                  required
                  inputProps={{ maxLength: 256 }}
                />
                <TextField
                  sx={{ marginTop: "20px" }}
                  type={"text"}
                  id="lastName"
                  label="Last name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value.trimStart())}
                  required
                  inputProps={{ maxLength: 256 }}
                />
              </FormControl>
              <FormControl fullWidth sx={{ marginTop: -3 }}>
                <RolesGroup selectedRole={selectedRole} roleChangeHandler={roleChangeHandler} />
              </FormControl>
            </Grid>
          </fieldset>
        </form>
      </CardContent>
      <CardActions
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Box ml={2} sx={{ display: "flex" }}></Box>
        <Box mr={2} mb={1} mt={1}>
          <Button
            sx={{ marginRight: 3, fontWeight: 600 }}
            disabled={submissionInFlight}
            variant="text"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="permissions-request-form"
            sx={{ px: 3.5, color: "#FFFFFF" }}
            disabled={submissionInFlight}
          >
            Create
          </Button>
        </Box>
      </CardActions>
    </React.Fragment>
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <Box>
        {userInvite && selectedOrganization && (
          <InviteConfirmationModal
            isOpen={isConfirmationDialogOpen}
            userInvite={userInvite}
            organizationName={selectedOrganization.name}
            onClose={closeConfirmationHandler}
            onConfirm={onSuccessHandler}
            onError={enqueueError}
          />
        )}
      </Box>
    </Box>
  );
}
