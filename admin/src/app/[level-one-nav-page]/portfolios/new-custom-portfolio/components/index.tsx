"use client";

import Page from "@components/layout/containers/page";
import { OrganizationResponse } from "@rubiconcarbon/shared-types";
import NewCustomPortfolioComponent from "./new-custom-portfolio";

const NewCustomPortfolio = ({ organizations }: { organizations: OrganizationResponse[] }): JSX.Element => {
  return (
    <Page>
      <NewCustomPortfolioComponent organizations={organizations} />
    </Page>
  );
};

export default NewCustomPortfolio;
