import { AuthorizeServer } from "@app/authorize-server";
import { OrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import NewCustomPortfolio from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement } from "react";

/**
 * New Custom Portfolio Page
 *
 * This is a server component that renders the New Custom Portfolio page
 */
export default async function NewCustomPortfolioPage(): Promise<JSX.Element> {
  const organizations = await withErrorHandling(async () =>
    baseApiRequest<OrganizationResponse[]>(`admin/organizations`),
  );

  // Check if the result is a server error
  if (isValidElement(organizations)) return organizations;

  return (
    <AuthorizeServer permissions={[PermissionEnum.TRADES_READ, PermissionEnum.BOOKS_CREATE_RCT_PORTFOLIOS]}>
      <NewCustomPortfolio organizations={organizations as OrganizationResponse[]} />
    </AuthorizeServer>
  );
}
