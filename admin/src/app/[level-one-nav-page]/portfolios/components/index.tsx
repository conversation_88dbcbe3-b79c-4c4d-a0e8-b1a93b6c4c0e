"use client";

import Page from "@components/layout/containers/page";
import { AdminBookQueryResponse } from "@rubiconcarbon/shared-types";
import PortfoliosTable from "./portfolios-table";

const Portfolios = ({ booksResponse }: { booksResponse: AdminBookQueryResponse }): JSX.Element => {
  return (
    <Page>
      <PortfoliosTable booksResponse={booksResponse} />
    </Page>
  );
};

export default Portfolios;
