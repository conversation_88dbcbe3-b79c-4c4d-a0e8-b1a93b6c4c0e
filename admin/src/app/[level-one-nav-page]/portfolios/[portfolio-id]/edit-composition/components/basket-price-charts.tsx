import React, { useEffect, useState } from "react";
import { Box, Grid, Typography } from "@mui/material";
import ProjectVintage from "@models/project-vintage";
import COLORS from "@components/ui/theme/colors";
import currencyFormat from "@/utils/formatters/currency-format";
import { MISSING_DATA } from "@constants/constants";
import { getAverageByKey } from "@utils/helpers/general/general";
import ChangeDirectionIcon from "@components/ui/change-direction-icon/change-direction-icon";
import SingleValueChart, { SingleValueChartData } from "./single-value-chart";
import { Undefinable } from "@rubiconcarbon/frontend-shared";

const sectionStyle = {
  fontWeight: 500,
  color: COLORS.pureBlack,
  fontSize: "20px",
  lineHeight: "60px",
  paddingLeft: "30px",
};

interface BasketPriceChartsProps {
  currentAllocation: ProjectVintage[];
  projectedAllocation: ProjectVintage[];
}

export default function BasketPriceCharts(props: BasketPriceChartsProps): JSX.Element {
  const { currentAllocation, projectedAllocation } = props;
  const [currentMarketPrice, setCurrentMarketPrice] = useState<Undefinable<number>>();
  const [newMarketPrice, setNewMarketPrice] = useState<Undefinable<number>>();
  const [currentCostBasis, setCurrentCostBasis] = useState<Undefinable<number>>();
  const [newCostBasis, setNewCostBasis] = useState<Undefinable<number>>();

  useEffect(() => {
    if (currentAllocation) {
      setCurrentMarketPrice(getAverageByKey(currentAllocation, "latestTraderPrice", "quantity"));
      setCurrentCostBasis(getAverageByKey(currentAllocation, "averageCostBasis", "quantity"));
    }
  }, [currentAllocation]);

  useEffect(() => {
    if (projectedAllocation) {
      setNewMarketPrice(getAverageByKey(projectedAllocation, "latestTraderPrice", "quantity"));
      setNewCostBasis(getAverageByKey(projectedAllocation, "averageCostBasis", "quantity"));
    }
  }, [projectedAllocation]);

  const currectPriceChartData: SingleValueChartData = {
    mainLabel: "Current MTM",
    value: currentMarketPrice ? currencyFormat(currentMarketPrice, 2)! : MISSING_DATA,
    backColor: COLORS.paleBlue,
  };

  const newPriceChartData: SingleValueChartData = {
    mainLabel: "New MTM",
    value: newMarketPrice ? currencyFormat(newMarketPrice, 2)! : MISSING_DATA,
    backColor: COLORS.paleGreen,
    icon: <ChangeDirectionIcon currentValue={currentMarketPrice} newValue={newMarketPrice} />,
  };

  const currentCBChartData: SingleValueChartData = {
    mainLabel: "Current Cost Basis",
    value: currentCostBasis ? currencyFormat(currentCostBasis, 4)! : MISSING_DATA,
    backColor: COLORS.paleBlue,
  };

  const newCBChartData: SingleValueChartData = {
    mainLabel: "New Cost Basis",
    value: newCostBasis ? currencyFormat(newCostBasis, 4)! : MISSING_DATA,
    backColor: COLORS.paleGreen,
    icon: <ChangeDirectionIcon currentValue={currentCostBasis} newValue={newCostBasis} />,
  };

  return (
    <Grid
      container
      item
      xs={12}
      mt={2}
      sx={{
        display: "flex",
        border: "1px solid rgba(0, 0, 0, 0.23)",
        paddingBottom: "40px",
      }}
    >
      <Grid item xs={12}>
        <Typography variant="body1" component="span" sx={sectionStyle}>
          Price
        </Typography>
      </Grid>
      <Grid item xs={6} mt={-1}>
        <Grid container alignItems="flex-start" justifyContent="center">
          <Grid item xs={6}>
            <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
              <SingleValueChart singleValueChartData={currentCBChartData} />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
              <SingleValueChart singleValueChartData={newCBChartData} />
            </Box>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={6} mt={-1}>
        <Grid container alignItems="flex-start" justifyContent="center">
          <Grid item xs={6}>
            <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
              <SingleValueChart singleValueChartData={currectPriceChartData} />
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ paddingLeft: "10px", paddingRight: "10px" }}>
              <SingleValueChart singleValueChartData={newPriceChartData} />
            </Box>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
