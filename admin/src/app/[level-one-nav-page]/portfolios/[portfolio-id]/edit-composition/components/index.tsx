"use client";

import Page from "@components/layout/containers/page";
import BookComposition from "./basket-composition";
import { AdminBookResponse } from "@rubiconcarbon/shared-types";

const PortfolioComposition = ({ portfolio }: { portfolio: AdminBookResponse }): JSX.Element => {
  return (
    <Page>
      <BookComposition portfolio={portfolio} />
    </Page>
  );
};

export default PortfolioComposition;
