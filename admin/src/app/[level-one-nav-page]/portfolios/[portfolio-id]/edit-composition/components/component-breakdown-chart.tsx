import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Typography from "@mui/material/Typography";
import SquareIcon from "@mui/icons-material/Square";
import { TableContainer } from "@mui/material";
import ProjectVintage from "@models/project-vintage";
import { mapVintagesToChart } from "../utilities/composition-data-map";

const maxChartCap = 1000;

const titleStyle = {
  fontWeight: 500,
  color: "#000000",
  fontSize: "18px",
  lineHeight: "32px",
};

const subTitleStyle = {
  fontWeight: 400,
  color: "#000000",
  fontSize: "14px",
  lineHeight: "24px",
};

const tableTitleStyle = {
  color: "#121212",
  fontWeight: 600,
  textAlign: "left",
};

interface ComponentBreakdownProps {
  basketComponents: ProjectVintage[];
  colorScale: string[];
  description?: string;
  title: string;
  backColor: string;
  groupField: string;
  groupTitle: string;
  isCap?: boolean;
}

export default function ComponentBreakdown({
  basketComponents,
  colorScale,
  description,
  title,
  backColor,
  groupField,
  groupTitle,
  isCap = true,
}: ComponentBreakdownProps): JSX.Element {
  if (!basketComponents) return <></>;

  const chartCap = isCap === true ? colorScale.length : maxChartCap;

  const [tableData] = mapVintagesToChart(basketComponents, groupField, chartCap);

  const getBoxColor = (index: number): string => {
    return index > colorScale.length - 1 ? colorScale[colorScale.length - 1] : colorScale[index];
  };

  return (
    <Box flexDirection="column" sx={{ width: "100%", paddingBottom: 2, backgroundColor: backColor, padding: "20px" }}>
      <Grid container alignItems="flex-start" justifyContent="center">
        <Grid item xs={12}>
          <Typography variant="body2" component="span" sx={titleStyle}>
            {title}
          </Typography>
        </Grid>
        <Grid item xs={12} mt={1} mb={3}>
          <Box height={30}>
            <Typography variant="body2" component="span" sx={subTitleStyle}>
              {description}
            </Typography>
          </Box>
        </Grid>
        <Grid item xs={12} aria-label="Basket composition legend" role="figure">
          <TableContainer sx={{ maxHeight: 400, paddingRight: 1 }}>
            <Table size="small" padding="none" sx={{ overflow: "scroll" }}>
              <TableHead>
                <TableRow>
                  <TableCell />
                  <TableCell sx={tableTitleStyle}>{groupTitle}</TableCell>
                  <TableCell sx={tableTitleStyle}>Proportion</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tableData.map((data, index) => (
                  <TableRow key={data.x}>
                    <TableCell sx={{ padding: "5px", width: "30px" }}>
                      <SquareIcon
                        sx={{
                          float: "left",
                          color: getBoxColor(index),
                          width: "15px",
                        }}
                      />
                    </TableCell>
                    <TableCell
                      sx={{
                        padding: "0.5rem 0",
                        textAlign: "left",
                        width: "350px",
                      }}
                    >
                      {data.x}
                    </TableCell>
                    <TableCell align="right" sx={{ padding: "0.5rem 0", textAlign: "left" }}>{`${data.y}%`}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>
    </Box>
  );
}
