import React from "react";
import { Box, Grid, Typography, Tooltip } from "@mui/material";
import COLORS from "@components/ui/theme/colors";
import DetailsIcon from "@mui/icons-material/Info";
import { Maybe } from "@rubiconcarbon/frontend-shared";

export interface SingleValueChartData {
  mainLabel?: string;
  seconndaryLabel?: string;
  value: string;
  backColor?: string;
  icon?: JSX.Element;
  tooltip?: string;
}

interface SingleValueChartProps {
  singleValueChartData: SingleValueChartData;
}

export default function SingleValueChart(props: SingleValueChartProps): JSX.Element {
  const { singleValueChartData } = props;

  const baseStyle = {
    fontWeight: 500,
    color: COLORS.pureBlack,
    paddingLeft: "30px",
  };

  const valueStyle = {
    fontSize: "32px",
  };

  const valueLabelStyle = {
    fontSize: "20px",
  };

  const valueSecLabelStyle = {
    fontSize: "14px",
  };

  const componentStyle = {
    padding: 1.5,
    borderRadius: "4px",
    height: "100%",
    backgroundColor: singleValueChartData.backColor ? singleValueChartData.backColor : COLORS.lightGrey,
  };

  return (
    <Box style={componentStyle}>
      <Grid container item xs={12} mt={2} sx={{ display: "flex" }}>
        <Grid item xs={12}>
          <Typography variant="body1" component="span" sx={{ ...baseStyle, ...valueStyle }}>
            {singleValueChartData.value} {singleValueChartData.icon}
          </Typography>
          <Maybe condition={singleValueChartData?.tooltip !== undefined}>
            <Tooltip title={singleValueChartData.tooltip} placement="top">
              <DetailsIcon sx={{ color: COLORS.rubiconGreen }} />
            </Tooltip>
          </Maybe>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="body1" component="span" sx={{ ...baseStyle, ...valueLabelStyle }}>
            {singleValueChartData.mainLabel}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="body1" component="span" sx={{ ...baseStyle, ...valueSecLabelStyle }}>
            {singleValueChartData.seconndaryLabel}
          </Typography>
        </Grid>
      </Grid>
    </Box>
  );
}
