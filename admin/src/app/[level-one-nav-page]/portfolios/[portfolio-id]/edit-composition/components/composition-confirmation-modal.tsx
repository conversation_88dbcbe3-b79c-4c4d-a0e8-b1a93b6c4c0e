import React, { useContext } from "react";
import { Box, Typography, Table, TableBody, TableCell, TableContainer, TableRow, Grid } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import { useState } from "react";
import { AxiosContext } from "@providers/axios-provider";
import BasketCompositionVintage from "@models/basket-composition-vintage";
import integerFormat from "@/utils/formatters/integer-format";
import { isEmpty } from "lodash";
import { uuid, TransferRequest, AssetTransferRequest, TransferResponse } from "@rubiconcarbon/shared-types";
import { BaseDialogProps, DialogBackdrop } from "@models/dialogs";

interface CompositionConfirmaModalProps extends BaseDialogProps {
  basketComposition: BasketCompositionVintage[];
  basketId: uuid;
  refreshData: () => void;
}

export default function CompositionConfirmationModal({
  isOpen,
  basketComposition,
  basketId,
  onClose,
  refreshData,
  onConfirm,
  onError,
}: CompositionConfirmaModalProps): JSX.Element | null {
  const [requestInFlight, setRequestInFlight] = useState(false);
  const { api } = useContext(AxiosContext);
  const defaultPortfolioId = uuid(process.env.NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT);

  const buildPayload = (composition: BasketCompositionVintage[]): TransferRequest => {
    const assetTransfers: AssetTransferRequest[] = [];

    if (composition?.length > 0) {
      composition.forEach((element) => {
        const transferAmount = element.quantity - (element.originalQuantity ?? 0);
        if (transferAmount < 0) {
          assetTransfers.push({
            amount: Math.abs(transferAmount),
            assetId: element.id,
            destinationId: defaultPortfolioId,
            sourceId: basketId,
          });
        } else {
          assetTransfers.push({
            amount: transferAmount,
            assetId: element.id,
            destinationId: basketId,
            sourceId: defaultPortfolioId,
          });
        }
      });
    }

    return {
      assetTransfers,
    };
  };

  const submitCompositionHandler = (): void => {
    const payload = buildPayload(basketComposition);
    setRequestInFlight(true);
    api
      .post<TransferResponse>(`admin/transfers`, payload)
      .then(() => {
        refreshData();
        onConfirm?.("Successfully updated portfolio composition");
        onClose();
      })
      .catch((e) => {
        let message = "Failed to update portfolio composition. ";
        if (!isEmpty(e?.response?.data?.message)) {
          message += e?.response?.data?.message;
        }
        console.error(message, e);
        onError?.(message);
      })
      .finally(() => {
        onClose();
        setRequestInFlight(false);
      });
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth={"lg"}>
      <DialogTitle>Confirm Portfolio Update</DialogTitle>
      <DialogContent sx={{ marginTop: "-20px" }}>
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          Are you sure you want to save the following changes?
        </Typography>
        <Box mt={3}>
          <TableContainer sx={{ width: 750, maxHeight: 500 }}>
            <Table aria-label="composition summary table">
              <TableBody>
                {basketComposition?.map((row) => (
                  <TableRow key={row.id} sx={{ "& > *": { borderBottom: "unset" } }}>
                    <TableCell align="left">{row.name}</TableCell>
                    <TableCell align="left">
                      {row.quantity === 0 ? (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <DeleteIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">Removed {integerFormat(row.originalQuantity)}</Typography>
                        </Grid>
                      ) : row.originalQuantity ? (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <CheckIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">
                            Updated from {integerFormat(row.originalQuantity)} to {integerFormat(row.quantity)}{" "}
                          </Typography>
                        </Grid>
                      ) : (
                        <Grid style={{ display: "flex", color: "#094436" }}>
                          <AddIcon sx={{ paddingBottom: "4px" }} />
                          <Typography variant="body2">Added {integerFormat(row.quantity)}</Typography>
                        </Grid>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
        <DialogBackdrop requestInFlight={requestInFlight} />
      </DialogContent>
      <DialogActions>
        <Button disabled={requestInFlight} variant="text" onClick={onClose}>
          Cancel
        </Button>
        <Button
          disabled={requestInFlight}
          variant="contained"
          type="submit"
          sx={{ px: 3.5, color: "#FFFFFF" }}
          onClick={submitCompositionHandler}
        >
          Yes, proceed
        </Button>
      </DialogActions>
    </Dialog>
  );
}
