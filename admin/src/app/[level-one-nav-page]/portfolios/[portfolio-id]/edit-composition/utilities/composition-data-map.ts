import { flow } from "lodash";
import ProjectVintage from "@models/project-vintage";

export interface ChartData {
  x: string;
  y: number;
}

function sumCompositionByDisplayGroup(basketComponents: ProjectVintage[], groupField: string): Record<string, number> {
  return basketComponents.reduce(
    (groups, basketComponent) => {
      const accumulator = { ...groups };
      const key = basketComponent[groupField];
      if (!accumulator[key]) {
        accumulator[key] = 0;
      }
      accumulator[key] += basketComponent.quantity;
      return accumulator;
    },
    {} as Record<string, number>,
  );
}

function percentThenAlphabeticalSort(a: { x: string; y: number }, b: { x: string; y: number }): number {
  let compareVal = b.y - a.y;
  if (compareVal === 0) {
    compareVal = a.x.localeCompare(b.x);
  }
  return compareVal;
}

function mapDisplayGroupDataToChartDataArray(proportionsByDisplayGroup: Record<string, number>): {
  x: string;
  y: number;
}[] {
  const sum = Object.keys(proportionsByDisplayGroup).reduce((a, b) => a + proportionsByDisplayGroup[b], 0);
  return Object.keys(proportionsByDisplayGroup).map((groupName) => {
    const roundedGroupY = Math.round(((proportionsByDisplayGroup[groupName] * 100) / sum) * 100) / 100;
    return {
      x: groupName,
      y: roundedGroupY,
    };
  });
}

function sortChartData(data: ChartData[]): ChartData[] {
  return [...data].sort(percentThenAlphabeticalSort);
}

function capArrayLength(data: ChartData[], maxSize = 9): ChartData[] {
  return data.reduce((cappedArray, currentValue, currentIndex) => {
    const accumulator = [...cappedArray];
    if (currentIndex < maxSize) {
      accumulator.push(currentValue);
    } else {
      const other = cappedArray[maxSize - 1];
      accumulator[maxSize - 1] = { x: "Other", y: other.y + currentValue.y };
    }
    return accumulator;
  }, [] as ChartData[]);
}

export function mapVintagesToChart(
  basketComponents: ProjectVintage[],
  groupField: string,
  cap = 9,
): [ChartData[], ChartData[]] {
  const mappedAndSortedData = flow(
    sumCompositionByDisplayGroup,
    mapDisplayGroupDataToChartDataArray,
    sortChartData,
  )(basketComponents, groupField);
  return [capArrayLength(mappedAndSortedData, cap), capArrayLength(mappedAndSortedData, cap)];
}
