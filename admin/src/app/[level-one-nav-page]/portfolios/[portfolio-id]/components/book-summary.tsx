import { Box, Typography, Grid, Tooltip, Stack } from "@mui/material";
import React, { useMemo, useState } from "react";
import { AdminBookResponse, PermissionEnum, BookType } from "@rubiconcarbon/shared-types";
import BasketManagement from "./basket-management";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import BlockIcon from "@mui/icons-material/Block";
import BasketSummaryTotals from "./basket-summary-totals";
import CreateIcon from "@mui/icons-material/Create";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import COLORS from "@components/ui/theme/colors";
import BasketMgtCharts from "./basket-mgt-charts";
import BookRenameModal from "./book-rename-dialog";

const btnStyle = {
  marginLeft: "20px",
  borderColor: COLORS.rubiconGreen,
  textTransform: "none",
  width: "110px",
  color: COLORS.rubiconGreen,
  backgroundColor: COLORS.white,
  fontWeight: 500,
  "&:hover": {
    backgroundColor: COLORS.white,
    boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
  },
};

const blockIconStyle = {
  height: "25px",
  color: COLORS.red,
  textAlign: "left",
  marginLeft: "10px",
};

interface BookSummaryProps {
  book: AdminBookResponse;
  refresh: () => Promise<AdminBookResponse>;
  reloadData: () => Promise<void>;
}

export default function BookSummary(props: BookSummaryProps): JSX.Element {
  const { book, refresh, reloadData } = props;
  const [isBookRenameDialogOpen, setIsBookRenameDialogOpen] = useState<boolean>(false);

  //Summary first row
  const title = useMemo(
    () => (
      <Box sx={{ display: "inline-flex", width: "100%", justifyContent: "space-between" }}>
        <Stack direction="row">
          <Typography
            variant="body2"
            component="h4"
            fontWeight="700"
            sx={{
              fontSize: "32px",
              fontWeight: "500",
              color: COLORS.pureBlack,
              display: "inline-flex",
            }}
          >
            {book?.name} Portfolio
            {!book?.isEnabled && (
              <Tooltip title="the book is not active" enterDelay={750} placement="top">
                <BlockIcon sx={blockIconStyle} />
              </Tooltip>
            )}
            <Maybe condition={book.type === BookType.PORTFOLIO_CUSTOM}>
              <ActionButton
                onClickHandler={() => setIsBookRenameDialogOpen(true)}
                startIcon={<CreateIcon />}
                style={btnStyle}
                requiredPermission={PermissionEnum.BOOKS_UPDATE}
              >
                Rename
              </ActionButton>
            </Maybe>
          </Typography>
        </Stack>
        <Box sx={{ float: "right", justifyContent: "flex-end" }}>
          <BasketManagement basketId={book.id} />
        </Box>
      </Box>
    ),
    [book?.name, book?.isEnabled, book.id, book.type],
  );

  const onRenameCompleteHandler = async (): Promise<void> => {
    setIsBookRenameDialogOpen(false);
    await reloadData();
  };

  const onRenameCloseHandler = (): void => {
    setIsBookRenameDialogOpen(false);
  };

  return (
    <Maybe condition={book !== null}>
      <Box
        sx={{
          backgroundColor: COLORS.white,
          borderRadius: "4px",
          display: "flex",
        }}
      >
        <Box
          sx={{
            width: "5px",
            backgroundColor: getPortfolioColor(book.id),
            borderBottomLeftRadius: "4px",
            borderTopLeftRadius: "4px",
          }}
        ></Box>
        <Grid container spacing={1} sx={{ padding: "24px" }}>
          {/*First row - title and buttons */}
          <Grid item sm={12} md={12} lg={12} xl={12}>
            <Box>{title}</Box>
          </Grid>

          <BasketSummaryTotals book={book} refresh={refresh} />

          {/*last row - charts */}
          <Grid item sm={12}>
            <BasketMgtCharts book={book} />
          </Grid>
        </Grid>
      </Box>
      <BookRenameModal
        bookId={book.id}
        initialPortfolioName={book.name}
        isOpen={!!isBookRenameDialogOpen}
        onSave={onRenameCompleteHandler}
        onClose={onRenameCloseHandler}
      />
    </Maybe>
  );
}
