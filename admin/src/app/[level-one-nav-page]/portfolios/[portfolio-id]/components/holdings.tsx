import { Box, Grid } from "@mui/material";
import React, { useState } from "react";
import {
  AdminBookResponse,
  AdminProjectResponse,
  GroupedAllocationWithNestedResponse,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import { getAmountUnallocated } from "@models/project";
import { isNothing } from "@rubiconcarbon/frontend-shared";
import COLORS from "@components/ui/theme/colors";
import GroupStackedBar from "@components/ui/charts/group-stacked-bar";
import usePerformantEffect from "@/hooks/use-performant-effect";

type Source = (string | number)[][];

interface Allocations {
  allocated: number;
  unallocated: number;
}
interface HoldingsProps {
  book: AdminBookResponse;
  availableProjects: AdminProjectResponse[];
}

export default function Holdings(props: HoldingsProps): JSX.Element {
  const { book, availableProjects } = props;
  const [typeAllocations, setTypeAllocations] = useState<(string | number)[][]>([]);
  const [countryAllocations, setCountryAllocations] = useState<(string | number)[][]>([]);
  const [vintagesAllocations, setVintagesAllocations] = useState<(string | number)[][]>([]);

  usePerformantEffect(() => {
    setGeographyAllocationsChart();
    setTypeAllocationsChart();
    setVintagesAllocationsChart();
  }, [book, availableProjects]);

  const setGeographyAllocationsChart = (): void => {
    const map = new Map<string, Allocations>();
    getAllocationsMapByCountry(map, book, "allocated");
    getUnallocatedAmountsByCountry(map, availableProjects);
    const source = buildSource(map);
    setCountryAllocations(source);
  };

  const setTypeAllocationsChart = (): void => {
    const typeMap = new Map<string, Allocations>();
    getAllocationsMapByType(typeMap, book, "allocated");
    getUnallocatedAmountsByType(typeMap, availableProjects);
    const typeSource = buildSource(typeMap);
    setTypeAllocations(typeSource);
  };

  const setVintagesAllocationsChart = (): void => {
    const vintagesMap = new Map<string, Allocations>();
    getAllocationsMapByVintage(vintagesMap, book, "allocated");

    const vintagesSource = buildSource(vintagesMap);
    setVintagesAllocations(vintagesSource);
  };

  // todo : @kofi this can probably use allocationsByProject
  // if it's easier I can also add an ownerAllocationsByCountry it just seemed kind of unnecessary
  const getAllocationsMapByCountry = (
    map: Map<string, Allocations>,
    book: AdminBookResponse,
    valueName: "allocated" | "unallocated",
  ): void => {
    if ((book.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations) {
      (book.ownerAllocations as GroupedAllocationWithNestedResponse).allocations.forEach((allocation) => {
        const mapKey = (allocation?.detailedAsset as TrimmedProjectVintageResponse)?.project?.country?.name;
        if (!isNothing(mapKey)) updateAllocationsMap(map, mapKey!, allocation.amountAllocated, valueName);
      });
    }
  };

  // @kfoi this should be able to use ownerAllocationsByProjectType
  const getAllocationsMapByType = (
    map: Map<string, Allocations>,
    book: AdminBookResponse,
    valueName: "allocated" | "unallocated",
  ): void => {
    // if ((book.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations) {
    //   (book.ownerAllocations as GroupedAllocationWithNestedResponse).allocations.forEach((allocation) => {
    //     const mapKey = (allocation?.detailedAsset as TrimmedProjectVintageResponse)?.project?.projectType?.category;
    //     updateAllocationsMap(map, mapKey, allocation.amountAllocated, valueName);
    //   });
    // }
    if (book.ownerAllocationsByProjectType) {
      book.ownerAllocationsByProjectType.forEach((c) => {
        const mapKey = c.projectType.category;
        updateAllocationsMap(map, mapKey, c.totalAmountAllocated, valueName);
      });
    }
  };

  const getAllocationsMapByVintage = (
    map: Map<string, Allocations>,
    book: AdminBookResponse,
    valueName: "allocated" | "unallocated",
  ): void => {
    if ((book.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations) {
      (book.ownerAllocations as GroupedAllocationWithNestedResponse).allocations.forEach((c) => {
        const mapKey = c.asset?.label;
        if (!isNothing(mapKey)) updateAllocationsMap(map, mapKey!, c.amountAllocated, valueName);
      });
    }
  };

  const getUnallocatedAmountsByCountry = (map: Map<string, Allocations>, projects: AdminProjectResponse[]): void => {
    projects.forEach((p) => {
      const mapKey = p?.country?.name;
      if (!isNothing(mapKey)) updateAllocationsMap(map, mapKey!, getAmountUnallocated(p), "unallocated");
    });
  };

  const getUnallocatedAmountsByType = (map: Map<string, Allocations>, projects: AdminProjectResponse[]): void => {
    projects.forEach((p) => {
      const mapKey = p?.projectType?.category;
      if (!isNothing(mapKey)) updateAllocationsMap(map, mapKey!, getAmountUnallocated(p), "unallocated");
    });
  };

  const updateAllocationsMap = (
    map: Map<string, Allocations>,
    mapKey: string,
    value: number,
    allocationsKey: "allocated" | "unallocated",
  ): void => {
    const allocations: Allocations = {
      allocated: 0,
      unallocated: 0,
    };

    if (!map.has(mapKey)) {
      //New entry
      map.set(mapKey, {
        ...allocations,
        [allocationsKey]: value,
      });
    } else {
      //Existing entry
      const curElement = map.get(mapKey);

      if (curElement)
        map.set(mapKey, {
          ...curElement,
          [allocationsKey]: curElement[allocationsKey] + value,
        });
    }
  };

  const buildSource = (map: Map<string, Allocations>): Source => {
    let source: Source = [];
    if (map.size > 0) {
      const newMap = Array.from(map).sort((a, b) => String(a[0]).localeCompare(b[0]));
      const sortedMap = new Map(newMap);
      source = [["type", "Allocated", "Unallocated"]];
      sortedMap.forEach((value, key) => {
        source.push([key, value.allocated, value.unallocated]);
      });
    }

    return source;
  };

  return (
    <>
      <Grid container spacing={2} mt={2} sx={{ padding: 0 }}>
        <Grid item xs={12} md={12} lg={12} xl={4} mt={1}>
          <Box
            sx={{
              backgroundColor: COLORS.white,
              paddingLeft: "10px",
              borderRadius: "5px",
            }}
          >
            <GroupStackedBar source={vintagesAllocations} legend={["Allocated", "Unallocated"]} title={"Vintage"} />
          </Box>
        </Grid>
        <Grid item xs={12} md={12} lg={12} xl={4} mt={1}>
          <GroupStackedBar source={countryAllocations} legend={["Allocated", "Unallocated"]} title={"Geography"} />
        </Grid>
        <Grid item xs={12} md={12} lg={12} xl={4} mt={1}>
          <GroupStackedBar source={typeAllocations} legend={["Allocated", "Unallocated"]} title={"Category"} />
        </Grid>
      </Grid>
    </>
  );
}
