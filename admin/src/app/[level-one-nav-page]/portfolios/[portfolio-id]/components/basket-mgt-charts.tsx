import { Box } from "@mui/material";
import React, { useState, useEffect, useMemo } from "react";
import { AdminBookResponse, BookType } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import COLORS from "@components/ui/theme/colors";
import StackedBar, { ProgressBarChartData } from "@components/ui/charts/stacked-bar";

interface BasketMgtChartsProps {
  book: AdminBookResponse;
}
export default function BasketMgtCharts(props: BasketMgtChartsProps): JSX.Element {
  const { book } = props;
  const [chartsCollection, setChartsCollection] = useState<ProgressBarChartData[]>([]);

  const bookGeneratedRcts = book?.assetAllocations;
  const customerHeldRcts = book?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_CUSTOMER);

  const totalBarValue = bookGeneratedRcts?.totalAmountAllocated ?? 0;

  // @kofi why does this need to be useMemo?
  const pendingPurchasePercent = useMemo(
    () => ((bookGeneratedRcts?.totalAmountPendingPurchase ?? 0) * 100) / totalBarValue,
    [bookGeneratedRcts?.totalAmountPendingPurchase, totalBarValue],
  );

  useEffect(() => {
    if (book) {
      setChartsCollection([
        {
          name: "available to sell",
          value: bookGeneratedRcts?.totalAmountAvailable ?? 0, // todo : @kofi to check
          title: "Available to sell",
          emptyValuesPlaceholder: false,
          style: {
            barColor: COLORS.chartOrange,
            title: {
              fontSize: "14px",
              padding: [0, 0, 70, 0],
              align: "left",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, 50, 0],
              align: "left",
            },
          },
        },
        {
          name: "pending purchase",
          value: bookGeneratedRcts?.totalAmountPendingPurchase ?? 0,
          title: "Pending \npurchase",
          emptyValuesPlaceholder: false,
          style: {
            barColor: COLORS.chartPaleGreen,
            title: {
              fontSize: "14px",
              padding: [10, 0, -75, 0],
              align: "left",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, -14, 0],
              align: "left",
            },
          },
        },
        {
          name: "customer holding",
          value: customerHeldRcts?.totalAmountAllocated ?? undefined, // todo : @kofi to check that the right relations are requested
          title: "Customer holding",
          emptyValuesPlaceholder: false,
          labelPosition: "right",
          style: {
            barColor: COLORS.chartsPurple,
            title: {
              fontSize: "14px",
              padding: [0, 0, 30, 0],
              align: "right",
            },
            value: {
              fontSize: "14px",
              padding: pendingPurchasePercent < 1 ? [0, -12, -25, 0] : [0, 0, -25, 0],
              align: "right",
            },
          },
        },
      ]);
    }
  }, [book, bookGeneratedRcts, customerHeldRcts, pendingPurchasePercent]);

  return (
    <Box sx={{ minWidth: "200px" }}>
      <Maybe condition={!!chartsCollection}>
        <StackedBar chartsArray={chartsCollection} />
      </Maybe>
    </Box>
  );
}
