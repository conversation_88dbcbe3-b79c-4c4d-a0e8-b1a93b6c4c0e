import React, { useState, useContext, useCallback, useMemo, FormEvent } from "react";
import { Box, TextField, Dialog, DialogContent, DialogActions, DialogTitle, Typography, Button } from "@mui/material";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AdminBookResponse, uuid } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { BaseDialogProps } from "@models/dialogs";
import usePerformantEffect from "@/hooks/use-performant-effect";
import ActionButton from "@components/ui/action-button/action-button-enhanced";

interface IFormField {
  error?: boolean;
  message?: string;
  value?: string;
}

interface BookRenameProps extends BaseDialogProps {
  bookId: uuid;
  initialPortfolioName: string;
  onSave: () => void;
}

export default function BookRenameModal({
  isOpen,
  onClose,
  onSave,
  bookId,
  initialPortfolioName,
}: BookRenameProps): JSX.Element | null {
  const [isShowConfirmation, setIsShowConfirmation] = useState<boolean>(false);
  const [portfolioName, setPortfolioName] = useState<IFormField>({
    value: "",
    message: "",
    error: false,
  });

  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { api } = useContext(AxiosContext);
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);

  usePerformantEffect(() => {
    setPortfolioName({
      ...portfolioName,
      value: initialPortfolioName,
    });
  }, [initialPortfolioName]);

  const isValidPortfolioName = useCallback(() => {
    if (!portfolioName || portfolioName.value === "") {
      setPortfolioName({
        ...portfolioName,
        error: true,
        message: "portfolio name can't be empty",
      });
      return false;
    }

    setPortfolioName({
      ...portfolioName,
      error: false,
      message: "",
    });

    return true;
  }, [portfolioName]);

  const onSaveHandler = useCallback(async () => {
    if (isValidPortfolioName()) {
      setIsShowConfirmation(true);
    }
  }, [isValidPortfolioName]);

  const onConfirmSaveHandler = useCallback(async () => {
    const payload = {
      name: portfolioName.value,
    };

    try {
      setSubmissionInFlight(true);
      await api.patch<AdminBookResponse>(`admin/books/${bookId}`, payload);
      enqueueSuccess("Successfully renamed portfolio");
      setPortfolioName({
        value: "",
        message: "",
        error: false,
      });
      onSave();
      setIsShowConfirmation(false);
    } catch (error: any) {
      if (error?.response?.data?.message) {
        setIsShowConfirmation(false);
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to rename portfolio");
    } finally {
      setSubmissionInFlight(false);
    }
  }, [portfolioName, api, bookId, enqueueError, enqueueSuccess, onSave]);

  const onCloseHandler = useCallback((): void => {
    onClose();
    setPortfolioName({
      value: initialPortfolioName,
      message: "",
      error: false,
    });
    setIsShowConfirmation(false);
  }, [initialPortfolioName, onClose]);

  const portfolioNameHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newName = {
        ...portfolioName,
        value: event.target.value,
      };
      setPortfolioName(newName);
    },
    [portfolioName],
  );

  const dialogActions = useMemo(
    () => (
      <>
        <Button variant="text" onClick={onCloseHandler} sx={{ fontWeight: 600, textTransform: "capitalize" }}>
          Cancel
        </Button>
        <ActionButton
          style={{ width: isShowConfirmation ? `150px` : `100px` }}
          onClickHandler={isShowConfirmation ? onConfirmSaveHandler : onSaveHandler}
          isDisabled={initialPortfolioName === portfolioName?.value}
        >
          {isShowConfirmation ? `Yes, Proceed` : `Save`}
        </ActionButton>
      </>
    ),
    [
      isShowConfirmation,
      initialPortfolioName,
      portfolioName?.value,
      onCloseHandler,
      onSaveHandler,
      onConfirmSaveHandler,
    ],
  );

  const submitHandler = useCallback((e: FormEvent): void => {
    e.preventDefault();
  }, []);

  return (
    <Dialog open={isOpen} onClose={onCloseHandler} fullWidth>
      <DialogTitle
        sx={{
          color: "#383838",
          fontWeight: 600,
          backgroundColor: "#FDFFFC",
        }}
      >
        {isShowConfirmation ? `Please Confirm` : `Update Portfolio Name`}
      </DialogTitle>
      <DialogContent sx={{ height: "110px", overflowY: "hidden" }}>
        <Maybe condition={!isShowConfirmation}>
          <Box sx={{ marginTop: -3 }}>
            <form id="rename-portfolio-form" onSubmit={submitHandler}>
              <fieldset style={{ display: "contents" }} disabled={submissionInFlight}>
                <TextField
                  id="portfolioName"
                  label="Portfolio name"
                  value={portfolioName.value}
                  helperText={portfolioName.message}
                  error={portfolioName.error}
                  onChange={portfolioNameHandler}
                  required
                  inputProps={{ maxLength: 256 }}
                  sx={{ width: "100%" }}
                />
              </fieldset>
            </form>
          </Box>
        </Maybe>
        <Maybe condition={isShowConfirmation}>
          <Typography variant="body1" component="p" sx={{ fontWeight: 500, marginTop: -1 }}>
            You are about to change the portfolio name to <b>{portfolioName.value}</b>.
          </Typography>
        </Maybe>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "space-between",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
