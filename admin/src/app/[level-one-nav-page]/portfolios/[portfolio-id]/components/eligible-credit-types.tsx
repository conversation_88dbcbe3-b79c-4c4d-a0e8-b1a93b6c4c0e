import { Box, Typography } from "@mui/material";
import React, { useContext, useState } from "react";
import { AdminBookResponse, ProjectTypeResponse } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import EditCreditTypesDialog from "./edit-credit-types-dialog";
import ChipItem from "@components/ui/chip-item/chip-item";
import CustomButton from "@components/ui/custom-button/custom-button";
import COLORS from "@components/ui/theme/colors";

const maxTypesToDisplay = 5;
const maxTypeLength = 15;

interface EligableCreditTypesProps {
  book: AdminBookResponse;
  refresh: () => Promise<AdminBookResponse>;
}

export default function EligibleCreditTypes(props: EligableCreditTypesProps): JSX.Element {
  const { book, refresh } = props;
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [isEditBasketProjectTypesOpen, setIsEditBasketProjectTypesOpen] = useState<boolean>(false);

  const showAvailableTypes = (): JSX.Element[] => {
    const availableTypes: JSX.Element[] = [];
    if (!!book && !!book?.ownerAllocationsByProjectType && book?.ownerAllocationsByProjectType.length > 0) {
      for (let i = 0; i < book?.ownerAllocationsByProjectType.length && i < maxTypesToDisplay; i++) {
        availableTypes.push(
          <Box key={book.ownerAllocationsByProjectType[i].projectType.id} sx={{ paddingLeft: "10px" }}>
            <ChipItem
              label={book.ownerAllocationsByProjectType[i].projectType.type}
              maxLength={book.ownerAllocationsByProjectType.length > maxTypesToDisplay ? maxTypeLength : undefined}
              onDeleteHandler={() => onDeleteHandler(book.ownerAllocationsByProjectType?.[i].projectType[i].id)}
            />
          </Box>,
        );
      }

      if (book?.ownerAllocationsByProjectType.length > maxTypesToDisplay) {
        availableTypes.push(
          <Box key="moreTypes" sx={{ paddingLeft: "10px" }}>
            ...
          </Box>,
        );
      }
    }

    return availableTypes;
  };

  const submitDeleteTypeRequest = (allowedProjectTypes: number[]): void => {
    const payload = {
      projectTypeIds: allowedProjectTypes,
    };

    api
      .patch<ProjectTypeResponse>(`admin/books/${book.id}`, payload)
      .then(() => {
        enqueueSuccess("Successfully removed selected credit type");
        refresh();
      })
      .catch((e) => {
        enqueueError(`Sorry, we are unable to complete your request. ${e?.response?.data?.message}`);
        console.error(e);
      });
  };

  const onDeleteHandler = (typeId?: number): void => {
    if (!typeId) return;

    const updatedTypes = book?.ownerAllocationsByProjectType
      ?.filter((allocation) => allocation.projectType.id !== typeId)
      ?.map((m) => m.projectType.id);
    submitDeleteTypeRequest(updatedTypes ?? []);
  };

  return (
    <Box
      sx={{
        display: "flex",
      }}
    >
      <Box>
        <Box mt={2} sx={{ display: "flex" }}>
          <Box sx={{ paddingTop: "8px" }}>
            <Typography
              variant="body2"
              component="h4"
              sx={{
                fontWeight: "500",
              }}
            >
              Eligible credit types:
            </Typography>
          </Box>
          {showAvailableTypes()}
          <CustomButton
            onClickHandler={() => setIsEditBasketProjectTypesOpen(true)}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.whiteGrey,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginLeft: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
            }}
          >
            ADD/REMOVE
          </CustomButton>
        </Box>
      </Box>
      <EditCreditTypesDialog
        bookId={book.id}
        open={isEditBasketProjectTypesOpen}
        onClose={() => setIsEditBasketProjectTypesOpen(false)}
        refreshBook={refresh}
      />
    </Box>
  );
}
