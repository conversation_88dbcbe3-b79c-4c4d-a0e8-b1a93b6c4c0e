"use client";

import Page from "@components/layout/containers/page";
import PortfolioDetails from "./portfolio-details";
import { AdminBookResponse, AdminBookQueryResponse, TransactionQueryResponse } from "@rubiconcarbon/shared-types";

const PortfolioDetail = ({
  portfolio,
  booksResponse,
  completedTransactionsResponse,
}: {
  portfolio: AdminBookResponse;
  booksResponse: AdminBookQueryResponse;
  completedTransactionsResponse: TransactionQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <PortfolioDetails
        portfolio={portfolio}
        booksResponse={booksResponse}
        completedTransactionsResponse={completedTransactionsResponse}
      />
    </Page>
  );
};

export default PortfolioDetail;
