import {
  BookAction,
  BookType,
  ForwardLineItemResponse,
  ForwardResponse,
  ForwardStatus,
  ForwardType,
  TrimmedBookResponse,
  TrimmedOrganizationResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { IsDate, IsNotEmpty, IsPositive, MaxDate, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { BetweenFields, MaxOfField, MinOfField } from "../utilities/number-validators";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { IsNotEmptyString } from "../utilities/string-validators";
import { PrimitiveTypeProject } from "@models/primitive-type-project";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

const maxDate = new Date();
maxDate.setHours(23, 59, 0, 0);

class ForwardBook implements TrimmedBookResponse {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid = undefined;

  allowedActions: BookAction[];

  description?: string;

  isEnabled: boolean;

  name: string;

  organization: TrimmedOrganizationResponse;

  type: BookType;

  createdAt: Date;

  updatedAt?: Date;
}

export class ForwardLineItem
  implements
    Omit<
      ForwardLineItemResponse,
      | "expectedAmount"
      | "lastUpdatedDeliveryDate"
      | "maxAmount"
      | "minAmount"
      | "rawPrice"
      | "serviceFee"
      | "settledAmount"
      | "otherFee"
      | "originalDeliveryDate"
      | "settledAt"
      | "createdAt"
      | "updatedAt"
      | "projectVintage" //TODO:: Fix build error
    >
{
  constructor() {}

  id: uuid = undefined;

  _id: string = undefined; // for auto-generating render ids

  uiKey: string = undefined;

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => ForwardBook)
  book?: ForwardBook = undefined;

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @BetweenFields<ForwardLineItem>("minAmount", "maxAmount", {
    message: "Please enter a quantity between Min and Max",
  })
  @IsNotEmpty({ message: "Required" })
  expectedAmount: string = undefined; // number

  @ValidateIf((o: GenericTableRowModel<ForwardLineItem>) => !o?.creating && !o.stagedForDelivery)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  lastUpdatedDeliveryDate: string = undefined; // date;

  lineItemKey: string;

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @MinOfField<ForwardLineItem>("minAmount", { message: "Max should be more than Min quantity" })
  @IsNotEmpty({ message: "Required" })
  maxAmount: string = undefined; // number

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @MaxOfField<ForwardLineItem>("maxAmount", { message: "Min should not exceed Max quantity" })
  @IsNotEmpty({ message: "Required" })
  minAmount: string = undefined; // number

  @ValidateIf((o: GenericTableRowModel<ForwardLineItem>) => o?.creating)
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  originalDeliveryDate: string = undefined; // date

  @Type(() => Number)
  @ValidateIf((o) => !o.price)
  @IsPositive({ message: "Please enter a positive value" })
  @IsNotEmpty({ message: "Required if Sub Total is empty" })
  unitPrice: string = undefined; // decimal

  @Type(() => Number)
  @ValidateIf((o) => !o.unitPrice)
  @IsPositive({ message: "Please enter a positive value" })
  @IsNotEmpty({ message: "Required if Price is empty" })
  rawPrice: string = undefined; // decimal

  @ValidateNested()
  @Type(() => PrimitiveTypeVintage)
  projectVintage: PrimitiveTypeVintage = undefined;

  otherFee?: string = undefined; // decimal

  serviceFee?: string = undefined; // decimal

  feeTotal?: string = undefined; // decimal

  grandTotal?: string = undefined; // decimal

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @BetweenFields<ForwardLineItem>("minAmount", "maxAmount", {
    message: "Please enter a quantity between Min and Max",
  })
  @IsNotEmpty({ message: "Required" })
  settledAmount?: string = undefined; // number

  status: ForwardStatus = ForwardStatus.PENDING;

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @MaxDate(maxDate, { message: "Date cannot be in the future" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  settledAt: string = undefined; // date

  stagedForDelivery: boolean = undefined;

  bookBreached: boolean = undefined;

  availableAmount: number = undefined;

  docsCount: number;
}

export class ForwardModel
  implements Omit<ForwardResponse, "amount" | "lineItems" | "createdAt" | "updatedAt" | "project">
{
  constructor() {}

  id: uuid = undefined;

  uiKey: string = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeProject)
  project: PrimitiveTypeProject = undefined;

  @IsNotEmptyString({ message: "Cannot be an empty string" })
  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o) => o.type === undefined || o.type === ForwardType.BUY)
  counterparty: string = undefined;

  @ValidateIf((o) => o.type === "sell")
  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => ForwardBook)
  customerPortfolio: ForwardBook = undefined;

  documents: string = undefined;

  amount: string = undefined; // number

  status: ForwardStatus = ForwardStatus.PENDING; // by default, it should pending

  lineItems: ForwardLineItem[] = [];

  @IsNotEmpty({ message: "Required" })
  type: ForwardType = undefined;

  createdAt: string = undefined; // date

  updatedAt: string = undefined; // date
}

export class ForwardFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => ForwardModel)
  amends: ForwardModel[];
}

export class MultiForwardLineItemFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => ForwardLineItem)
  amends: ForwardLineItem[];
}
