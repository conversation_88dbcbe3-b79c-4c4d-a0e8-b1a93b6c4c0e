import { AuthorizeServer } from "@app/authorize-server";
import {
  ForwardQueryResponse,
  PermissionEnum,
  BookRelations,
  BookType,
  AdminBookQueryResponse,
  GroupingParentQueryResponse,
} from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement } from "react";
import ForwardDeliveryPage from "./components";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";

/**
 * Forward Delivery Page
 *
 * This is a server component that renders the Forward Delivery page
 */
export default async function ForwardDelivery({
  searchParams,
}: {
  searchParams: Promise<{ type?: "buy" | "sell"; search?: string }>;
}): Promise<JSX.Element> {
  const { type, search } = await searchParams;

  const forwardsResponse = await withErrorHandling(async () =>
    baseApiRequest<ForwardQueryResponse>(
      `admin/forwards?${generateQueryParams({
        includeTotalCount: true,
        limit: SERVER_PAGINATION_LIMIT,
        ...(type ? { type } : {}),
      })}`,
    ),
  );

  const customerPortfoliosResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: false,
        includeRelations: [BookRelations.ORGANIZATION],
        types: [BookType.PORTFOLIO_CUSTOMER],
      })}`,
    ),
  );

  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<GroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if any of the results is a server error
  if (isValidElement(forwardsResponse)) return forwardsResponse;
  if (isValidElement(customerPortfoliosResponse)) return customerPortfoliosResponse;
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.FORWARDS_READ]}>
      <ForwardDeliveryPage
        forwardsResponse={forwardsResponse as ForwardQueryResponse}
        customerPortfoliosResponse={customerPortfoliosResponse as AdminBookQueryResponse}
        booksByParentResponse={booksByParentResponse as GroupingParentQueryResponse}
        type={type as "buy" | "sell"}
        search={search}
      />
    </AuthorizeServer>
  );
}
