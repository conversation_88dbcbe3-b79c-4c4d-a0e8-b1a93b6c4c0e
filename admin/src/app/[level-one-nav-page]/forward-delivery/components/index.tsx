"use client";

import Page from "@components/layout/containers/page";
import ForwardDeliveryComponent from "./forward-delivery";
import { ForwardQueryResponse, AdminBookQueryResponse, GroupingParentQueryResponse } from "@rubiconcarbon/shared-types";

const ForwardDeliveryPage = ({
  forwardsResponse,
  customerPortfoliosResponse,
  booksByParentResponse,
  type,
  search,
}: {
  forwardsResponse: ForwardQueryResponse;
  customerPortfoliosResponse: AdminBookQueryResponse;
  booksByParentResponse: GroupingParentQueryResponse;
  type?: "buy" | "sell";
  search?: string;
}): JSX.Element => {
  return (
    <Page>
      <ForwardDeliveryComponent
        forwardsResponse={forwardsResponse}
        customerPortfoliosResponse={customerPortfoliosResponse}
        booksByParentResponse={booksByParentResponse}
        type={type}
        search={search}
      />
    </Page>
  );
};

export default ForwardDeliveryPage;
