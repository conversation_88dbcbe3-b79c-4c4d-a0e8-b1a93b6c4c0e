import { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import usePerformantEffect from "@/hooks/use-performant-effect";
import {
  Stack,
  Typography,
  TextField,
  MenuItem,
  Alert,
  Collapse,
  Divider,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  Box,
} from "@mui/material";
import { calculator, currencyFormat, Maybe, numberFormat, toDecimal } from "@rubiconcarbon/frontend-shared";
import { AllocationResponse, BookType, AdminProjectVintageResponse, uuid } from "@rubiconcarbon/shared-types";
import dayjs from "dayjs";
import { ChangeEvent, useCallback, useEffect, useMemo } from "react";
import { useForm, Path, SetValueConfig, Controller } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useMount, useToggle, useUnmount } from "react-use";
import { ForwardLineItem } from "../models/forward";
import { HandleLineItemSubmit } from "../types/forward-line-item";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { DatePicker } from "@mui/x-date-pickers";
import { bookIsBreached } from "@utils/helpers/portfolio/book-is-breached";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { deepEqual } from "fast-equals";
import { projectIsRCTApproved } from "@utils/helpers/project/project-is-rct-approved";

import classes from "../styles/deliver-form.module.scss";

const SingleForwardLineItemFormResolver = classValidatorResolver(ForwardLineItem);

type DeliverFormProps = {
  isBuy: boolean;
  loadingAllocations: boolean;
  row: ForwardLineItem;
  books: PrimitiveTypeBook[];
  bookOptions: UseAutoCompleteOptionsReturnEntry<uuid>[];
  bookToVintages: Record<uuid, Record<uuid, Omit<AllocationResponse, "owner">>>;
  bindOnSubmitLineItem: (handleLineItemSubmit: () => HandleLineItemSubmit) => void;
  onFormValidationChange: (valid: boolean) => void;
  settleLineItem: (formData: ForwardLineItem) => Promise<void>;
};

const DeliverForm = ({
  isBuy,
  loadingAllocations,
  row,
  books,
  bookOptions,
  bookToVintages,
  bindOnSubmitLineItem,
  onFormValidationChange,
  settleLineItem,
}: DeliverFormProps): JSX.Element => {
  const [bookLimitApproved, setBookLimitApproved] = useToggle(false);

  const { control, formState, getValues, setValue, watch, handleSubmit } = useForm({
    mode: "onChange",
    resolver: SingleForwardLineItemFormResolver,
    defaultValues: row,
  });

  const { isValid, errors } = formState;

  const setValueIfNeeded = useCallback(
    (field: Path<ForwardLineItem>, value: any, option: SetValueConfig = {}): void => {
      const current = getValues(field as any);
      if (!deepEqual(current, value)) setValue(field as any, value, option);
    },
    [getValues, setValue],
  );

  watch();

  const selectedBook = watch("book");

  const availableAmount = watch("availableAmount");

  const watchForFeeTotalEffect = watch(["serviceFee", "otherFee"]);

  const watchForGrandTotalEffect = watch(["unitPrice", "settledAmount", "serviceFee", "otherFee"]);

  const disableBookSelection = useMemo(
    () => !isBuy && (loadingAllocations || (!loadingAllocations && !bookOptions?.length)),
    [bookOptions?.length, isBuy, loadingAllocations],
  );

  const bookBreached = useMemo(
    () =>
      bookIsBreached(
        books?.find(({ id }) => selectedBook?.id == id),
        calculator(watchForGrandTotalEffect[0]).multiply(watchForGrandTotalEffect[1]).calculate(),
      ),
    [books, selectedBook?.id, watchForGrandTotalEffect],
  );

  const isRCTApproved = useMemo(
    () =>
      isBuy
        ? selectedBook?.type === BookType.PORTFOLIO_DEFAULT
          ? projectIsRCTApproved(row?.projectVintage as unknown as AdminProjectVintageResponse)
          : true
        : true,
    [row?.projectVintage, selectedBook?.type, isBuy],
  );

  usePerformantEffect(
    () => onFormValidationChange(isValid && isRCTApproved && (bookBreached && isBuy ? bookLimitApproved : true)),
    [isRCTApproved, isValid, bookBreached, bookLimitApproved, isBuy],
  );

  useEffect(() => {
    const [serviceFee, otherFee] = watchForFeeTotalEffect;
    setValueIfNeeded("feeTotal", calculator(serviceFee).add(otherFee).calculate().toString());
  }, [setValueIfNeeded, watchForFeeTotalEffect]);

  useEffect(() => {
    const [price, settledAmount, serviceFee, otherFee] = watchForGrandTotalEffect;
    setValueIfNeeded(
      "grandTotal",
      calculator(calculator(price).multiply(settledAmount).calculate())
        .add(isBuy ? serviceFee : -serviceFee)
        .add(isBuy ? otherFee : -otherFee)
        .calculate()
        .toString(),
    );
  }, [setValueIfNeeded, watchForGrandTotalEffect, isBuy]);

  useEffect(() => {
    if (!isBuy && selectedBook?.id) {
      const { amountAvailable } = bookToVintages?.[selectedBook?.id]?.[row?.projectVintage?.id] || {};

      setValueIfNeeded("availableAmount", amountAvailable);
    } else setValueIfNeeded("availableAmount", undefined);
  }, [bookToVintages, isBuy, row?.projectVintage?.id, selectedBook, setValueIfNeeded]);

  useEffect(() => {
    const subscription = watch(({ unitPrice, settledAmount, rawPrice, book }, { name, type }) => {
      if (type === "change") {
        const amount = toDecimal(settledAmount, { treatNothingAsNaN: true });

        switch (name) {
          case "book.id":
            setValue("book", books?.find(({ id }) => book?.id == id) as any);
            break;
          case "unitPrice":
            if (!amount.isNaN() && !amount.isZero())
              setValue("rawPrice", calculator(unitPrice).multiply(settledAmount).calculate().toString(), {
                shouldValidate: true,
              });
            else setValue("rawPrice", "0", { shouldValidate: true });
            break;
          case "settledAmount":
            if (!amount.isNaN() && !amount.isZero())
              setValue("rawPrice", calculator(unitPrice).multiply(amount).calculate().toString(), {
                shouldValidate: true,
              });
            else setValue("rawPrice", "0", { shouldValidate: true });
            break;
          case "rawPrice":
            if (!amount.isNaN() && !amount.isZero())
              setValue("unitPrice", calculator(rawPrice).divide(settledAmount).calculate().toString(), {
                shouldValidate: true,
              });
            break;
          default:
            break;
        }
      }
    });
    return (): void => subscription.unsubscribe();
  }, [books, setValue, watch]);

  const onSubmit = async (formData: ForwardLineItem): Promise<void> => await settleLineItem(formData);

  useMount(() => {
    bindOnSubmitLineItem(handleSubmit(onSubmit) as any);
  });

  useUnmount(() => {
    bindOnSubmitLineItem(null);
  });

  return (
    <Stack className={classes.DeliverForm} gap={3}>
      <Maybe condition={isBuy}>
        <Collapse in={bookBreached} unmountOnExit>
          <Stack gap={3}>
            <Alert severity="error" variant="standard" className={classes.NonApproval}>
              <Typography variant="body2" fontWeight={100}>
                This line item exceeds the allowed limit for the {selectedBook?.name} book of{" "}
                {currencyFormat(books?.find(({ id }) => selectedBook?.id == (id as uuid))?.limit?.holdingPriceMax)}
              </Typography>
              <FormControl>
                <FormControlLabel
                  label={<Typography variant="body2">I have obtained trade approval</Typography>}
                  control={
                    <Checkbox
                      classes={{
                        root: classes.CheckBox,
                      }}
                      size="small"
                      checked={bookLimitApproved}
                      onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) => setBookLimitApproved(checked)}
                    />
                  }
                />
              </FormControl>
            </Alert>
            <Divider />
          </Stack>
        </Collapse>
      </Maybe>
      <Maybe condition={isBuy}>
        <Collapse in={!isRCTApproved} unmountOnExit>
          <Stack gap={3}>
            <Alert severity="error" variant="standard" className={classes.NonApproval}>
              <Typography>Cannot process delivery.</Typography>
              <Typography variant="body2" fontWeight={100}>
                The line item doesn&apos;t pass the minimum requirement of the RCT book.
              </Typography>
            </Alert>
            <Divider />
          </Stack>
        </Collapse>
      </Maybe>

      <Typography fontWeight={300}>Please confirm the details of the delivery</Typography>
      <Grid container>
        <Grid item xs={4}>
          <Stack gap={0.5}>
            <Typography variant="subtitle2" fontWeight="100">
              Project Vintage
            </Typography>
            <Stack>
              <Typography variant="body2">{row?.projectVintage?.project?.name}</Typography>
              <Typography variant="body2" color="GrayText">
                {row?.projectVintage?.project?.registryProjectId} - {row?.projectVintage?.name}
              </Typography>
            </Stack>
          </Stack>
        </Grid>
      </Grid>
      <Stack direction="row" gap={2}>
        <Controller
          name={"book.id"}
          control={control}
          disabled={disableBookSelection}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              select
              label="Book"
              value={value ?? ""}
              InputProps={{ ref }}
              error={!!errors?.book?.id}
              helperText={
                <>
                  <Maybe condition={!errors?.book && !isBuy}>
                    <Maybe condition={loadingAllocations}>
                      <Box component="span">Loading...</Box>
                    </Maybe>
                    <Maybe condition={!loadingAllocations && !bookOptions?.length}>
                      <Box component="span" color="#C90005">
                        Insufficient inventory to fulfill the order
                      </Box>
                    </Maybe>
                  </Maybe>
                  <Maybe condition={!!errors?.book}>
                    <Stack>
                      <Maybe condition={!isBuy && !bookOptions?.length}>
                        Insufficient inventory to fulfill the order
                      </Maybe>
                      {errors?.book?.id?.message}
                    </Stack>
                  </Maybe>
                </>
              }
              {...otherProps}
              disabled={disableBookSelection}
              fullWidth
            >
              {bookOptions?.map((option) => (
                <MenuItem key={option.value} value={option.value} disabled={option.disabled}>
                  {option?.displayLabel || option.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />
      </Stack>
      <Stack direction="row" gap={2}>
        <Controller
          control={control}
          name={"unitPrice"}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label="Price"
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
              }}
              {...otherProps}
              error={!!errors?.unitPrice}
              helperText={errors?.unitPrice?.message}
              fullWidth
            />
          )}
        />
        <Controller
          control={control}
          name={"settledAmount"}
          render={({ field: { ref, value, onChange, ...otherProps }, formState: { errors } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              label="Quantity"
              value={value}
              isAllowed={({ floatValue }) => !floatValue || isBuy || floatValue <= availableAmount}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
              }}
              error={!!errors?.settledAmount}
              helperText={
                !errors?.settledAmount && !isBuy && !!selectedBook?.id ? (
                  <span>Available: {numberFormat(availableAmount)}</span>
                ) : (
                  <Stack component="span">
                    <Maybe condition={!isBuy && !!selectedBook?.id}>
                      <span>Available: {numberFormat(availableAmount)}</span>
                    </Maybe>
                    <span>{errors?.settledAmount?.message}</span>
                  </Stack>
                )
              }
              {...otherProps}
              disabled={!isBuy && !selectedBook?.id}
              fullWidth
            />
          )}
        />
        <Controller
          control={control}
          name={"rawPrice"}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label={isBuy ? "Sub Total" : "Gross Revenue"}
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
              }}
              {...otherProps}
              error={!!errors?.rawPrice}
              helperText={errors?.rawPrice?.message}
              fullWidth
            />
          )}
        />
      </Stack>
      <Stack direction="row" gap={2}>
        <Controller
          control={control}
          name={"serviceFee"}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label="Service Fees"
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
              }}
              {...otherProps}
              error={!!errors?.serviceFee}
              helperText={errors?.serviceFee?.message}
              fullWidth
            />
          )}
        />
        <Controller
          control={control}
          name={"otherFee"}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label="Other Fees"
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
              }}
              {...otherProps}
              error={!!errors?.otherFee}
              helperText={errors?.otherFee?.message}
              fullWidth
            />
          )}
        />
        <Controller
          control={control}
          name={"feeTotal"}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={false}
              thousandSeparator
              prefix="$"
              label="Fee Total"
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              onValueChange={({ floatValue }) => onChange(floatValue)}
              customInput={TextField}
              InputProps={{
                ref,
                readOnly: true,
              }}
              {...otherProps}
              error={!!errors?.feeTotal}
              helperText={errors?.feeTotal?.message}
              disabled
              fullWidth
              readOnly
            />
          )}
        />
      </Stack>
      <Stack direction="row" gap={2}>
        <Controller
          control={control}
          name={"grandTotal"}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <NumericFormat
              allowNegative={true}
              thousandSeparator
              prefix="$"
              label={isBuy ? "Grand Total" : "Net Revenue"}
              decimalScale={2}
              fixedDecimalScale={true}
              value={value}
              customInput={TextField}
              InputProps={{
                ref,
                readOnly: true,
              }}
              {...otherProps}
              error={!!errors?.grandTotal}
              helperText={errors?.grandTotal?.message}
              disabled
              fullWidth
              readOnly
            />
          )}
        />
      </Stack>
      <Stack>
        <Controller
          control={control}
          name="settledAt"
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            return (
              <DatePicker
                format="MM/DD/YYYY"
                value={value ? dayjs(value as any) : null}
                disableFuture
                onChange={(newValue) => onChange(newValue ? newValue.toDate() : null)}
                slotProps={{
                  textField: {
                    label: "Asset Delivered Date",
                    fullWidth: true,
                    InputProps: {
                      inputRef: ref,
                    },
                    error: !!errors?.settledAt,
                    FormHelperTextProps: {
                      component: "div",
                    },
                    helperText: errors?.settledAt?.message,
                    ...otherProps,
                  },
                  popper: {
                    placement: "bottom-end",
                  },
                }}
              />
            );
          }}
        />
      </Stack>
    </Stack>
  );
};

export default DeliverForm;
