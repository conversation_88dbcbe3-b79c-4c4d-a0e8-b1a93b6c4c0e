import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import {
  Box,
  Button,
  capitalize,
  Chip,
  Divider,
  IconButton,
  <PERSON>u,
  MenuItem,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { ForwardStatus, PermissionEnum } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { MouseEvent, useContext, useMemo } from "react";
import useGenericTableRowActions from "@components/ui/generic-table/hooks/use-generic-table-row-actions";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { calculator, Maybe, usePopperState } from "@rubiconcarbon/frontend-shared";
import {
  AddRounded,
  ArrowDropDownRounded,
  CancelRounded,
  CheckRounded,
  DeleteRounded,
  EditRounded,
} from "@mui/icons-material";
import { useAtomValue, useStore } from "jotai";
import { ForwardLineItemExtensions } from "../types/forward-line-item";
import { GenericTableExternal } from "@components/ui/generic-table/types/generic-table-external";
import { MISSING_DATA } from "@constants/constants";
import { AuthContext } from "@providers/auth-provider";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { externalAtom } from "@components/ui/generic-table/state";
import { ForwardLineItem } from "../models/forward";

import actionClasses from "../styles/actions-cell.module.scss";

type RowOnlyProps = {
  row: GenericTableRowModel<ForwardLineItem>;
};

const AddOption = (): JSX.Element => (
  <Box component={Stack} direction="row" justifyContent="space-between" width="100%">
    <Typography variant="body2" textTransform="none" color="primary">
      Add manually
    </Typography>
    <AddRounded color="primary" />
  </Box>
);

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useContext(AuthContext);
  const store = useStore();

  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    ForwardLineItem,
    ForwardLineItemExtensions
  >;
  const { toggleEdit, submitRow, cancelAmendment } = useGenericTableRowActions<ForwardLineItem>();
  const { creating, editing, dirty, submitting, disabled, isRowActive } = useGenericTableRowState<ForwardLineItem>(row);

  const permissions = useMemo(() => user.permissions, [user.permissions]);
  const { readonlyParent, settleLineItem, cancelLineItem } = useMemo(
    () => (typeof extensions === "function" ? extensions(row) : extensions),
    [extensions, row],
  );
  const rowLevelSubmission = useMemo(() => readonlyParent, [readonlyParent]);

  const hasPermissionToSettle = permissions.includes(PermissionEnum.FORWARDS_SETTLE_LINE_ITEMS);
  const hasPermissionToEdit = permissions.includes(PermissionEnum.FORWARDS_UPDATE_LINE_ITEMS);
  const hasPermissionToCancel = permissions.includes(PermissionEnum.FORWARDS_CANCEL_LINE_ITEMS);

  const { popperId, ref: popperRef, popout, close, toggle } = usePopperState<HTMLButtonElement>({ id: "more-actions" });

  return (
    <Stack className={actionClasses.Actions} direction="row" gap={1}>
      <Maybe condition={!isRowActive && row?.status === ForwardStatus.PENDING}>
        <Tooltip title={!hasPermissionToSettle ? "Insufficient permissions" : ""}>
          <Box>
            <Button
              className={actionClasses.Button}
              variant="contained"
              disableElevation
              startIcon={<CheckRounded />}
              disabled={!hasPermissionToSettle || disabled}
              onClick={async (event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();

                close();
                await settleLineItem(row);
              }}
            >
              Approve Delivery
            </Button>
          </Box>
        </Tooltip>
        <>
          <Button
            ref={popperRef}
            className={`${actionClasses.Button} ${actionClasses.GrayTextColor}`}
            color="inherit"
            variant="outlined"
            disableElevation
            disabled={disabled}
            endIcon={<ArrowDropDownRounded className={actionClasses.Icon} fontSize="inherit" />}
            onClick={toggle}
          >
            More Actions
          </Button>
          <Menu
            id={popperId}
            disablePortal
            open={popout}
            anchorEl={popperRef?.current}
            anchorOrigin={{
              vertical: 45,
              horizontal: -30,
            }}
            onClose={close}
          >
            <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : ""}>
              <Box>
                <MenuItem
                  disableRipple
                  disabled={!hasPermissionToEdit}
                  onClick={(event: MouseEvent<HTMLLIElement>) => {
                    event?.preventDefault();
                    event?.stopPropagation();

                    close();
                    toggleEdit(row);
                  }}
                >
                  <Button
                    className={`${actionClasses.Button} ${actionClasses.MenuListButton}`}
                    startIcon={
                      <EditRounded
                        className={`${actionClasses.Icon} ${actionClasses.GrayTextColor}`}
                        fontSize="inherit"
                      />
                    }
                    color="inherit"
                  >
                    Edit Line Item
                  </Button>
                </MenuItem>
              </Box>
            </Tooltip>
            <Tooltip title={!hasPermissionToCancel ? "Insufficient permissions" : ""}>
              <Box>
                <MenuItem disableRipple disabled={!hasPermissionToCancel} onClick={close}>
                  <Button
                    className={`${actionClasses.Button} ${actionClasses.MenuListButton}`}
                    color="error"
                    startIcon={<DeleteRounded className={actionClasses.Icon} fontSize="inherit" />}
                    onClick={async (event: MouseEvent<HTMLButtonElement>) => {
                      event?.preventDefault();
                      event?.stopPropagation();

                      close();
                      await cancelLineItem(row);
                    }}
                  >
                    Cancel Line Item
                  </Button>
                </MenuItem>
              </Box>
            </Tooltip>
          </Menu>
        </>
      </Maybe>
      <Maybe condition={isRowActive && (creating || editing)}>
        <Maybe condition={rowLevelSubmission}>
          <IconButton
            color="primary"
            onClick={(event: MouseEvent<HTMLButtonElement>) => {
              event?.preventDefault();
              event?.stopPropagation();
              submitRow(row);
            }}
            type="button"
            disabled={submitting || (editing && !dirty)}
          >
            <CheckRounded />
          </IconButton>
          <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
        </Maybe>
        <IconButton
          color="error"
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();
            cancelAmendment(row);
          }}
        >
          <CancelRounded />
        </IconButton>
      </Maybe>
    </Stack>
  );
};

export const LINE_ITEM_COLUMNS: GenericTableColumn<ForwardLineItem>[] = [
  {
    field: "projectVintage.id",
    label: "Vintage",
    type: "autocomplete",
    exportable: true,
    editable: false,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    fixedAutoCompleteOptions: {
      options: [
        {
          label: "_add_vintage_",
          displayLabel: <AddOption />,
          value: "_add_vintage_",
        },
        {
          internal: true,
          label: "_divider_",
          displayLabel: <Divider />,
          value: null,
        },
      ],
      renderOptionOn: (option, defaultOptions) => option.label === "_add_vintage_" || !!defaultOptions?.length,
      renderOptionAt: "before",
    },
    deriveDataValue: (row) => row?.projectVintage?.name,
  },
  {
    field: "book.id",
    label: "Book",
    type: "select",
    disable: (row) => !row?.projectVintage?.id,
    width: GenericTableFieldSizeEnum.large,
    deriveDataValue: (row) => row?.book?.name,
  },
  {
    field: "unitPrice",
    label: "Price",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    deriveDataValue: (row) =>
      calculator(row?.rawPrice)
        .divide(row?.status === ForwardStatus.SETTLED ? row?.settledAmount : row?.expectedAmount)
        .calculate()
        .toString(),
  },
  {
    field: "minAmount",
    label: "Min Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "maxAmount",
    label: "Max Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "expectedAmount",
    label: "Expected Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
  },
  {
    field: "settledAmount",
    label: "Settled Quantity",
    type: "number",
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.medium,
  },
  {
    field: "rawPrice",
    label: "Sub Total",
    type: "money",
    width: GenericTableFieldSizeEnum.flexsmall,
  },
  {
    field: "serviceFee",
    label: "Service Fee",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "otherFee",
    label: "Other Fee",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "grandTotal",
    label: "Grand Total",
    type: "money",
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.small,
    deriveDataValue: (row) => calculator(row?.rawPrice).add(row?.serviceFee).add(row?.otherFee).calculate().toString(),
  },
  {
    field: "originalDeliveryDate",
    label: "Orig. Delivery Date",
    type: "date",
    creatable: true,
    editable: false,
    width: GenericTableFieldSizeEnum.medium,
  },
  {
    field: "lastUpdatedDeliveryDate",
    label: "Last Delivery Date",
    type: "date",
    validator: {
      disablePast: true,
    },
    creatable: false,
    editable: true,
    hide: (rows) => rows?.some((row) => row?.creating),
    width: GenericTableFieldSizeEnum.medium,
  },
  {
    field: "settledAt",
    label: "Asset Delivered Date",
    type: "date",
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
  },
  {
    field: "status",
    label: "Status",
    type: "select",
    creatable: false,
    editable: false,
    hide: (rows) => rows?.some((row) => row?.creating),
    transformDataValue: (value: ForwardStatus) => (value ? capitalize(value) : ""),
    renderDataCell: ({ status }): JSX.Element => {
      const [color, background] =
        status === ForwardStatus.SETTLED
          ? ["#077D55", "#EDF7ED"]
          : status === ForwardStatus.CANCELED
            ? ["#D32F2F", "#D32F2F1A"]
            : ["#000000DE", "#00000014"];

      return status ? (
        <Chip label={status ? capitalize(status) : ""} sx={{ color, background }} />
      ) : (
        <>{MISSING_DATA}</>
      );
    },
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    hide: (rows) => rows?.every((row) => [ForwardStatus.CANCELED, ForwardStatus.SETTLED].includes(row?.status)),
    width: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
