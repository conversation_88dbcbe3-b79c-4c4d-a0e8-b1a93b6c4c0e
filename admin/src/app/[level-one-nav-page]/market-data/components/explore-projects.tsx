import { Box, Stack } from "@mui/material";
import { SupersetDashboard } from "@components/superset-dashboard/superset-dashboard";

import classes from "../styles/styles.module.scss";

const ExploreProjects = (): JSX.Element => {
  const DASHBOARD_KEY = "ca3b2f9b-b6f7-453d-b445-1c8e134c6890";

  return (
    <Box className={classes.boxContainer}>
      <Stack direction="row" sx={{ justifyContent: "space-between" }}>
        <Box mt={1} className={classes.exploreTitle}>{`Explore Projects`}</Box>
      </Stack>
      <SupersetDashboard
        key={DASHBOARD_KEY}
        dashboard={DASHBOARD_KEY}
        config={{
          hideTitle: true,
          hideTab: true,
          hideChartControls: false,
          filters: { visible: false, expanded: false },
        }}
        style={{ height: "1150px", marginTop: "10px" }}
      />
    </Box>
  );
};

export default ExploreProjects;
