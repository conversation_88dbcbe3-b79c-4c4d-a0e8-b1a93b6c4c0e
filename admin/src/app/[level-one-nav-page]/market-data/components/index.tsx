"use client";

import Page from "@components/layout/containers/page";
import { Stack } from "@mui/material";
import ExploreProjects from "./explore-projects";
import ExploreRetirements from "./retirement-details";
import MarketDataSection from "./market-data-section";
import MarketIntelligenceMngmt from "@app/market-intelligence/components/reports-list";

const MarketData = (): JSX.Element => {
  return (
    <Page>
      <Stack gap={3}>
        <ExploreProjects />
        <ExploreRetirements />
        <MarketDataSection
          title="Market Insights"
          subTitle="Latest news and updates"
          headerSx={{
            alignItems: "start",
          }}
        >
          <MarketIntelligenceMngmt isExtendedFunc={false} />
        </MarketDataSection>
      </Stack>
    </Page>
  );
};

export default MarketData;
