import React, { useState, useEffect, useContext } from "react";
import {
  AdminBookResponse,
  AdminProjectResponse,
  ProjectVintageRelations,
  AdminProjectVintageResponse,
  RetirementType,
  TransactionResponse,
  UserActionDataResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import FormControl from "@mui/material/FormControl";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { stringComparator } from "@utils/comparators/comparator";
import dateFormatterLocalTime from "@/utils/formatters/local-time-date-formatter";
import { groupBy, max, uniq, orderBy } from "lodash";
import { Autocomplete, Select, SelectChangeEvent, TextField } from "@mui/material";
import { AxiosContext } from "@providers/axios-provider";
import integerFormat from "@/utils/formatters/integer-format";
import { subDays } from "date-fns";
import { generateQueryParams, Maybe, toNumber, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import { UserActivityTableRow, AggregateUserActionType, allowedTypes, allowedTypeByCode } from "../types/general";
import ActionChips from "./action-chips";
import ExpandedRow from "./expanded-row";
import SearchChips from "./search-chips";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { MISSING_DATA } from "@constants/constants";

export default function UserActivityTable({
  since,
  useActionsDataResponse: serverUseActionsDataResponse,
}: {
  since: string;
  useActionsDataResponse: UserActionDataResponse[];
}): JSX.Element {
  const { api } = useContext(AxiosContext);
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const [data, setData] = useState<UserActivityTableRow[]>();
  const [filteredData, setFilteredData] = useState<UserActivityTableRow[]>();
  const [products, setProducts] = useState<Map<uuid, string>>(new Map<uuid, string>());
  const [projects, setProjects] = useState<Map<uuid, string>>(new Map<uuid, string>());
  const [vintages, setVintages] = useState<Map<uuid, AdminProjectVintageResponse>>(
    new Map<uuid, AdminProjectVintageResponse>(),
  );
  const [retirements, setRetirements] = useState<Map<uuid, TransactionResponse>>(new Map<uuid, TransactionResponse>());
  const [period, setPeriod] = useState<number>(toNumber(since));
  const [types, setTypes] = useState<AggregateUserActionType[]>([]);

  const columnsDef: ColDef[] = [
    {
      columnName: "user",
      displayName: "Name",
      comparator: stringComparator,
    },
    {
      columnName: "org",
      displayName: "Organization",
      comparator: stringComparator,
      formatter: {
        func: (x: any): JSX.Element => (
          <>
            <Maybe condition={x?.get("orgId") !== undefined}>
              <CustomerPortfolio
                portfolio={{ name: x?.get("org"), organization: { id: x?.get("orgId") } } as any}
                style={{ fontSize: 14 }}
              />
            </Maybe>
            <Maybe condition={x?.get("orgId") === undefined}>{MISSING_DATA}</Maybe>
          </>
        ),
        inputFields: ["org", "orgId"],
      },
    },

    {
      columnName: "lastAction",
      displayName: "Last Activity",
      formatter: { func: dateFormatterLocalTime },
      exportFormatter: { func: dateFormatterEST },
    },
    {
      columnName: "actions",
      displayName: "Activity Types",
      formatter: {
        func: (x: any): JSX.Element => <ActionChips actions={x as AggregateUserActionType[]} />,
      },
    },
    {
      columnName: "events",
      displayName: "Count",
      formatter: {
        func: (x: any): string => ((x as any[]).length > 0 ? integerFormat((x as any[]).length) : "-"),
      },
    },
  ];

  const { data: userActionsDataResponse, trigger: refreshUserActions } = useTriggerRequest<
    UserActionDataResponse[],
    object,
    { since: string; includeInternal: boolean }
  >({
    url: "admin/user-actions",
    queryParams: {
      since: subDays(new Date(), toNumber(since)).toISOString(),
      includeInternal: process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT !== "prod",
    },
    optimisticData: serverUseActionsDataResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch user actions.");
        logger.error(`Unable to fetch user actions. Error: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    if (userActionsDataResponse && userActionsDataResponse.length > 0) {
      // portfolios
      const bookIds = uniq(
        userActionsDataResponse
          .filter((x) => x.type === "portfolio_viewed")
          .map((x) => (x.data as any)?.productId)
          .filter((x) => x !== undefined),
      );
      if (bookIds?.length) {
        api
          .get(
            `/admin/books?${generateQueryParams({
              ids: bookIds,
            })}`,
          )
          .then((d) => {
            setProducts(new Map(d.data.data.map((x: AdminBookResponse) => [x.id, x.name])));
          });
      }

      // projects
      const projectIds = uniq(
        userActionsDataResponse
          .filter((x) => x.type === "project_viewed")
          .map((x) => (x.data as any)?.projectId)
          .filter((x) => x !== undefined)
          .concat(
            userActionsDataResponse
              .filter((x) => x.type === "rct_quote_request")
              .flatMap((x) => (x.data as any)?.projectIds)
              .filter((x) => x !== undefined),
          ),
      );
      if (projectIds.length) {
        api
          .get(
            `/admin/projects?${generateQueryParams({
              ids: projectIds,
            })}`,
          )
          .then((d) => {
            setProjects(new Map(d.data.data.map((x: AdminProjectResponse) => [x.id, x.name])));
          });
      }

      // vintages
      const vintageIds = uniq(
        userActionsDataResponse
          .filter((x) => x.type === "byorct_price_estimate" || x.type === "byorct_requested")
          .flatMap((x) => (x.data as any)?.vintageIds)
          .filter((x) => x !== undefined),
      );
      if (vintageIds.length) {
        api
          .get(
            `/admin/project-vintages?${generateQueryParams({
              includeRelations: [ProjectVintageRelations.PROJECT],
              ids: vintageIds,
            })}`,
          )
          .then((d) => {
            setVintages(new Map(d.data.data.map((x: AdminProjectVintageResponse) => [x.id, x])));
          });
      }

      //retirements
      const retirementIds = uniq(
        userActionsDataResponse
          .filter((x) => x.type === "rct_retirement_request")
          .flatMap((x) => (x.data as any)?.retirementId)
          .filter((x) => x !== undefined),
      );
      if (retirementIds?.length) {
        api
          .get(
            `/admin/transactions?${generateQueryParams({
              types: [RetirementType.RETIREMENT],
              ids: retirementIds,
            })}`,
          )
          .then((d) => {
            setRetirements(new Map(d.data.data.map((x: TransactionResponse) => [x.id, x])));
          });
      }

      const formattedRows: UserActivityTableRow[] = Object.entries(
        groupBy(userActionsDataResponse, (x) => x.user.id),
      ).map(([k, v]) => {
        return {
          id: v.at(0).user.id,
          user: v.at(0).user.name,
          userId: uuid(k),
          org: v.at(0).organization?.name ?? "-",
          orgId: v.at(0).organization?.id,
          lastAction: max(v.filter((x) => x.type !== "login").map((x) => x.createdAt)),
          lastLogin: max(v.filter((x) => x.type === "login").map((x) => x.createdAt)),
          actions: orderBy(
            uniq(
              v
                .filter((x) => x.type !== "login")
                .map(
                  (x) =>
                    (x.type === "byorct_price_estimate" || x.type === "byorct_requested"
                      ? "byo"
                      : x.type) as AggregateUserActionType,
                ),
            ),
          ),
          events: orderBy(
            v.filter((x) => x.type !== "login"),
            (x) => x.createdAt,
            "desc",
          ),
        };
      });
      setData(formattedRows);
    } else {
      setData([]);
    }
  }, [userActionsDataResponse, api]);

  useEffect(() => {
    if (types.length == 0) {
      setFilteredData(data);
    } else {
      setFilteredData(
        data
          .map((x) => ({
            ...x,
            events: x.events.filter(
              (y) =>
                types.includes(y.type) ||
                (types.includes("byo") && (y.type === "byorct_price_estimate" || y.type === "byorct_requested")),
            ),
          }))
          .filter((x) => x.events.length > 0)
          .map((x) => ({
            ...x,
            actions: uniq(
              x.events
                .filter((y) => y.type !== "login")
                .map(
                  (x) =>
                    (x.type === "byorct_price_estimate" || x.type === "byorct_requested"
                      ? "byo"
                      : x.type) as AggregateUserActionType,
                ),
            ),
          })),
      );
    }
  }, [data, types]);

  const popExpandContent = (row: UserActivityTableRow): JSX.Element => {
    if (row.events.length == 0)
      return (
        <>
          <h4>No Activity events available within the selected time frame.</h4>
        </>
      );
    return (
      <ExpandedRow row={row} products={products} projects={projects} vintages={vintages} retirements={retirements} />
    );
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = data?.filter(
      (row) =>
        row.user?.toUpperCase().includes(searchString) ||
        row.org?.toUpperCase().includes(searchString) ||
        dateFormatterLocalTime(row.lastAction?.toString())?.includes(searchString),
    );

    setFilteredData(filteredData);
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <div>
        <FormControl sx={{ m: 1, width: 200, backgroundColor: "white" }} size="small">
          <Select
            value={period.toString()}
            native
            onChange={async (event: SelectChangeEvent): Promise<void> => {
              setPeriod(toNumber(event.target.value));
              await refreshUserActions({
                queryParams: {
                  since: subDays(new Date(), toNumber(event.target.value)).toISOString(),
                },
              });
            }}
          >
            <option value={1}>Last 24h</option>
            <option value={7}>Last week</option>
            <option value={14}>Last two weeks</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </Select>
        </FormControl>
        <FormControl sx={{ m: 1, maxWidth: 400, minWidth: 250, backgroundColor: "white" }} size="small">
          <Autocomplete
            multiple
            value={types}
            onChange={(_, newValue) => {
              setTypes(newValue);
            }}
            size="small"
            options={allowedTypes.map((x) => x[0])}
            getOptionLabel={(option) => allowedTypeByCode.get(option) as string}
            renderTags={(tagValue, getTagProps) =>
              tagValue.map((option, index) => {
                const { key, ...tagProps } = getTagProps({ index });
                return <SearchChips key={key} actions={[option]} {...tagProps}></SearchChips>;
              })
            }
            renderInput={(params) => <TextField {...params} label="" placeholder={types.length == 0 ? "All" : ""} />}
          />
        </FormControl>
      </div>
    );
  };

  return (
    <TableBox>
      <EnhancedTable
        name={"user_activity"}
        columnsDef={columnsDef}
        data={filteredData}
        expandedContent={popExpandContent}
        getFilteredData={getFilteredData}
        searchBarContent={getSearchBarContent}
        defaultSort={{ columnName: "lastAction", order: SortOrder.DESC }}
      />
    </TableBox>
  );
}
