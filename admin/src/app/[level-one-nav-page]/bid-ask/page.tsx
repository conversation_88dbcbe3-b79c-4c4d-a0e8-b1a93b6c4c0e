import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { generateQueryParams, reportingApiRequest } from "@app/libs/server";
import { withErrorHandling } from "@app/data-server";
import BidAsk from "./components";
import { ResponseType } from "./types/response-type";
import { isValidElement } from "react";

/**
 * Bid Ask Page
 *
 * This is a server component that renders the Bid Ask page
 */
export default async function BidAskPage(): Promise<JSX.Element> {
  const marketOffers = await withErrorHandling(async () =>
    reportingApiRequest<ResponseType[]>(
      `market-offers?${generateQueryParams({
        limit: 10,
        page: 0,
        sort_order: "desc",
        sort_column: "pricing_date",
        date_filter: "1 Week",
        search_string: "",
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(marketOffers)) return marketOffers;

  return (
    <AuthorizeServer permissions={[PermissionEnum.REPORTING_MARKET_DATA]}>
      <BidAsk marketOffers={marketOffers as ResponseType[]} />
    </AuthorizeServer>
  );
}
