"use client";

import Page from "@components/layout/containers/page";
import PortfolioSandboxComponent from "./portfolios-list/sandbox";
import { ModelPortfolioQueryResponse } from "@rubiconcarbon/shared-types";

const PortfolioSandbox = ({
  modelPortfoliosResponse,
}: {
  modelPortfoliosResponse: ModelPortfolioQueryResponse;
}): JSX.Element => {
  return (
    <Page>
      <PortfolioSandboxComponent modelPortfoliosResponse={modelPortfoliosResponse} />
    </Page>
  );
};

export default PortfolioSandbox;
