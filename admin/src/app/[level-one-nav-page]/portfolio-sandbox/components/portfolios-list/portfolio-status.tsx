import React, { use<PERSON>allback, useContext, useEffect, useMemo, useState } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { FormControl, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";
import { NO_STATUS, PortfolioStatusMapping } from "@/mappers/portfolio-status-mapper";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import DialogTheme from "@components/ui/dialogs/dialog-themes";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { ModelPortfolioResponse, ModelPortfolioStatus } from "@rubiconcarbon/shared-types";
import { Undefinable } from "@rubiconcarbon/frontend-shared";

const getEnumKeyByValue = (enumerated: any, value: string): string => {
  return Object.keys(enumerated)[Object.values(enumerated).indexOf(value as typeof enumerated)];
};

const getStatusKeyByDisplayValue = (statusDisplayValue: string): Undefinable<string> => {
  return Object.keys(PortfolioStatusMapping)?.find((key) => PortfolioStatusMapping[key] === statusDisplayValue);
};

export default function PortfolioStatus(props: {
  portfolioId: string;
  currentStatus?: ModelPortfolioStatus;
  onSuccess: () => void;
}): JSX.Element {
  const { portfolioId, currentStatus, onSuccess } = props;

  const [activeStatus, setActiveStatus] = useState<number>(0);
  const [isStatusConfirmationOpen, setIsStatusConfirmationOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const statusOptions: string[] = useMemo(() => {
    const statusArr = Object.values(ModelPortfolioStatus).map((v) => PortfolioStatusMapping[v]);
    statusArr.unshift(NO_STATUS);
    return statusArr;
  }, []);

  const resetStatus = useCallback((): void => {
    if (currentStatus) {
      const index = Object.keys(ModelPortfolioStatus).indexOf(getEnumKeyByValue(ModelPortfolioStatus, currentStatus));
      setActiveStatus(index + 1);
    } else {
      setActiveStatus(0);
    }
  }, [currentStatus]);

  useEffect(() => {
    resetStatus();
  }, [currentStatus, resetStatus]);

  const statusHandler = (index: number): void => {
    setActiveStatus(index);
    setIsStatusConfirmationOpen(true);
  };

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmUpdate(portfolioId),
    },
  ];

  const onConfirmUpdate = useCallback(
    async (id: string) => {
      const newStatus = getStatusKeyByDisplayValue(statusOptions[activeStatus]);
      const payload = {
        status: newStatus !== NO_STATUS ? newStatus : null,
      };

      try {
        await api.patch<ModelPortfolioResponse>(`admin/model-portfolios/${id}`, payload);
        enqueueSuccess("Successfully updated portfolio");
        setIsStatusConfirmationOpen(false);
        onSuccess();
      } catch (error: any) {
        if (error?.response?.data?.message) {
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else enqueueError("Unable to update portfolio");
      }
    },
    [enqueueSuccess, enqueueError, api, onSuccess, activeStatus, statusOptions],
  );

  const onCancelStatusUpdate = (): void => {
    setIsStatusConfirmationOpen(false);
    resetStatus();
  };

  const getStatusLabel = useCallback(
    (index: number, label: string): string => {
      switch (label) {
        case PortfolioStatusMapping[ModelPortfolioStatus.LOST]:
          return `${statusOptions?.length - 2}b - ${label}`;
        case PortfolioStatusMapping[ModelPortfolioStatus.ORDER_CREATED]:
          return `${statusOptions?.length - 2}a - ${label}`;
        default:
          return `${index} - ${label}`;
      }
    },
    [statusOptions?.length],
  );

  return (
    <>
      <FormControl sx={{ m: 1, width: 200, backgroundColor: "white" }} size="small">
        <Select
          value={activeStatus?.toString() ?? 0}
          onChange={(event: SelectChangeEvent): void => {
            statusHandler(Number(event.target.value));
          }}
        >
          {statusOptions.map((label, index) => (
            <MenuItem key={index} value={index} sx={{ height: "35px" }}>
              {getStatusLabel(index, label)}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <ConfirmationModal
        isOpen={isStatusConfirmationOpen}
        onClose={onCancelStatusUpdate}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500, width: 450, marginBottom: "5px" }}>
          You are about to change portfolio status to{" "}
          <b>{PortfolioStatusMapping[getStatusKeyByDisplayValue(statusOptions[activeStatus]) ?? ""]}</b>.
        </Typography>
      </ConfirmationModal>
    </>
  );
}
