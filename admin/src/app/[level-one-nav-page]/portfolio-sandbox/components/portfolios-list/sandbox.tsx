import React, { useState, useEffect, useCallback, useMemo } from "react";
import { ModelPortfolioQueryResponse, ModelPortfolioStatus, PermissionEnum, uuid } from "@rubiconcarbon/shared-types";
import { stringComparator } from "@utils/comparators/comparator";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { Stack } from "@mui/material";
import { SandboxPortfolio, mapSandboxPortfolio } from "@models/sandbox-portfolio";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import PortfolioSandboxActions from "./sandbox-actions";
import NewPortfolioModal from "../portfolio-sandbox-header/new-portfolio-modal";
import useNavigation from "@/hooks/use-navigation";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import PortfolioStatus from "./portfolio-status";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";

export default function PortfolioSandbox({
  modelPortfoliosResponse: serverModelPortfoliosResponse,
}: {
  modelPortfoliosResponse: ModelPortfolioQueryResponse;
}): JSX.Element {
  const { pushToPath } = useNavigation();
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [portfolios, setPortfolios] = useState<SandboxPortfolio[]>();
  const [isNewPortfolioDialogOpen, setIsNewPortfolioDialogOpen] = useState<boolean>(false);

  const { data: modelPortfoliosResponse, trigger: refreshModelPortfolios } =
    useTriggerRequest<ModelPortfolioQueryResponse>({
      url: `/admin/model-portfolios`,
      queryParams: {
        limit: SERVER_PAGINATION_LIMIT,
      },
      optimisticData: serverModelPortfoliosResponse,
      swrOptions: {
        onError: (error: any): void => {
          enqueueError("Unable to load model portfolios");
          logger.error(`Unable to load model portfolios: ${error?.message}`, {});
        },
      },
    });

  const modelPortfolios = useMemo(() => modelPortfoliosResponse?.data, [modelPortfoliosResponse]);

  const getActions = useCallback(
    (inputMap: Map<string, string | boolean>): JSX.Element => {
      return (
        <PortfolioSandboxActions
          id={inputMap.get("id")?.toString() ?? ""}
          name={inputMap.get("name")?.toString() ?? ""}
          allowExportPDF={inputMap.get("allowExportPDF") === true ? true : false}
          refreshPortfolios={refreshModelPortfolios}
        />
      );
    },
    [refreshModelPortfolios],
  );

  const getCustomer = useCallback((inputMap: Map<string, string>): JSX.Element => {
    return (
      <CustomerPortfolio
        portfolio={
          { name: inputMap.get("organizationName"), organization: { id: inputMap.get("organizationId") } } as any
        }
        style={{ fontSize: 14 }}
      />
    );
  }, []);

  const getStatus = useCallback(
    (inputMap: Map<string, string>): JSX.Element => {
      const id = inputMap.get("id")?.toString() ?? "";
      const status = inputMap.get("status");
      return (
        <PortfolioStatus
          portfolioId={id}
          currentStatus={status as ModelPortfolioStatus}
          onSuccess={async () => {
            await refreshModelPortfolios();
          }}
        />
      );
    },
    [refreshModelPortfolios],
  );

  const columnsDef: ColDef[] = [
    {
      columnName: "uiKey",
      displayName: "Portfolio Id",
      comparator: stringComparator,
      style: { width: "200px" },
    },
    {
      columnName: "name",
      displayName: "Portfolio Name",
      comparator: stringComparator,
    },
    {
      columnName: "organizationName",
      displayName: "Customer",
      comparator: stringComparator,
      formatter: {
        func: getCustomer,
        inputFields: ["organizationName", "organizationId"],
      },
    },
    {
      columnName: "status",
      displayName: "Status",
      sortable: false,
      style: { width: "150px" },
      formatter: {
        func: getStatus,
        inputFields: ["status", "id"],
        overrideMissingDataDisplay: true,
      },
    },
    {
      columnName: "lastUpdated",
      displayName: "Last Updated",
      formatter: { func: dateFormatterEST },
      exportFormatter: { func: dateFormatterEST },
    },
    {
      columnName: "createdBy",
      displayName: "Created By",
      comparator: stringComparator,
    },
    {
      columnName: "updatedBy",
      displayName: "Updated By",
      comparator: stringComparator,
    },
    {
      columnName: "id",
      displayName: "Actions",
      formatter: {
        func: getActions,
        inputFields: ["id", "name", "allowExportPDF"],
      },
      exportable: false,
      sortable: false,
    },
  ];

  useEffect(() => {
    if (modelPortfolios) {
      modelPortfolios.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
      setPortfolios(mapSandboxPortfolio(modelPortfolios));
    }
  }, [modelPortfolios]);

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = modelPortfolios?.filter(
      (model) =>
        model?.uiKey?.toUpperCase().includes(searchString) ||
        model.name.toUpperCase().includes(searchString) ||
        model.createdBy.name.toUpperCase().includes(searchString) ||
        model.updatedBy.name.toUpperCase().includes(searchString),
    );
    setPortfolios(mapSandboxPortfolio(filteredData));
  };

  const newPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(true);
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <Stack direction="row" gap="15px">
        <ActionButton
          onClickHandler={newPortfolioHandler}
          requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_WRITE}
          style={{ width: "190px" }}
        >
          Create Portfolio
        </ActionButton>
      </Stack>
    );
  };

  const saveNewPortfolioHandler = (id: uuid): void => {
    setIsNewPortfolioDialogOpen(false);
    if (id) {
      pushToPath(`${id}/edit-portfolio-sandbox`);
    }
  };

  const closeNewPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(false);
  };

  return (
    <>
      <TableBox>
        {portfolios && (
          <EnhancedTable
            name={"portfolios_info"}
            columnsDef={columnsDef}
            exportable={true}
            data={portfolios}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "lastUpdated", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <NewPortfolioModal
        isOpen={isNewPortfolioDialogOpen}
        onSave={saveNewPortfolioHandler}
        onClose={closeNewPortfolioHandler}
      />
    </>
  );
}
