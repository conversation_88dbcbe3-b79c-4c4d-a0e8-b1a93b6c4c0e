import React, { useState, useEffect, useCallback, useContext, useMemo } from "react";
import { <PERSON>ack, IconButton, TableCell, TableRow, createFilterOptions, Typography, Box } from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import {
  uuid,
  AdminProjectResponse,
  AdminProjectVintageResponse,
  BufferCategoryResponse,
} from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import CancelIcon from "@mui/icons-material/Cancel";
import { AxiosContext } from "@providers/axios-provider";
import integerFormat from "@/utils/formatters/integer-format";
import {
  ColDef,
  PortfolioRow,
  Project,
  Vintage,
  VintageFormField,
  PortfolioComponentResponse,
  getInventoryShortage,
  validateRequiredNumberInput,
  isValidRow,
  handleQuantityChange,
  convertStringToNumber,
  BufferCategory,
  getEmptyPorrtfolioRow,
  ProjectPopulationType,
} from "./portfolio-sandbox-model";
import Decimal from "decimal.js";
import { MISSING_COST_BASIS_VALUE, MISSING_CURRENCY_VALUE, MISSING_DATA } from "@constants/constants";
import { percentageFormat, Undefinable } from "@rubiconcarbon/frontend-shared";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import { isEmpty } from "lodash";
import decimalFormat from "@/utils/formatters/decimal-format";
import IntegrityScore from "@components/integrity-score/integrity-score-chart";
import InventoryShortage from "./inventory-shortage";
import BaseRow from "../infra/portfolio-sandbox-row";

import classes from "../../styles/styles.module.scss";

const customCurrencyFormat = (input: Decimal, fixed: number = 4): string | null => {
  const CUSTOM_MISSING_DATA = fixed === 2 ? MISSING_CURRENCY_VALUE : MISSING_COST_BASIS_VALUE;
  if (input) {
    return +input > 0 ? `$${(+input).toFixed(fixed)}` : CUSTOM_MISSING_DATA;
  }
  return CUSTOM_MISSING_DATA;
};

const getCategoryById = (id: Undefinable<uuid>, bufferCategories: BufferCategoryResponse[]): Undefinable<string> => {
  if (!!id && !isEmpty(bufferCategories)) {
    return bufferCategories.find((c) => c.id === id)?.name;
  }
  return undefined;
};

const validateRow = (row: PortfolioRow): PortfolioRow => {
  return {
    ...row,
    amountAllocated: validateRequiredNumberInput(row.amountAllocated),
    bufferCategory: {
      ...row.bufferCategory,
    },
    costBasis: {
      ...row.costBasis,
    },
    portfolioManagerEstimate: {
      ...row.portfolioManagerEstimate,
    },
  };
};

export default function Row(props: {
  row: PortfolioComponentResponse;
  idx: uuid;
  editIdx?: uuid;
  isRestricted?: boolean;
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  deleteHandler: (id: uuid) => void;
  submitEditRow: (updatedRow: PortfolioRow) => void;
  availableProjects: Project[];
  bufferCategories: BufferCategoryResponse[];
  isActionDisabled?: boolean;
  projectsPopulationType?: ProjectPopulationType;
}): JSX.Element {
  const {
    row,
    idx,
    editIdx,
    startEditing,
    stopEditing,
    deleteHandler,
    submitEditRow,
    availableProjects,
    bufferCategories,
    isActionDisabled = false,
    isRestricted = false,
    projectsPopulationType = ProjectPopulationType.NOT_APPLICABLE,
  } = props;
  const [portfolioRow, setPortfolioRow] = useState<PortfolioRow>(getEmptyPorrtfolioRow());
  const [availableVintages, setAvailableVintages] = useState<Vintage[]>();
  const { api } = useContext(AxiosContext);
  const filter = createFilterOptions();
  const currentlyEditing = idx === editIdx;

  const isCustomVintage: boolean = useMemo(() => !portfolioRow?.vintageInterval?.value?.id, [portfolioRow]);

  const getAvailableVintages = useCallback(
    async (id: uuid): Promise<AdminProjectVintageResponse[]> => {
      const vintagesResponse = await api.get<AdminProjectVintageResponse[]>(`admin/projects/${id}/project-vintages`);
      return vintagesResponse.data;
    },
    [api],
  );

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const name = event.target.name;
      const row = { ...portfolioRow };
      row[name].value = value;
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferPercentageChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const row = { ...portfolioRow };
      if (!row.bufferPercentage) {
        row.bufferPercentage = {};
      }
      row.bufferPercentage.value = !isEmpty(value) ? ((convertStringToNumber(value) ?? 0) / 100).toString() : undefined;
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onBufferCategoryChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: BufferCategory): void => {
      const row = { ...portfolioRow };
      if (!row.bufferCategory) {
        row.bufferCategory = {};
      }
      row.bufferCategory.value = newValue;
      setPortfolioRow(row);
    },
    [portfolioRow],
  );

  const onQuantityChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      setPortfolioRow(handleQuantityChange(portfolioRow, event.target.value, projectsPopulationType));
    },
    [portfolioRow, projectsPopulationType],
  );

  const projectSelectionHandler = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>, newValue: AdminProjectResponse | null): Promise<void> => {
      if (newValue?.name) {
        //updating both project id and project name
        setPortfolioRow({
          ...portfolioRow,
          registryProjectId: { value: newValue ?? undefined },
          vintageInterval: { value: { name: "" } },
          projectName: { value: newValue?.name },
        });
      } else {
        setPortfolioRow({
          ...portfolioRow,
          registryProjectId: { value: newValue ?? undefined },
          vintageInterval: { value: { name: "" } },
        });
      }

      //Retrieving vintages
      if (newValue?.registryProjectId) {
        const vintages = await getAvailableVintages(newValue.id);
        if (vintages) {
          const result: Vintage[] = vintages.map((v) => ({
            id: v.id,
            name: v.name,
          }));

          setAvailableVintages(result);
        }
      }
    },
    [getAvailableVintages, portfolioRow],
  );

  const vintageSelectionHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, newValue: Vintage): void => {
      //1. typed a value and clicked enter
      if (typeof newValue === "string") {
        const newVintageInterval: VintageFormField = {
          value: { name: newValue },
        };
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: newVintageInterval,
        });
      } else if (newValue && newValue.inputValue) {
        //2. selected to add a new item
        // Create a new value from the user input
        const newVintageInterval: VintageFormField = {
          value: { name: newValue.name },
        };
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: newVintageInterval,
        });
      } else {
        //3. Selected an existing item from the list - regular flow
        setPortfolioRow({
          ...portfolioRow,
          vintageInterval: { value: newValue },
        });
      }
    },
    [portfolioRow],
  );

  const projectCategoryDefaultProps = useMemo(
    () => ({
      options: bufferCategories ?? [],
      getOptionLabel: (option: BufferCategoryResponse): string => (option?.name ? option.name : ""),
      isOptionEqualToValue: (option: BufferCategoryResponse, value: BufferCategoryResponse): boolean => option.id === value.id,
    }),
    [bufferCategories],
  );

  const projectRegistryIdDefaultProps = useMemo(
    () => ({
      options: availableProjects ?? [],
      getOptionLabel: (option: Project): string => (option?.registryProjectId ? option.registryProjectId : ""),
      isOptionEqualToValue: (option: Project, value: Project): boolean => option.id === value.id,
    }),
    [availableProjects],
  );

  const vintagesDefaultProps = useMemo(
    () => ({
      options: availableVintages ?? [], //
      getOptionLabel: (option: Vintage): string => {
        if (typeof option === "string") {
          return option;
        }
        // Add "xxx" option created dynamically
        if (option.inputValue) {
          return option.inputValue;
        }
        return option?.name;
      },
      isOptionEqualToValue: (option: Vintage, value: Vintage): boolean => option.id === value.id,
      filterOptions: (options, params): unknown[] => {
        const filtered = filter(options, params);
        // Suggest the creation of a new value
        if (params.inputValue !== "") {
          filtered.push({
            inputValue: `+ Add ${params.inputValue}`,
            name: params.inputValue,
          });
        }
        return filtered;
      },
    }),
    [availableVintages, filter],
  );

  const existingRowDef: ColDef[] = useMemo(
    () => [
      {
        id: "rctEligible",
        title: "",
        type: "display",
        value: null,
        mode: "display",
        formatter: {
          func: (x: any): JSX.Element => {
            const vintage = {
              riskBufferPercentage: x.get("bufferPercentage") as any,
              project: {
                suspended: x.get("suspended"),
                rctStandard: x.get("rctStandard"),
                isScienceTeamApproved: x.get("isScienceTeamApproved"),
              },
            } as any;

            return (
              <Typography variant="body2" component="div" sx={{ marginTop: "2px" }}>
                <RCTEligibilityChip vintage={vintage} />
              </Typography>
            );
          },
          inputFields: ["bufferPercentage", "suspended", "rctStandard", "isScienceTeamApproved"],
        },
      },
      {
        id: "registryProjectId",
        title: "Project",
        type: "autocomplete",
        value: portfolioRow?.registryProjectId?.value,
        onChange: projectSelectionHandler,
        helperText: portfolioRow?.registryProjectId?.message,
        error: portfolioRow?.registryProjectId?.error,
        defaultProps: projectRegistryIdDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.registryProjectId?.value?.id) &&
          !isEmpty(portfolioRow?.registryProjectId?.value?.registryProjectId)
            ? "this is a custom project"
            : "",
        mode: "display",
        formatter: {
          func: (x: any): JSX.Element => {
            return (
              <>
                <div>
                  {x.get("registryProjectId")} - {x.get("projectName")}
                  <Stack direction="row" gap={1}>
                    <Maybe condition={x.get("suspended") === true}>
                      <SuspendedChip />
                    </Maybe>
                  </Stack>
                </div>
              </>
            );
          },
          inputFields: ["registryProjectId", "suspended", "rctStandard", "projectName"],
        },
      },
      {
        id: "type",
        title: "Type",
        type: "display",
        value: row?.project?.projectType?.type ?? MISSING_DATA,
      },
      {
        id: "integrityGradeScore",
        title: "Rubicon Carbon Integrity Grade",
        type: "display",
        value: row?.project?.integrityGradeScore ?? MISSING_DATA,
        formatter: {
          func: (): JSX.Element => {
            const integrityGradeScore = availableProjects?.find((p) => p.id === row["projectId"])?.integrityGradeScore;
            return (
              <>
                <Maybe condition={!!integrityGradeScore}>
                  <Box sx={{ marginTop: "-5px" }}>
                    <IntegrityScore
                      score={Math.round(integrityGradeScore ?? 0)}
                      separate
                      hideSubtext
                      className={classes.integrityGrade}
                    />
                  </Box>
                </Maybe>
                <Maybe condition={!integrityGradeScore}>
                  <Box>{MISSING_DATA}</Box>
                </Maybe>
              </>
            );
          },
        },
      },
      {
        id: "country",
        title: "Country",
        type: "display",
        value: row?.project?.country?.name ?? MISSING_DATA,
      },
      {
        id: "vintageInterval",
        title: "Vintage",
        type: "autocomplete",
        value: portfolioRow?.vintageInterval?.value,
        onChange: vintageSelectionHandler,
        helperText: portfolioRow?.vintageInterval?.message,
        error: portfolioRow?.vintageInterval?.error,
        defaultProps: vintagesDefaultProps,
        tooltip:
          isEmpty(portfolioRow?.vintageInterval?.value?.id) && !isEmpty(portfolioRow?.vintageInterval?.value?.name)
            ? "this is a custom vintage"
            : "",
        mode: "display",
      },
      {
        id: "bufferPercentage",
        title: "Required Buffer",
        type: isCustomVintage ? "number" : "display",
        value: portfolioRow?.bufferPercentage?.value,
        onChange: onBufferPercentageChangeHandler,
        helperText: portfolioRow?.bufferPercentage?.message,
        error: portfolioRow?.bufferPercentage?.error,
        formatter: { func: (x: number) => decimalFormat(x * 100) },
        mode: "edit",
        hidden: isRestricted,
        valueFormatter: percentageFormat,
        suffix: "%",
      },
      {
        id: "bufferCategory",
        title: "Buffer Category",
        type: isCustomVintage ? "autocomplete" : "display",
        value:
          !!portfolioRow?.vintageInterval?.value?.id || !currentlyEditing
            ? portfolioRow?.bufferCategory?.value?.name || MISSING_DATA
            : portfolioRow?.bufferCategory?.value,
        onChange: onBufferCategoryChangeHandler,
        helperText: portfolioRow?.bufferCategory?.message,
        error: portfolioRow?.bufferCategory?.error,
        defaultProps: projectCategoryDefaultProps,
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "amountAllocated",
        title: "Quantity",
        type: "number",
        value: portfolioRow?.amountAllocated?.value,
        onChange: onQuantityChangeHandler,
        helperText: portfolioRow?.amountAllocated?.message,
        error: portfolioRow?.amountAllocated?.error,
        formatter: { func: integerFormat },
        mode: "edit",
      },
      {
        id: "inventoryShortage",
        title: "Inventory Shortage",
        type: "display",
        value: MISSING_DATA,
        formatter: {
          func: () => (
            <InventoryShortage
              projectsPopulationType={projectsPopulationType}
              availableAmount={row?.amountAvailable ?? 0}
              portfolioRow={portfolioRow}
            />
          ),
        },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "costBasis",
        title: "Cost Basis",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.costBasis?.value ?? null,
        onChange: onChangeHandler,
        helperText: portfolioRow?.costBasis?.message,
        error: portfolioRow?.costBasis?.error,
        decimalScale: 4,
        prefix: "$",
        formatter: { func: customCurrencyFormat },
        mode: "edit",
        hidden: isRestricted,
      },
      {
        id: "portfolioManagerEstimate",
        title: "MTM",
        type: portfolioRow?.vintageInterval?.value?.id ? "display" : "number",
        value: portfolioRow?.portfolioManagerEstimate?.value ?? null,
        onChange: onChangeHandler,
        helperText: portfolioRow?.portfolioManagerEstimate?.message,
        error: portfolioRow?.portfolioManagerEstimate?.error,
        decimalScale: 2,
        prefix: "$",
        formatter: { func: (x: Decimal) => customCurrencyFormat(x, 2) },
        mode: "edit",
        hidden: isRestricted,
      },
    ],
    [
      isCustomVintage,
      onBufferCategoryChangeHandler,
      projectCategoryDefaultProps,
      portfolioRow,
      row,
      onChangeHandler,
      onBufferPercentageChangeHandler,
      onQuantityChangeHandler,
      projectRegistryIdDefaultProps,
      projectSelectionHandler,
      vintageSelectionHandler,
      vintagesDefaultProps,
      availableProjects,
      currentlyEditing,
      isRestricted,
      projectsPopulationType,
    ],
  );

  useEffect(() => {
    const rowBufferCatId = row?.vintageId
      ? (row?.vintage as AdminProjectVintageResponse)?.project?.bufferCategory?.id
      : row?.bufferCategoryId;

    setPortfolioRow({
      id: row?.id,
      registryProjectId: {
        value: {
          registryProjectId: row?.registryProjectId ?? "",
          id: row?.projectId,
        },
      },
      vintageInterval: {
        value: {
          name: row?.vintageInterval?.toString() ?? "",
          id: row?.vintageId ?? undefined,
          amountAvailable: row?.amountAvailable,
          holdingAmount: row?.holdingAmount,
        },
      },
      bufferPercentage: { value: row?.bufferPercentage?.toString() },
      bufferCategory: {
        value: {
          name: getCategoryById(rowBufferCatId, bufferCategories) ?? "",
          id: rowBufferCatId,
        },
      },
      assetAllocationsByBookType: row?.assetAllocationsByBookType,
      amountAllocated: {
        value: row?.amountAllocated?.toString(),
        message: `${row?.holdingAmount ? `Holding: ${integerFormat(row.holdingAmount)}` : ""} ${row?.amountAvailable ? `Available: ${integerFormat(row.amountAvailable)}` : ""}`,
      },
      costBasis: { value: row?.costBasis ? row.costBasis?.toFixed(4, Decimal.ROUND_HALF_UP).toString() : "0" },
      portfolioManagerEstimate: {
        value: row?.portfolioManagerEstimate ? row?.portfolioManagerEstimate?.toString() : "0",
      },
      inventoryShortage: getInventoryShortage(
        row?.amountAllocated,
        row?.holdingAmount ?? 0,
        row?.assetAllocationsByBookType ?? [],
      ),
    });
  }, [startEditing, bufferCategories, row]);

  const cancelEditHandler = (): void => {
    stopEditing();
  };

  const submitRowHandler = (): void => {
    const validatedRow = validateRow(portfolioRow);
    setPortfolioRow(validatedRow);
    if (isValidRow(validatedRow)) {
      submitEditRow(validatedRow);
    }
  };

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        <BaseRow rowDef={existingRowDef} type={"existing"} isEdit={currentlyEditing} rowData={row} />
        <TableCell>
          {currentlyEditing ? (
            <Stack direction="row" gap={2}>
              <IconButton sx={{ color: "rgba(154, 198, 106, 1)" }} edge="start" onClick={submitRowHandler}>
                <CheckIcon />
              </IconButton>
              <IconButton
                sx={{ marginLeft: "1px", color: COLORS.rubiconGreen }}
                edge="start"
                onClick={cancelEditHandler}
              >
                <CancelIcon />
              </IconButton>
            </Stack>
          ) : (
            <Stack direction="row" gap={2}>
              <IconButton
                disabled={isActionDisabled}
                sx={{ color: COLORS.rubiconGreen }}
                edge="start"
                onClick={startEditing.bind(this, idx)}
              >
                <CreateIcon />
              </IconButton>
              <IconButton
                disabled={isActionDisabled}
                sx={{ color: COLORS.rubiconGreen }}
                edge="start"
                onClick={deleteHandler.bind(this, row.id)}
              >
                <DeleteIcon />
              </IconButton>
            </Stack>
          )}
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}
