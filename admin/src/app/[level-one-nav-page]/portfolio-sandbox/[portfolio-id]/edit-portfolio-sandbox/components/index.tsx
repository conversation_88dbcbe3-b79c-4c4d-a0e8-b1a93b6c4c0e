"use client";

import Page from "@components/layout/containers/page";
import EditPortfolioSandboxComponent from "./edit-portfolio-sandbox";
import { ModelPortfolioResponse } from "@rubiconcarbon/shared-types";

const EditPortfolioSandbox = ({ modelPortfolio }: { modelPortfolio: ModelPortfolioResponse }): JSX.Element => {
  return (
    <Page>
      <EditPortfolioSandboxComponent modelPortfolio={modelPortfolio} />
    </Page>
  );
};

export default EditPortfolioSandbox;
