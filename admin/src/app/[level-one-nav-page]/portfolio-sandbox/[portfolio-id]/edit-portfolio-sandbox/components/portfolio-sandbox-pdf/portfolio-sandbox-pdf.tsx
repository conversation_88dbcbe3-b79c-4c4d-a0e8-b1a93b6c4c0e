import React, { useCallback } from "react";
import { Box, Tooltip } from "@mui/material";
import useFileDownloader, { DownloadResult } from "@hooks/use-file-downloader";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { uuid } from "@rubiconcarbon/shared-types";
import PDFIcon from "@components/icons/pdf-icon";

const PDFBoxStyle = {
  paddingTop: "5px",
  "&:hover": {
    cursor: "pointer",
  },
};

export default function PortfolioSandboxPDF(props: {
  portfolioId: uuid;
  portfolioName: string;
  tooltip?: string;
  isDisabled?: boolean;
}): JSX.Element {
  const { portfolioId, portfolioName, isDisabled = false, tooltip = "" } = props;
  const { download } = useFileDownloader();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const downloadPDFHandler = useCallback(async () => {
    if (!!portfolioId && !!portfolioName) {
      const result: DownloadResult = await download(
        `portfolio_${portfolioName}`,
        `reporting/pdf/model-portfolio?id=${portfolioId}`,
      );
      if (result?.isSuccess) {
        enqueueSuccess("PDF exported successfully");
      } else {
        enqueueError("Unable to download PDF");
      }
    }
  }, [portfolioId, portfolioName, download, enqueueSuccess, enqueueError]);

  return (
    <Tooltip title={tooltip} placement="left-start">
      <Box sx={PDFBoxStyle} onClick={isDisabled ? undefined : downloadPDFHandler}>
        <PDFIcon height={34} width={34} disabled={isDisabled} />
      </Box>
    </Tooltip>
  );
}
