import { AuthorizeServer } from "@app/authorize-server";
import { ModelPortfolioResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import EditPortfolioSandbox from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement } from "react";

/**
 * Edit Portfolio Sandbox Page
 *
 * This is a server component that renders the Edit Portfolio Sandbox page
 */
export default async function EditPortfolioSandboxPage({
  params,
}: {
  params: Promise<{ "portfolio-id": string }>;
}): Promise<JSX.Element> {
  const { "portfolio-id": id } = await params;

  const modelPortfolioResponse = await withErrorHandling(async () =>
    baseApiRequest<ModelPortfolioResponse>(`admin/model-portfolios/${id}`),
  );

  // Check if the result is a server error
  if (isValidElement(modelPortfolioResponse)) return modelPortfolioResponse;

  return (
    <AuthorizeServer
      permissions={[PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE, PermissionEnum.MODEL_PORTFOLIOS_WRITE]}
    >
      <EditPortfolioSandbox modelPortfolio={modelPortfolioResponse as ModelPortfolioResponse} />
    </AuthorizeServer>
  );
}
