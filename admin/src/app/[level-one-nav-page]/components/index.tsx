"use client";

import { useAppPathname } from "@hooks/router-hooks";
import NestedMenuSection from "@components/nested-menu-section/nested-menu-section";
import useNavigationMenu from "@providers/navigation-menu-provider";
import Page from "@components/layout/containers/page";

export default function LevelOneNav(): JSX.Element {
  const pathname = useAppPathname();
  const { permissibleMenus, getPermissableMenu } = useNavigationMenu();

  if (!getPermissableMenu || !permissibleMenus) return <></>;

  const menu = getPermissableMenu(pathname, permissibleMenus, "equals");

  return <Page>{!!menu && <NestedMenuSection menus={[menu]} />}</Page>;
}
