import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import TradeCounterpartyForm from "./components";

/**
 * Create Trade Counterparty Page
 *
 * This is a server component that renders the Create Trade Counterparty page
 */
export default function CreateTradeCounterpartyPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.COUNTERPARTIES_CREATE]}>
      <TradeCounterpartyForm />
    </AuthorizeServer>
  );
}
