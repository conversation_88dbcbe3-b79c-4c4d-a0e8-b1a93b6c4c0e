import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { UnifiedOrgStatusChip } from "@components/ui/unified-org-status-chip/unified-org-status-chip";
import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";
import { AuthContext } from "@providers/auth-provider";
import useNavigation from "@/hooks/use-navigation";
import { Stack, Tooltip, IconButton, Divider } from "@mui/material";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { MouseEvent, useContext, useMemo } from "react";

const ActionsCell = ({ row }: { row: GenericTableRowModel<UnifiedOrganizationModel> }): JSX.Element => {
  const { user } = useContext(AuthContext);
  const { pushToPath } = useNavigation();

  const permissions = useMemo(() => user.permissions, [user.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.COUNTERPARTIES_UPDATE);
  const hasPermissionToUpload = permissions.includes(PermissionEnum.DOCUMENTS_CREATE);

  return (
    <Stack direction="row" alignItems="center" gap={1}>
      <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : "Edit counterparty"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();
            pushToPath(`${row.id}/edit-trade-counterparty`);
          }}
        >
          <MatIcon value="edit" variant="round" size={24} color={!hasPermissionToEdit ? "disabled" : "primary"} />
        </IconButton>
      </Tooltip>
      <Divider sx={{ height: 28.5 }} orientation="vertical" />
      <Tooltip title={!hasPermissionToUpload ? "Insufficient permissions" : "Upload document"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();
            pushToPath(`${row.id}/upload-document?type=${OrganizationType.Counterparty}`);
          }}
        >
          <MatIcon
            value="file_upload"
            variant="round"
            size={24}
            color={!hasPermissionToEdit ? "disabled" : "primary"}
          />
        </IconButton>
      </Tooltip>
    </Stack>
  );
};

export const COUNTERPARTY_COLUMNS: GenericTableColumn<UnifiedOrganizationModel>[] = [
  {
    field: "name",
    label: "Name",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
  },
  {
    field: "createdAtF",
    label: "Create Date",
    type: "date",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "onboardingStatusF",
    label: "Onboard Status",
    useRenderedAsEdit: true,
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <UnifiedOrgStatusChip row={row} path={{ value: "isOnboarded", label: "onboardingStatusF" }} />
    ),
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
