import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { UnifiedOrgStatusChip } from "@components/ui/unified-org-status-chip/unified-org-status-chip";
import { UnifiedOrganizationModel } from "@models/organization";
import Link from "next/link";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { AuthContext } from "@providers/auth-provider";
import { MouseEvent, useContext, useMemo } from "react";
import { Divider, IconButton, Stack, Tooltip } from "@mui/material";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import useNavigation from "@/hooks/use-navigation";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { OrganizationType } from "@constants/organization-type.enum";

import classes from "../styles/links.module.scss";

const ActionsCell = ({ row }: { row: GenericTableRowModel<UnifiedOrganizationModel> }): JSX.Element => {
  const { user } = useContext(AuthContext);
  const { pushToPath } = useNavigation();

  const permissions = useMemo(() => user.permissions, [user.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.ORGANIZATIONS_UPDATE);
  const hasPermissionToUpload = permissions.includes(PermissionEnum.DOCUMENTS_CREATE);

  return (
    <Stack direction="row" alignItems="center" gap={1}>
      <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : "Edit organization"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            pushToPath(`${row.id}/edit-portal-organization`);
          }}
        >
          <MatIcon value="edit" variant="round" size={24} color={!hasPermissionToEdit ? "disabled" : "primary"} />
        </IconButton>
      </Tooltip>
      <Divider sx={{ height: 28.5 }} orientation="vertical" />
      <Tooltip title={!hasPermissionToUpload ? "Insufficient permissions" : "Upload document"}>
        <IconButton
          color="primary"
          disabled={!hasPermissionToEdit}
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            pushToPath(`${row.id}/upload-document?type=${OrganizationType.Portal}`);
          }}
        >
          <MatIcon
            value="file_upload"
            variant="round"
            size={24}
            color={!hasPermissionToEdit ? "disabled" : "primary"}
          />
        </IconButton>
      </Tooltip>
    </Stack>
  );
};

export const PORTAL_COLUMNS: GenericTableColumn<UnifiedOrganizationModel>[] = [
  {
    field: "name",
    label: "Organization",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row): JSX.Element => (
      <Link
        href={`/customer-management/organizations/${row.id}`}
        className={classes.Link}
        onClick={(e) => e.stopPropagation()}
      >
        {row.name}
      </Link>
    ),
  },
  {
    field: "rubiconManager.name",
    label: "Account Manager",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
  },
  {
    field: "salesforceIdentifier",
    label: "Salesforce Account ID",
    width: GenericTableFieldSizeEnum.large,
    fixedWidth: true,
  },
  {
    field: "createdAtF",
    label: "Create Date",
    type: "date",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "statusF",
    label: "Status",
    width: GenericTableFieldSizeEnum.xsmall,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <UnifiedOrgStatusChip row={row} path={{ value: "isEnabled", label: "statusF" }} />
    ),
  },
  {
    field: "onboardingStatusF",
    label: "Onboard Status",
    useRenderedAsEdit: true,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <UnifiedOrgStatusChip row={row} path={{ value: "isOnboarded", label: "onboardingStatusF" }} />
    ),
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
