import { AuthorizeServer } from "@app/authorize-server";
import { CounterpartyQueryResponse, OrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import Organizations from "./components";
import { withPermissionHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Organizations Page
 *
 * This is a server component that renders the Organizations page
 */
export default async function OrganizationsPage(): Promise<JSX.Element> {
  const organizationResponse = await withPermissionHandling(
    async () =>
      baseApiRequest<OrganizationResponse[]>(
        `admin/organizations?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
        })}`,
      ),
    [PermissionEnum.ORGANIZATIONS_READ],
    { ignoreError: true },
  );

  const counterpartiesResponse = await withPermissionHandling(
    async () =>
      baseApiRequest<CounterpartyQueryResponse>(
        `admin/counterparties?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeTotalCount: true,
        })}`,
      ),
    [PermissionEnum.COUNTERPARTIES_READ],
    { ignoreError: true },
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;
  if (isValidElement(counterpartiesResponse)) return counterpartiesResponse;

  return (
    <AuthorizeServer
      partiallyAuthorize
      permissions={[PermissionEnum.ORGANIZATIONS_READ, PermissionEnum.COUNTERPARTIES_READ]}
    >
      <Organizations
        organizationResponse={organizationResponse as OrganizationResponse[]}
        counterpartiesResponse={counterpartiesResponse as CounterpartyQueryResponse}
      />
    </AuthorizeServer>
  );
}
