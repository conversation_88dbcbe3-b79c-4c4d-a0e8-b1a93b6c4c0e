"use client";

import { AdminBookQueryResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import PortalOrganizationComponent from "./portal-organization";

export default function PortalOrganization({
  organizationResponse,
  customerPortfolioResponse,
}: {
  organizationResponse: OrganizationResponse;
  customerPortfolioResponse: AdminBookQueryResponse;
}): JSX.Element {
  return (
    <PortalOrganizationComponent
      organizationResponse={organizationResponse}
      customerPortfolioResponse={customerPortfolioResponse}
    />
  );
}
