import { AuthorizeServer } from "@app/authorize-server";
import { CounterpartyResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement } from "react";
import TradeCounterpartyForm from "./components";

/**
 * Edit Trade Counterparty Page
 *
 * This is a server component that renders the Edit Trade Counterparty page
 */
export default async function EditTradeCounterpartyPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  const counterpartyResponse = await withErrorHandling(async () =>
    baseApiRequest<CounterpartyResponse>(`admin/counterparties/${id}`),
  );

  // Check if the result is a server error
  if (isValidElement(counterpartyResponse)) return counterpartyResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.COUNTERPARTIES_UPDATE]}>
      <TradeCounterpartyForm counterpartyResponse={counterpartyResponse as CounterpartyResponse} />
    </AuthorizeServer>
  );
}
