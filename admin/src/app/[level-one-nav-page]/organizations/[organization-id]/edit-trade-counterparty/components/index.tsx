"use client";

import { CounterpartyResponse } from "@rubiconcarbon/shared-types";
import TradeCounterpartyFormComponent from "../../../components/trade-counterparty-form";

export default function TradeCounterpartyForm({
  counterpartyResponse,
}: {
  counterpartyResponse?: CounterpartyResponse;
}): JSX.Element {
  return <TradeCounterpartyFormComponent counterpartyResponse={counterpartyResponse} />;
}
