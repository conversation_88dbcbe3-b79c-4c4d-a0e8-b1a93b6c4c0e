import { AuthorizeServer } from "@app/authorize-server";
import { AdminBookQueryResponse, BookType, OrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import PortalOrganization from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Organization Detail Page
 *
 * This is a server component that renders the organization detail page
 */
export default async function OrganizationDetailPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  const organizationResponse = await withErrorHandling(async () =>
    baseApiRequest<OrganizationResponse>(`admin/organizations/${id}`),
  );

  const customerPortfolioResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookQueryResponse>(
      `admin/books?${generateQueryParams({
        organizationId: id,
        types: [BookType.PORTFOLIO_CUSTOMER],
        limit: SERVER_PAGINATION_LIMIT,
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;
  if (isValidElement(customerPortfolioResponse)) return customerPortfolioResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_READ]}>
      <PortalOrganization
        organizationResponse={organizationResponse as OrganizationResponse}
        customerPortfolioResponse={customerPortfolioResponse as AdminBookQueryResponse}
      />
    </AuthorizeServer>
  );
}
