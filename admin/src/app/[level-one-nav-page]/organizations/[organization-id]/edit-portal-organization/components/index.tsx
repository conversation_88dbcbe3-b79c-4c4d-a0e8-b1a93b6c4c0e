"use client";

import PortalOrganizationFormComponent from "../../../components/portal-organization-form";
import { OrganizationResponse, UserResponse } from "@rubiconcarbon/shared-types";

export default function PortalOrganizationForm({
  organizationResponse,
  managers,
}: {
  organizationResponse?: OrganizationResponse;
  managers: UserResponse[];
}): JSX.Element {
  return <PortalOrganizationFormComponent organizationResponse={organizationResponse} managers={managers} />;
}
