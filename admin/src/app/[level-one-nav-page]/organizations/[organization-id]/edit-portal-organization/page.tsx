import { AuthorizeServer } from "@app/authorize-server";
import {
  OrganizationResponse,
  OrganizationUserRole,
  PermissionEnum,
  UserResponse,
  UserStatus,
} from "@rubiconcarbon/shared-types";
import PortalOrganizationForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement } from "react";

/**
 * Edit Portal Organization Page
 *
 * This is a server component that renders the Edit Portal Organization page
 */
export default async function EditPortalOrganizationPage({
  params,
}: {
  params: Promise<{ "organization-id": string }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;

  const organizationResponse = await withErrorHandling(async () =>
    baseApiRequest<OrganizationResponse>(`admin/organizations/${id}`),
  );

  const managers = await withErrorHandling(async () =>
    baseApiRequest<UserResponse[]>(
      `admin/organizations/${process.env.NEXT_PUBLIC_ADMIN_ORGANIZATION}/users?${generateQueryParams({
        status: UserStatus.ENABLED,
        roles: [OrganizationUserRole.MANAGER],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(organizationResponse)) return organizationResponse;
  if (isValidElement(managers)) return managers;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_UPDATE]}>
      <PortalOrganizationForm
        organizationResponse={organizationResponse as OrganizationResponse}
        managers={managers as UserResponse[]}
      />
    </AuthorizeServer>
  );
}
