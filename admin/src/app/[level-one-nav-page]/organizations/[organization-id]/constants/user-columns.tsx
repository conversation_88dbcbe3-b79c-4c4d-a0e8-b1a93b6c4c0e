import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { Button, Popover, Typography } from "@mui/material";
import { UserResponse } from "@rubiconcarbon/shared-types";
import { UnifiedOrgStatusChip } from "@components/ui/unified-org-status-chip/unified-org-status-chip";
import { Maybe, usePopperState } from "@rubiconcarbon/frontend-shared";
import { UserRoleLabel } from "@constants/user-role";
import { ArrowDropDown } from "@mui/icons-material";

export type UserModel = {
  rolesF: string;
  statusF: string;
  statusBool: boolean;
} & UserResponse;

const RolesCell = ({ row }: { row: GenericTableRowModel<UserModel> }): JSX.Element => {
  const isMultiple = row?.organization?.userRoles?.length > 1;

  const { popperId, ref: popperRef, popout, close, toggle } = usePopperState<HTMLButtonElement>({ id: "more-actions" });

  return (
    <>
      <Maybe condition={!isMultiple}>
        <Typography fontSize={14} fontWeight={300}>
          {UserRoleLabel[row?.organization?.userRoles?.[0]]}
        </Typography>
      </Maybe>
      <Maybe condition={isMultiple}>
        <Button
          ref={popperRef}
          disableRipple
          onClick={toggle}
          endIcon={
            popout ? <ArrowDropDown fontSize="small" sx={{ rotate: "180deg" }} /> : <ArrowDropDown fontSize="small" />
          }
          color="inherit"
          sx={{
            fontSize: 14,
            fontWeight: 300,
            textTransform: "none",
            padding: 0,
            "&:hover": {
              backgroundColor: "transparent",
            },
          }}
        >
          Multiple
        </Button>
        <Popover
          id={popperId}
          open={popout}
          anchorEl={popperRef?.current}
          onClose={close}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "center",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "center",
          }}
        >
          {row?.organization?.userRoles?.map((role) => (
            <Typography key={role} fontSize={14} fontWeight={300} sx={{ p: 1 }}>
              {UserRoleLabel[role]}
            </Typography>
          ))}
        </Popover>
      </Maybe>
    </>
  );
};

export const USER_COLUMNS: GenericTableColumn<UserModel>[] = [
  {
    field: "name",
    label: "Name",
    width: GenericTableFieldSizeEnum.large,
    fixedWidth: true,
  },
  {
    field: "organization.userRoles",
    label: "Roles",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <RolesCell row={row} />,
  },
  {
    field: "email",
    label: "Email",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    type: "text-popper",
  },
  {
    field: "createdAt",
    label: "Create Date",
    type: "datetime",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
  },
  {
    field: "status",
    label: "Status",
    width: GenericTableFieldSizeEnum.xsmall,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <UnifiedOrgStatusChip
        interactive={false}
        row={row as any}
        path={{ value: "statusBool" as any, label: "statusF" }}
      />
    ),
  },
];
