import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { DocumentResponse } from "@rubiconcarbon/shared-types";
import { Attachment } from "@components/attachment-list/attachment-list";

export type DocumentModel = {
  typeF: string;
  createdAtF: string;
} & DocumentResponse;

const DocumentCell = ({ row }: { row: GenericTableRowModel<DocumentModel> }): JSX.Element => {
  return (
    <Attachment
      document={{
        as: "icon",
        canDelete: false,
        ...row,
      }}
    />
  );
};

export const DOCUMENT_COLUMNS: GenericTableColumn<DocumentModel>[] = [
  {
    field: "filename",
    label: "Document Name",
    width: GenericTableFieldSizeEnum.xxxlarge,
    fixedWidth: true,
  },
  {
    field: "typeF",
    label: "Type",
    width: GenericTableFieldSizeEnum.large,
    fixedWidth: true,
  },
  {
    field: "createdAtF",
    label: "Create Date",
    type: "datetime",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    label: "Document",
    type: "action",
    sortable: false,
    exportable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <DocumentCell row={row} />,
  },
];
