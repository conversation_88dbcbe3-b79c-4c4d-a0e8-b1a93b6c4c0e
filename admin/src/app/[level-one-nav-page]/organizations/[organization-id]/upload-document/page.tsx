import { AuthorizeServer } from "@app/authorize-server";
import { CounterpartyResponse, OrganizationResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { OrganizationType } from "@constants/organization-type.enum";
import DocumentUpload from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest } from "@app/libs/server";
import { isValidElement } from "react";

/**
 * Upload Document Page
 *
 * This is a server component that renders the Upload Document page
 */
export default async function UploadDocumentPage({
  params,
  searchParams,
}: {
  params: Promise<{ "organization-id": string }>;
  searchParams: Promise<{ type: OrganizationType }>;
}): Promise<JSX.Element> {
  const { "organization-id": id } = await params;
  const { type } = await searchParams;

  const assetResponse = await withErrorHandling(async () =>
    baseApiRequest<CounterpartyResponse | OrganizationResponse>(
      `admin/${type === OrganizationType.Counterparty ? "counterparties" : "organizations"}/${id}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(assetResponse)) return assetResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.DOCUMENTS_CREATE]}>
      <DocumentUpload assetResponse={assetResponse as CounterpartyResponse | OrganizationResponse} type={type} />
    </AuthorizeServer>
  );
}
