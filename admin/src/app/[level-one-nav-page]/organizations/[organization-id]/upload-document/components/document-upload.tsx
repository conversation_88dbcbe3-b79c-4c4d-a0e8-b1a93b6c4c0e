import useS<PERSON> from "swr";
import { Box, Grid, Typography } from "@mui/material";
import {
  OrganizationResponse,
  DocumentType,
  DocumentUpdateRequest,
  DocumentUploadUrlRequest,
  uuid,
  CounterpartyResponse,
} from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { useRouter } from "next/navigation";
import { useCallback, useMemo } from "react";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useGetSetState } from "react-use";
import { useLogger } from "@providers/logging";
import { px } from "@rubiconcarbon/frontend-shared";
import { OrganizationType } from "@constants/organization-type.enum";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import UploaderWidget from "@components/ui/uploader/components/UploaderWidget";

import classes from "../styles/document-upload.module.scss";

export default function DocumentUpload({
  assetResponse,
  type,
}: {
  assetResponse: CounterpartyResponse | OrganizationResponse;
  type: OrganizationType;
}): JSX.Element {
  const { logger } = useLogger();
  const router = useRouter();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<DocumentUploadUrlRequest>();
  const [updatePayload, setUpdatePayload] = useGetSetState<DocumentUpdateRequest>();

  const id = useMemo(() => (assetResponse as CounterpartyResponse).id, [assetResponse]);
  const isACounterparty = useMemo(() => type === OrganizationType.Counterparty, [type]);

  const {
    data: { name } = { name: "" },
    error,
    isLoading,
  } = useSWR<OrganizationResponse | CounterpartyResponse>(
    id ? `/admin/${isACounterparty ? "counterparties" : "organizations"}/${id}` : null,
  );

  const { fetching, fetch, retrieveUploadLink, update } = useDocumentsApi({
    query: {
      ...px({ organizationId: !isACounterparty && id, counterpartyId: isACounterparty && id }, [
        null,
        undefined,
        false,
      ]),
      types: [DocumentType.MASTER_AGREEMENT],
    },
    updatePayload: updatePayload(),
    uploadLinkPayload: getUploadLinkPayload(),
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
    },
    onUpdateSuccess: async () => {
      enqueueSuccess("Successfully uploaded file");
      await fetch();
    },
    onUpdateError: (error: any) => {
      enqueueError("Successful upload but was unable to update file details");
      logger.error(`Successful upload but was unable to update file details: ${error?.message}`, {});
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!id) {
      setTimeout(async () => await fetch());
    }
  }, [id]);

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      ...px({ organizationId: !isACounterparty && id, counterpartyId: isACounterparty && id }, [
        null,
        undefined,
        false,
      ]),
      filename: file.name,
      type: DocumentType.MASTER_AGREEMENT,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const onFileUploadSuccess = useCallback(
    async (file: File, metadata: OnFileUploadSuccessMetaData): Promise<void> => {
      setUpdatePayload({
        id: uuid(metadata?.s3FileId),
        ...px({ organizationId: !isACounterparty && id, counterpartyId: isACounterparty && id }, [
          null,
          undefined,
          false,
        ]),
        filename: file.name,
        type: DocumentType.MASTER_AGREEMENT,
        isPublic: true,
      });

      setTimeout(async () => await update());
    },
    [id, isACounterparty, setUpdatePayload, update],
  );

  const onFileUploadError = useCallback((): void => {
    enqueueError("Unable to upload file");
  }, [enqueueError]);

  return (
    <Maybe condition={!isLoading || !error}>
      <Box className={classes.Container}>
        <Grid className={classes.Content} container direction="column" gap={3}>
          <Grid item>
            <Typography variant="body2">File details</Typography>
          </Grid>
          <Grid item container direction="row">
            <Grid item container md={3} xs={6} direction="row" gap={1}>
              <Typography className={classes.Label} variant="body2">
                {isACounterparty ? "Counterparty" : "Organization"}:
              </Typography>
              <Typography variant="body2">{name}</Typography>
            </Grid>
          </Grid>
          <UploaderWidget
            inputId={`${isACounterparty ? "counterparty" : "organization"}-uploads`}
            maxFiles={10}
            canDragAndDrop
            cancelButtonText="back"
            uploadLink={getS3UploadApiLink}
            allowedExtensions={["application/pdf"]}
            clearOnFileUploadSuccess
            uploadConfirmation={{
              title: "Confirm document upload",
              content: (
                <span>
                  Are you sure you want to{" "}
                  {type === OrganizationType.Portal ? "share file with Customer Portal Users" : "upload this file"}?
                </span>
              ),
            }}
            onFileUploadSuccess={onFileUploadSuccess}
            onFileUploadError={onFileUploadError}
            handleUploadClosure={() => router.back()}
          />
        </Grid>
      </Box>
    </Maybe>
  );
}
