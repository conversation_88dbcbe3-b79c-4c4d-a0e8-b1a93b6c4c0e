"use client";

import { CounterpartyResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import DocumentUpload from "./document-upload";
import { OrganizationType } from "@constants/organization-type.enum";

export default function UploadDocument({
  assetResponse,
  type,
}: {
  assetResponse: CounterpartyResponse | OrganizationResponse;
  type: OrganizationType;
}): JSX.Element {
  return <DocumentUpload assetResponse={assetResponse} type={type} />;
}
