import React, { useState, BaseSyntheticEvent, useContext, useEffect, useMemo, useCallback } from "react";
import { AxiosContext } from "@providers/axios-provider";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  DialogTitle,
  FormControlLabel,
  Radio,
  FormControl,
  RadioGroup,
} from "@mui/material";
import { CounterpartyResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useLogger } from "@providers/logging";
import { BaseDialogProps } from "@models/dialogs";
import { UnifiedOrganizationModel } from "@models/organization";
import { OrganizationType } from "@constants/organization-type.enum";

const cancelBtnStyle = {
  height: 35,
  color: "rgba(0, 0, 0, 0.87)",
  borderRadius: "5px",
  textTransform: "capitalize",
  fontWeight: 500,
  backgroundColor: "rgba(224, 224, 224, 1)",
  "&.Mui-disabled": {
    color: "gray !important",
  },
  "&:hover": {
    backgroundColor: "rgba(224, 224, 224, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 1px 1px",
  },
};

const disabledStyle = {
  marginTop: "-20px",
  padding: "16px",
  height: "145px",
  backgroundColor: "rgba(253, 237, 237, 1)",
};

const enabledStyle = {
  marginTop: "-25px",
  padding: "16px",
  height: "160px",
  backgroundColor: "rgba(229, 246, 253, 1)",
};

const radioLabelStyle = {
  marginTop: 1,
  marginBottom: 1,
};

interface OrgStatusModalProps extends BaseDialogProps {
  type: OrganizationType;
  model: UnifiedOrganizationModel;
  onSave: () => void;
}

export default function OrganizationStatusModal({
  type,
  model,
  isOpen,
  onClose,
  onSave,
}: OrgStatusModalProps): JSX.Element | null {
  const [isConfirmation, setIsConfirmation] = useState<boolean>(false);
  const [isEnabled, setIsEnabled] = useState<string>("");
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const isACounterparty = useMemo(() => type === OrganizationType.Counterparty, [type]);

  useEffect(() => {
    if (model) {
      setIsEnabled(model?.isEnabled === true ? "true" : "false");
    }
  }, [model]);

  const onSubmitHandler = (event: BaseSyntheticEvent): void => {
    event.preventDefault();
    setIsConfirmation(true);
  };

  const onCloseHandler = useCallback((): void => {
    setIsConfirmation(false);
    onClose();
  }, [onClose]);

  const submitUserStatus = useCallback(async (): Promise<void> => {
    const payload = {
      isEnabled: isEnabled === "true" ? true : false,
    };

    try {
      await api.patch<OrganizationResponse | CounterpartyResponse>(
        `admin/${isACounterparty ? "counterparties" : "organizations"}/${model?.id}`,
        payload,
      );
      enqueueSuccess(`${isACounterparty ? "Counterparty" : "Organization"} status was updated succsessfully`);
      setIsConfirmation(false);
      onSave();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        logger.error(error.response.data.message, {});
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError(`Unable update ${isACounterparty ? "counterparty" : "organization"}  status`);
    }
  }, [isEnabled, api, isACounterparty, model?.id, enqueueSuccess, onSave, enqueueError, logger]);

  const isInputChanged = useMemo(
    () => (model?.isEnabled === true ? "true" : "false") !== isEnabled,
    [isEnabled, model?.isEnabled],
  );
  const dialogHead = useMemo(
    () => (
      <Box mt={1}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          {isConfirmation ? "Please Confirm" : "Change Status"}
        </Typography>
      </Box>
    ),
    [isConfirmation],
  );

  const dialogActions = useMemo(
    () => (
      <Stack direction="row" gap={3}>
        <Button variant="text" onClick={onCloseHandler} sx={cancelBtnStyle}>
          Cancel
        </Button>

        <Maybe condition={!isConfirmation}>
          <ActionButton
            type="submit"
            form="user-status-form"
            style={{ fontWeight: 500, width: "100px", textTransform: "capitalize" }}
            isDisabled={!isInputChanged}
          >
            save
          </ActionButton>
        </Maybe>

        <Maybe condition={isConfirmation}>
          <Stack direction="row" sx={{ marginRight: "-10px" }}>
            <ActionButton
              style={{
                fontWeight: 500,
                width: "100px",
                textTransform: "capitalize",
              }}
              onClickHandler={submitUserStatus}
            >
              Save
            </ActionButton>
          </Stack>
        </Maybe>
      </Stack>
    ),
    [isConfirmation, isInputChanged, onCloseHandler, submitUserStatus],
  );

  const statusChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setIsEnabled(event.target.value);
  };

  return (
    <Dialog open={isOpen} onClose={onCloseHandler} fullWidth>
      <DialogTitle sx={{ backgroundColor: "white" }}>{dialogHead}</DialogTitle>

      <DialogContent sx={{ height: isConfirmation ? "210px" : "110px", overflow: "hidden" }}>
        <Box mt={-1} sx={{ width: "100%" }}>
          <Maybe condition={!isConfirmation}>
            <form id="user-status-form" onSubmit={onSubmitHandler}>
              <Stack direction="row" sx={{ width: "100%" }}>
                <FormControl fullWidth sx={{ marginTop: -3 }}>
                  <RadioGroup
                    aria-labelledby="user-status-group"
                    name="controlled-radio-buttons-group"
                    value={isEnabled}
                    onChange={statusChangeHandler}
                    sx={{ paddingTop: "7px" }}
                    row
                  >
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"true"}
                      control={<Radio />}
                      label={<Typography variant="body1">Enabled</Typography>}
                    />
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"false"}
                      control={<Radio />}
                      label={<Typography variant="body1">Disabled</Typography>}
                    />
                  </RadioGroup>
                </FormControl>
              </Stack>
            </form>
          </Maybe>
          <Maybe condition={isConfirmation}>
            <Box sx={isEnabled === "true" ? enabledStyle : disabledStyle}>
              <Stack direction="row" gap={1}>
                <InfoOutlinedIcon
                  sx={{ color: isEnabled === "true" ? "rgba(17, 149, 214, 1)" : "rgba(211, 47, 47, 1)" }}
                />
                <Stack direction="column" gap={2}>
                  {isEnabled === "true" ? (
                    <Typography>
                      Enabling {isACounterparty ? "counterparty" : "organization"} <b>{model?.name}</b> status will not
                      automatically restore access for its users. You must manually enable each user individually.
                    </Typography>
                  ) : (
                    <Typography>
                      Disabling {isACounterparty ? "counterparty" : "organization"} <b>{model?.name}</b> will
                      automatically revoke access for all associated users.
                    </Typography>
                  )}
                  <Typography>Do you still wish to continue?</Typography>
                </Stack>
              </Stack>
            </Box>
          </Maybe>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "right",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
