"use client";

import { CounterpartyQueryResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import UnifiedOrganizationsComponent from "./unified-organizations";

export default function Organizations({
  organizationResponse,
  counterpartiesResponse,
}: {
  organizationResponse: OrganizationResponse[];
  counterpartiesResponse: CounterpartyQueryResponse;
}): JSX.Element {
  return (
    <UnifiedOrganizationsComponent
      organizationResponse={organizationResponse}
      counterpartiesResponse={counterpartiesResponse}
    />
  );
}
