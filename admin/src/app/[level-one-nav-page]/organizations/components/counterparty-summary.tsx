import { Grid, Box, Stack, Typography } from "@mui/material";
import { DocumentType } from "@rubiconcarbon/shared-types";
import { MISSING_DATA } from "@constants/constants";
import { DocumentTypeUILabel } from "@constants/documents";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import AttachmentList from "@components/attachment-list/attachment-list";
import ItemDetails from "@components/ui/details/item-details";
import { currencyFormat, Maybe, percentageFormat } from "@rubiconcarbon/frontend-shared";
import { useMemo } from "react";
import { UnifiedOrganizationModel } from "@models/organization";
import { FeeTypeUILabel } from "../constants/fee-type";

interface CounterpartySummaryProps {
  counterparty: UnifiedOrganizationModel;
}

export default function CounterpartySummary(props: CounterpartySummaryProps): JSX.Element {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const { counterparty } = props;

  const fees = useMemo(
    () => counterparty?.counterparty?.defaultFees?.map(({ type, feeType, fee }) => ({ type, feeType, fee })),
    [counterparty?.counterparty?.defaultFees],
  );

  const buyFee = useMemo(() => fees?.find(({ type }) => type === "buy"), [fees]);
  const sellFee = useMemo(() => fees?.find(({ type }) => type === "sell"), [fees]);

  const emails = useMemo(
    () => counterparty?.counterparty?.confirmationEmails?.map(({ value }) => value),
    [counterparty?.counterparty?.confirmationEmails],
  );

  const { documents, fetching, fetch } = useDocumentsApi({
    query: {
      counterpartyId: counterparty?.id,
      types: [DocumentType.MASTER_AGREEMENT],
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!counterparty?.id) {
      setTimeout(async () => await fetch());
    }
  }, [counterparty?.id]);

  const handleDeletionSuccess = async (): Promise<void> => {
    enqueueSuccess(`Successfully deleted document for ${counterparty?.name}`);
    await fetch();
  };

  const handleDeletionError = async (error: any): Promise<void> => {
    enqueueError(`Failed to delete document for ${counterparty?.name}`);
    logger.error(`Failed to delete document for ${counterparty?.name}: ${error?.message}`, {});
  };

  return (
    <Box>
      <Grid
        container
        spacing={0}
        flexDirection={{
          xs: "column",
          md: "row",
        }}
        alignItems={{
          xs: "flex-start",
          md: "center",
        }}
      >
        <Grid container spacing={0}>
          <Grid item xs={12}>
            <ItemDetails
              label="Emails:"
              value={
                <>
                  <Maybe condition={emails?.length > 0}>
                    <Stack>
                      {emails?.map((email) => (
                        <Typography key={email} fontSize="0.875rem">
                          {email}
                        </Typography>
                      ))}
                    </Stack>
                  </Maybe>
                  <Maybe condition={emails?.length === 0}>{MISSING_DATA}</Maybe>
                </>
              }
              sx={{
                alignItems: "baseline",
              }}
            />
          </Grid>
          <Grid item container xs={12}>
            <ItemDetails
              label="Fees:"
              value={
                <>
                  <Maybe condition={!!buyFee || !!sellFee}>
                    <Grid container gap={2}>
                      <Maybe condition={!!buyFee}>
                        <Grid item xs={12} md={5.8}>
                          <ItemDetails
                            label="Buy Fee"
                            value={
                              <Stack>
                                <ItemDetails
                                  label="Fee Type:"
                                  value={<Typography fontSize="0.875rem">{FeeTypeUILabel[buyFee?.feeType]}</Typography>}
                                  sx={{
                                    alignItems: "baseline",
                                    "> div:first-child": {
                                      width: "100%",
                                      "> p": {
                                        padding: 0,
                                      },
                                    },
                                  }}
                                />
                                <ItemDetails
                                  label="Fee:"
                                  value={
                                    <Typography fontSize="0.875rem">
                                      {buyFee?.feeType === "percentage"
                                        ? percentageFormat(buyFee?.fee, { scale: 0.01 })
                                        : currencyFormat(buyFee?.fee)}
                                    </Typography>
                                  }
                                  sx={{
                                    alignItems: "baseline",
                                    "> div:first-child": {
                                      width: "100%",
                                      "> p": {
                                        padding: 0,
                                      },
                                    },
                                  }}
                                />
                              </Stack>
                            }
                            spacing={{
                              label: {
                                xs: 12,
                              },
                              value: {
                                xs: 12,
                              },
                            }}
                            sx={{
                              flexDirection: "column",
                              "> div:first-child": {
                                width: "100%",
                                "> p": {
                                  padding: "3px",
                                  backgroundColor: "lightgray",
                                },
                              },
                            }}
                          />
                        </Grid>
                      </Maybe>
                      <Maybe condition={!!sellFee}>
                        <Grid item xs={12} md={5.8}>
                          <ItemDetails
                            label="Sell Fee"
                            value={
                              <Stack>
                                <ItemDetails
                                  label="Fee Type:"
                                  value={
                                    <Typography fontSize="0.875rem">{FeeTypeUILabel[sellFee?.feeType]}</Typography>
                                  }
                                  sx={{
                                    alignItems: "baseline",
                                    "> div:first-child": {
                                      width: "100%",
                                      "> p": {
                                        padding: 0,
                                      },
                                    },
                                  }}
                                />
                                <ItemDetails
                                  label="Fee:"
                                  value={
                                    <Typography fontSize="0.875rem">
                                      {sellFee?.feeType === "percentage"
                                        ? percentageFormat(sellFee?.fee, { scale: 0.01 })
                                        : currencyFormat(sellFee?.fee)}
                                    </Typography>
                                  }
                                  sx={{
                                    alignItems: "baseline",
                                    "> div:first-child": {
                                      width: "100%",
                                      "> p": {
                                        padding: 0,
                                      },
                                    },
                                  }}
                                />
                              </Stack>
                            }
                            spacing={{
                              label: {
                                xs: 12,
                              },
                              value: {
                                xs: 12,
                              },
                            }}
                            sx={{
                              flexDirection: "column",
                              "> div:first-child": {
                                width: "100%",
                                "> p": {
                                  padding: "3px",
                                  backgroundColor: "lightgray",
                                },
                              },
                            }}
                          />
                        </Grid>
                      </Maybe>
                    </Grid>
                  </Maybe>
                  <Maybe condition={!buyFee && !sellFee}>{MISSING_DATA}</Maybe>
                </>
              }
              sx={{
                alignItems: "baseline",
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <ItemDetails
              label="Attachments:"
              value={
                <>
                  <Maybe condition={documents?.length > 0}>
                    <AttachmentList
                      attachments={documents}
                      deleteConfirmation={{
                        title: "Confirm document deletion",
                        content: ({ document }) => (
                          <span>
                            Are you sure you want to delete <strong>{DocumentTypeUILabel[document.type]}</strong> for{" "}
                            <strong>{counterparty.name}</strong>?
                          </span>
                        ),
                      }}
                      style={{ fontSize: "0.875rem" }}
                      onRemoveSuccess={handleDeletionSuccess}
                      onRemoveError={handleDeletionError}
                    />
                  </Maybe>
                  <Maybe condition={documents?.length === 0}>{MISSING_DATA}</Maybe>
                </>
              }
              sx={{
                alignItems: "baseline",
              }}
            />
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
