import {
  But<PERSON>,
  capitalize,
  Container,
  Di<PERSON>r,
  FormControl,
  FormControlLabel,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { getNewCounterPartyModel } from "../utilities/get-model-type";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import useNavigation from "@/hooks/use-navigation";
import {
  calculator,
  classcat,
  deepEqual,
  isArray,
  Maybe,
  Nullable,
  pickFromRecord,
  toBoolean,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import {
  CounterpartyCreateRequest,
  CounterpartyFeeType,
  CounterpartyResponse,
  CounterpartyUpdateRequest,
  DocumentType,
  DocumentUpdateRequest,
  DocumentUploadUrlRequest,
  TradeType,
  uuid,
} from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import COLORS from "@components/ui/theme/colors";
import InlineUploader from "@components/ui/uploader/components/InlineUploaderWidget";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useGetSetState, useToggle, useUnmount } from "react-use";
import { Fragment, MouseEvent, useCallback, useMemo, useState } from "react";
import { FileUploadHandlers } from "@/types/headless-downloader";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { toUnifiedOrganizationModel } from "../utilities/to-model";
import usePerformantState from "@/hooks/use-perfomant-state";
import { LoadingButton } from "@mui/lab";
import AttachmentList from "@components/attachment-list/attachment-list";
import { DocumentTypeUILabel } from "@constants/documents";
import { useStoreProvider } from "@providers/store-provider";
import Grid2 from "@mui/material/Unstable_Grid2";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { OrganizationType } from "@constants/organization-type.enum";
import { DefaultFee, Email, UnifiedOrganizationModel } from "@models/organization";
import { FeeTypeOptions } from "../constants/fee-type";
import { NumericFormat } from "react-number-format";

import classes from "../styles/form.module.scss";

const TradeCounterpartyModelResolver = classValidatorResolver(UnifiedOrganizationModel);

const TradeCounterpartyForm = ({
  counterpartyResponse,
}: {
  counterpartyResponse?: CounterpartyResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError, enqueueWarning, closeSnackbar } = useSnackbarVariants();
  const { updateEphemeralState } = useStoreProvider();

  const [uploading, toggleUploading] = useToggle(false);
  const [hasDocUploadError, toggleHasDocUploadError] = useToggle(false);

  const [uploadHandlers, setFileUploadHandlers] = useState<FileUploadHandlers>({});
  const [counterpartyPayload, setCounterpartyPayload] = useState<
    CounterpartyCreateRequest | CounterpartyUpdateRequest
  >();

  const [updatePayload, setUpdatePayload] = useGetSetState<DocumentUpdateRequest>();
  const [idFromCreation, setIdFromCreation] = useGetSetState<{ id: uuid }>();
  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<DocumentUploadUrlRequest>();
  const [localModel, setLocalModel] = usePerformantState<UnifiedOrganizationModel>();

  useUnmount(() => {
    updateEphemeralState("organizations.viewing", "counterparty");
  });

  const counterpartyId = counterpartyResponse?.id;

  const inEdit = useMemo(() => !!localModel?.id || !!counterpartyId, [localModel?.id, counterpartyId]);
  const hasFileToUpload = useMemo(() => Object.keys(uploadHandlers)?.length > 0, [uploadHandlers]);

  const model = useMemo(
    () =>
      inEdit
        ? localModel || { ...toUnifiedOrganizationModel(counterpartyResponse!), type: OrganizationType.Counterparty }
        : getNewCounterPartyModel(),
    [inEdit, localModel, counterpartyResponse],
  );

  const {
    control,
    formState: { errors },
    watch,
    reset,
    handleSubmit,
  } = useForm<UnifiedOrganizationModel>({
    resolver: TradeCounterpartyModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: emails,
    append,
    remove,
  } = useFieldArray<UnifiedOrganizationModel, "counterparty.confirmationEmails">({
    control,
    name: "counterparty.confirmationEmails",
  });

  const { insert: insertFee, remove: deleteFee } = useFieldArray<UnifiedOrganizationModel, "counterparty.defaultFees">({
    control,
    name: "counterparty.defaultFees",
  });

  const data = watch();
  const watchedFees = data?.counterparty?.defaultFees;

  const {
    documents = [],
    fetching,
    fetch,
    update,
    retrieveUploadLink,
  } = useDocumentsApi({
    query: {
      counterpartyId: counterpartyId as uuid,
      types: [DocumentType.MASTER_AGREEMENT],
    },
    updatePayload: updatePayload(),
    uploadLinkPayload: getUploadLinkPayload(),

    onFetchError: (error: any) => {
      enqueueError(`Unable to fetch documents for ${data?.name}.`);
      logger.error(`Unable to fetch documents for ${data?.name}: ${error?.message}`, {});
    },
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
      toggleHasDocUploadError(true);
    },
    onUpdateSuccess: () => {
      enqueueSuccess(
        `Successfully ${counterpartyId ? "updated" : "created"} orgaization${counterpartyId ? " details" : "."}`,
      );
      popFromPath(counterpartyId ? 2 : 1);
    },
    onUpdateError: (error: any) => {
      enqueueWarning("Partial upload; unable to update document association.");
      logger.error(`Partial upload; unable to update document association: ${error.message}`, {});
    },
  });

  const pure = useMemo(
    () => pickFromRecord(model, ["name", "isOnboarded", "counterparty.confirmationEmails", "counterparty.defaultFees"]),
    [model],
  );

  const impure = useMemo(
    () => pickFromRecord(data, ["name", "isOnboarded", "counterparty.confirmationEmails", "counterparty.defaultFees"]),
    [data],
  );

  const isDirty = useMemo(
    () =>
      !deepEqual(pure, impure, {
        transform: {
          isOnboarded: (value) => toBoolean(value),
        },
      }),
    [impure, pure],
  );

  const submittable = useMemo(() => ({ model: isDirty, document: hasFileToUpload }), [hasFileToUpload, isDirty]);

  const hasMultipleEmails = useMemo(() => emails.length > 1, [emails]);

  const { trigger: commitCounterparty, isMutating: committingCounterparty } = useTriggerRequest<
    CounterpartyResponse,
    CounterpartyCreateRequest | CounterpartyUpdateRequest
  >({
    url: `/admin/counterparties${inEdit ? `/${counterpartyId}` : ""}`,
    method: inEdit ? "patch" : "post",
    requestBody: counterpartyPayload,
    swrOptions: {
      onSuccess: async (data): Promise<void> => {
        setLocalModel(toUnifiedOrganizationModel(data));
        setIdFromCreation({ id: data?.id });

        if (hasFileToUpload) await submitDocument();
        else {
          enqueueSuccess(`Successfully ${inEdit ? "updated" : "created"} counterparty${inEdit ? " details" : "."}`);
          popFromPath(counterpartyId ? 2 : 1);
        }
      },
      onError: (error: { data: { message: string | string[] } }): void => {
        enqueueError(
          <Stack gap={1}>
            <Stack>
              <Typography>Unable to {inEdit ? "update" : "create"} counterparty details.</Typography>
              {isArray(error?.data?.message) ? (
                (error?.data?.message as string[])?.map((error) => <Typography key={error}>{error}</Typography>)
              ) : (
                <Typography key={error?.data?.message as string}>{error?.data?.message}</Typography>
              )}
            </Stack>
          </Stack>,
          { persist: true, className: classes.ErrorAlert },
        );
        logger.error(`Unable to ${inEdit ? "update" : "create"} counterparty details: ${error?.data?.message}`, {});
      },
    },
  });

  const disableForm = committingCounterparty || uploading;

  usePerformantEffect(() => {
    if (!!counterpartyId && !fetching) setTimeout(async () => await fetch());
  }, [counterpartyId]);

  usePerformantEffect(() => {
    if (!!counterpartyId && !!model) reset(model);
  }, [counterpartyId, model]);

  const hasTradeType = useCallback(
    (type: TradeType): boolean => !!watchedFees?.some(({ type: tradeType }) => type === tradeType),
    [watchedFees],
  );

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      counterpartyId: counterpartyId as uuid,
      filename: file.name,
      type: DocumentType.MASTER_AGREEMENT,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const handleDocumentsSubmission = async (): Promise<void> => {
    const documentUploadPromises = Object.entries(uploadHandlers).reduce(
      (accum, [inputId, handler]) => [
        ...accum,
        handler?.({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>)?.then(() =>
          handleExposeUploadHandler(inputId, null),
        ),
      ],
      [] as Promise<void>[],
    );

    await Promise.all(documentUploadPromises);
  };

  const handleExposeUploadHandler = (
    inputId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    if (handler) {
      setFileUploadHandlers((previous) => ({
        ...previous,
        [inputId]: handler,
      }));
    } else {
      setFileUploadHandlers((previous) =>
        Object.entries(previous).reduce((accum, [key, value]) => {
          if (key !== inputId)
            accum = {
              ...accum,
              [key]: value,
            };
          return accum;
        }, {}),
      );
    }
  };

  const handleFileUploadSuccess = useCallback(
    (file: File, metadata: OnFileUploadSuccessMetaData): void => {
      const id = metadata?.s3FileId as uuid;
      const filename = file?.name;

      setUpdatePayload({
        id,
        filename,
        counterpartyId: !counterpartyId ? (Object.values(idFromCreation()?.id).join("") as uuid) : model?.id,
        type: DocumentType.MASTER_AGREEMENT,
        isPublic: true,
      });

      setTimeout(async () => {
        await update();
      });
    },
    [setUpdatePayload, counterpartyId, idFromCreation, model?.id, update],
  );

  const handleFileUploadError = (): void => {
    enqueueError("Unable to upload document");
    toggleHasDocUploadError(true);
  };

  const handleFileRemoval = async (): Promise<boolean> => {
    toggleHasDocUploadError(false);
    return true;
  };

  const handleDeletionSuccess = async (): Promise<void> => {
    enqueueSuccess(`Successfully deleted document for ${model?.name}`);
    await fetch();
  };

  const handleDeletionError = async (error: any): Promise<void> => {
    enqueueError(`Failed to delete document for ${model?.name}`);
    logger.error(`Failed to delete document for ${model?.name}: ${error?.message}`, {});
  };

  const addNewEmail = useCallback((): void => {
    append(new Email());
  }, [append]);

  const removeEmail = useCallback(
    (index: number): void => {
      remove(index);
    },
    [remove],
  );

  const addFee = useCallback(
    (type: TradeType): void => {
      const fee = new DefaultFee();
      fee.type = type;

      insertFee(watchedFees?.length ?? 0, fee);
    },
    [watchedFees?.length, insertFee],
  );

  const removeFee = useCallback(
    (index: number): void => {
      deleteFee(index);
    },
    [deleteFee],
  );

  const FeeForm = useCallback(
    ({ index }: { index: 0 | 1 }): JSX.Element => (
      <Grid2 xs={12}>
        <Stack gap={2} height={120}>
          <Typography variant="body2">{capitalize(watchedFees?.at?.(index)?.type ?? "")}</Typography>
          <Stack direction="row" gap={1} alignItems="baseline">
            <Controller
              name={`counterparty.defaultFees.${index}.feeType`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  select
                  label="Calculation Method"
                  value={value ?? ""}
                  InputProps={{ ref }}
                  {...otherProps}
                  error={!!errors?.counterparty?.defaultFees?.at?.(index)?.feeType}
                  helperText={errors?.counterparty?.defaultFees?.at?.(index)?.feeType?.message}
                  fullWidth
                  disabled={disableForm}
                  sx={{ minWidth: 320 }}
                >
                  {FeeTypeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
            <Controller
              name={`counterparty.defaultFees.${index}.fee`}
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <NumericFormat
                  allowNegative={false}
                  thousandSeparator
                  decimalScale={2}
                  fixedDecimalScale
                  prefix={watchedFees?.at?.(index)?.feeType !== CounterpartyFeeType.PERCENTAGE ? "$ " : ""}
                  suffix={watchedFees?.at?.(index)?.feeType === CounterpartyFeeType.PERCENTAGE ? " %" : ""}
                  label="Fee"
                  value={value}
                  customInput={TextField}
                  InputProps={{ ref }}
                  error={!!errors?.counterparty?.defaultFees?.at?.(index)?.fee}
                  helperText={errors?.counterparty?.defaultFees?.at?.(index)?.fee?.message}
                  {...otherProps}
                  fullWidth
                />
              )}
            />
            <IconButton
              disabled={disableForm}
              sx={{
                position: "relative",
                top: 10,
                width: 35,
                height: 35,
              }}
              onClick={() => removeFee(index)}
            >
              <MatIcon variant="round" value="delete" color="action" />
            </IconButton>
          </Stack>
        </Stack>
      </Grid2>
    ),
    [control, disableForm, errors?.counterparty?.defaultFees, removeFee, watchedFees],
  );

  const submitDocument = async (): Promise<void> => {
    await handleDocumentsSubmission();
  };

  const onSubmit = (formData: UnifiedOrganizationModel): void => {
    closeSnackbar();
    toggleHasDocUploadError(false);

    if (submittable.model) {
      const payload: CounterpartyCreateRequest | CounterpartyUpdateRequest = {
        ...(inEdit ? { id: model?.id } : {}),
        name: formData.name,
        isEnabled: inEdit ? toBoolean(formData.isEnabled) : true,
        isOnboarded: toBoolean(formData.isOnboarded),
        tradeConfirmEmails: formData?.counterparty?.confirmationEmails?.map(({ value }) => value),
        defaultFees: formData?.counterparty?.defaultFees?.map(({ type, feeType, fee }) => ({
          type,
          feeType,
          [feeType.replace(/[-_]+(.)/g, (_, c: string) => c.toUpperCase()).concat("Fee")]: calculator(fee, {
            parserBlacklist: ["$", ",", " %"],
          })
            .divide(feeType === CounterpartyFeeType.PERCENTAGE ? 100 : 1)
            .calculate(),
        })),
      };

      setCounterpartyPayload(payload);
      setTimeout(async () => await commitCounterparty());
    } else if (submittable.document) setTimeout(async () => await submitDocument());
  };

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="name"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Counterparty Name"
              value={value ?? ""}
              InputProps={{ ref }}
              {...otherProps}
              error={!!errors?.name}
              helperText={errors?.name?.message}
              fullWidth
              disabled={disableForm}
            />
          )}
        />

        <Divider />

        <Stack sx={{ width: "100%" }} gap={2}>
          <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
            Fees:
          </Typography>
          <Grid2 container alignItems="center" gap={1} justifyContent="space-between">
            <Grid2 xs={12} md={5.8}>
              <Maybe condition={!hasTradeType(TradeType.BUY)}>
                <Button
                  startIcon={<MatIcon variant="round" value="add" size={34} />}
                  sx={{ height: 50 }}
                  onClick={() => addFee(TradeType.BUY)}
                  disabled={disableForm}
                >
                  <Typography variant="body2" textTransform="none">
                    Add Buy Fee
                  </Typography>
                </Button>
              </Maybe>
              <Maybe condition={hasTradeType(TradeType.BUY)}>
                <FeeForm index={watchedFees?.findIndex(({ type }) => type === TradeType.BUY) as 0 | 1} />
              </Maybe>
            </Grid2>

            <Grid2 xs={12} md={5.8}>
              <Maybe condition={!hasTradeType(TradeType.SELL)}>
                <Button
                  startIcon={<MatIcon variant="round" value="add" size={34} />}
                  sx={{ height: 50 }}
                  onClick={() => addFee(TradeType.SELL)}
                  disabled={disableForm}
                >
                  <Typography variant="body2" textTransform="none">
                    Add Sell Fee
                  </Typography>
                </Button>
              </Maybe>
              <Maybe condition={hasTradeType(TradeType.SELL)}>
                <FeeForm index={watchedFees?.findIndex(({ type }) => type === TradeType.SELL) as 0 | 1} />
              </Maybe>
            </Grid2>
          </Grid2>
        </Stack>

        <Divider />

        <Stack sx={{ width: "100%" }} gap={2}>
          <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
            Emails:
          </Typography>
          <Grid2 container alignItems="center" gap={1} rowGap={2}>
            <Maybe condition={emails.length === 0}>
              <Grid2 xs={12} md={6}>
                <Button
                  startIcon={<MatIcon variant="round" value="add" size={34} />}
                  sx={{ height: 50 }}
                  onClick={addNewEmail}
                  disabled={disableForm}
                >
                  <Typography variant="body2" textTransform="none">
                    Add Trade Confirmation Email
                  </Typography>
                </Button>
              </Grid2>
            </Maybe>
            <Maybe condition={emails.length > 0}>
              {emails.map((email, index) => {
                const isFirst = index === 0;
                const isLast = index === emails?.length - 1;
                const tackOnRemove = (hasMultipleEmails && isLast) || (!hasMultipleEmails && isFirst);
                const hasError = !!errors?.counterparty?.confirmationEmails?.at?.(index)?.value;

                return (
                  <Fragment key={email.id}>
                    <Grid2 xs={12} md={6}>
                      <Controller
                        name={`counterparty.confirmationEmails.${index}.value`}
                        control={control}
                        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                          <TextField
                            label={`${!isFirst ? "Additional " : ""}Trade Confirmation Email`}
                            value={value ?? ""}
                            InputProps={{ ref }}
                            {...otherProps}
                            error={!!errors?.counterparty?.confirmationEmails?.at?.(index)?.value}
                            helperText={errors?.counterparty?.confirmationEmails?.at?.(index)?.value?.message}
                            fullWidth
                            disabled={disableForm}
                          />
                        )}
                      />
                    </Grid2>
                    <Grid2
                      xs={tackOnRemove ? 5 : 12}
                      md={tackOnRemove ? 2 : 5}
                      sx={{ alignSelf: hasError ? "baseline" : "center" }}
                    >
                      <Button
                        startIcon={
                          <MatIcon
                            variant="round"
                            value={!isLast ? "remove" : "add"}
                            size={34}
                            sx={{
                              color: "white",
                              backgroundColor: !isLast ? COLORS.red : COLORS.rubiconGreen,
                              borderRadius: "50%",
                            }}
                          />
                        }
                        disabled={disableForm}
                        sx={{
                          height: 50,
                        }}
                        onClick={() => (!isLast ? removeEmail(index) : addNewEmail())}
                      >
                        <Typography variant="body2" textTransform="none">
                          {!isLast ? "Remove email" : "Add more emails"}
                        </Typography>
                      </Button>
                    </Grid2>
                    <Maybe condition={tackOnRemove}>
                      <Grid2
                        xs={tackOnRemove ? 5 : 12}
                        md={tackOnRemove ? 2 : 5}
                        sx={{ alignSelf: hasError ? "baseline" : "center" }}
                      >
                        <Button
                          startIcon={
                            <MatIcon
                              variant="round"
                              value="remove"
                              size={34}
                              sx={{
                                color: "white",
                                backgroundColor: COLORS.red,
                                borderRadius: "50%",
                              }}
                            />
                          }
                          disabled={disableForm}
                          sx={{
                            height: 50,
                          }}
                          onClick={() => removeEmail(index)}
                        >
                          <Typography variant="body2" textTransform="none">
                            Remove email
                          </Typography>
                        </Button>
                      </Grid2>
                    </Maybe>
                  </Fragment>
                );
              })}
            </Maybe>
          </Grid2>
        </Stack>

        <Divider />

        <Stack>
          <Stack
            className={classcat([classes.NonInputFieldSection, { [classes.Error]: !!errors?.isOnboarded }])}
            direction="row"
            sx={{ width: "100%" }}
          >
            <Typography
              className={classes.GroupLabel}
              variant="body1"
              color={COLORS.rubiconGreen}
              fontSize={15}
              fontWeight="bold"
            >
              Onboarding Status:
            </Typography>
            <Controller
              name="isOnboarded"
              control={control}
              defaultValue={data?.isOnboarded}
              render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => (
                <FormControl error={!!errors?.isOnboarded}>
                  <RadioGroup className={classes.RadioGroup} ref={ref} value={value ?? null} {...otherProps} row>
                    <FormControlLabel
                      label={<Typography variant="body1">Onboarded</Typography>}
                      value={true}
                      control={<Radio size="medium" />}
                      disabled={disableForm}
                    />
                    <FormControlLabel
                      label={<Typography variant="body1">Not Onboarded</Typography>}
                      value={false}
                      control={<Radio size="medium" />}
                      disabled={disableForm}
                    />
                  </RadioGroup>
                </FormControl>
              )}
            />
          </Stack>
          <Typography className={classes.NonInputFieldHelperText}>{errors?.isOnboarded?.message}</Typography>
        </Stack>

        <Divider />

        <Stack
          className={classcat([classes.NonInputFieldSection, { [classes.Error]: hasDocUploadError }])}
          sx={{ width: "100%" }}
          gap={2}
        >
          <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
            Agreement{documents?.length > 1 ? "s" : ""}:
          </Typography>

          <Stack gap={0.5}>
            <Maybe condition={documents.length > 0}>
              <AttachmentList
                attachments={documents}
                deleteConfirmation={{
                  title: "Confirm document deletion",
                  content: ({ document }) => (
                    <span>
                      Are you sure you want to delete <strong>{DocumentTypeUILabel[document.type]}</strong> for{" "}
                      <strong>{model.name}</strong>?
                    </span>
                  ),
                }}
                onRemoveSuccess={handleDeletionSuccess}
                onRemoveError={handleDeletionError}
              />
              <Divider sx={{ marginBottom: 1.5 }} />
            </Maybe>
            <InlineUploader
              inputId={`upload ${documents?.length ? "additional " : ""} agreement`}
              uploadLink={getS3UploadApiLink}
              uploadText={`upload ${documents?.length ? "additional " : ""} agreement`}
              clearOnFileUploadSuccess
              allowedExtensions={["application/pdf"]}
              externallyUpload
              onUploadingStatusChange={(status) => toggleUploading(status)}
              onExposeUploadHandler={handleExposeUploadHandler}
              onFileUploadSuccess={handleFileUploadSuccess}
              onFileUploadError={handleFileUploadError}
              onFileRemoval={handleFileRemoval}
              disabled={disableForm}
            />
          </Stack>

          <Typography variant="caption" fontWeight="100">
            * Accepted file type: PDF | Max file size: 15MB
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Button
            className={classes.ActionButton}
            color="error"
            disabled={disableForm}
            onClick={() => popFromPath(counterpartyId ? 2 : 1)}
          >
            Cancel
          </Button>
          <LoadingButton
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            loading={disableForm}
            disabled={disableForm || (inEdit && !isDirty && !hasFileToUpload)}
          >
            {counterpartyId ? "Update" : "Create"}
          </LoadingButton>
        </Stack>
      </Stack>
    </Container>
  );
};

export default TradeCounterpartyForm;
