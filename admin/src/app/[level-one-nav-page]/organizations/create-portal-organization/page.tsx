import { AuthorizeServer } from "@app/authorize-server";
import { OrganizationUserRole, PermissionEnum, UserResponse, UserStatus } from "@rubiconcarbon/shared-types";
import PortalOrganizationForm from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { isValidElement } from "react";

/**
 * Create Portal Organization Page
 *
 * This is a server component that renders the Create Portal Organization page
 */
export default async function CreatePortalOrganizationPage(): Promise<JSX.Element> {
  const managers = await withErrorHandling(async () =>
    baseApiRequest<UserResponse[]>(
      `admin/organizations/${process.env.NEXT_PUBLIC_ADMIN_ORGANIZATION}/users?${generateQueryParams({
        status: UserStatus.ENABLED,
        roles: [OrganizationUserRole.MANAGER],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(managers)) return managers;

  return (
    <AuthorizeServer permissions={[PermissionEnum.ORGANIZATIONS_CREATE]}>
      <PortalOrganizationForm managers={managers as UserResponse[]} />
    </AuthorizeServer>
  );
}
