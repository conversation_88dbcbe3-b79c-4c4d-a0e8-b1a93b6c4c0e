import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";

export const getNewPortalOrgModel = (): UnifiedOrganizationModel => {
  const model = new UnifiedOrganizationModel();
  model.type = OrganizationType.Portal;
  return model;
};

export const getNewCounterPartyModel = (): UnifiedOrganizationModel => {
  const model = new UnifiedOrganizationModel();
  model.type = OrganizationType.Counterparty;
  return model;
};
