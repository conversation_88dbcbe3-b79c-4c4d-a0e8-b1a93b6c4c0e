$error: #C90005;

.NonInputFieldSection {
    &.Error {
        padding: 8px;
        border-radius: 4px;
        border: solid 1px $error;
    }

    .GroupLabel {
        width: 160px;
        padding-top: 10px;
    }
    
    .FormControl {
    
        &.Agreements .FormLabel {
            margin-bottom: 15px;
        }
    
        .FormLabel {
            margin-bottom: 10px;
    
            .SectionHeader {
                font-weight: bold;
                color: #121212;
            }
        }
    
        .FormGroup {
            margin-left: 10px;
        }
    }
    
    .RadioGroup {
        width: 100%;
    } 
}

.NonInputFieldHelperText {
    font-weight: 400;
    font-size: 0.75rem;
    line-height: 1.66;
    letter-spacing: 0.03333em;
    margin-top: 3px;
    margin-right: 14px;
    margin-bottom: 0;
    margin-left: 14px;
    color: $error; // defaulted to error since that is the only use case now
} 

.ActionButton {
    border-radius: 5px;
}

.Dialog {
    min-width: 1000px;

    .Content {
        padding: 20px !important;
    }

    .Actions {
        background-color: rgb(248, 248, 248) !important;
        height: 70px;
        
        button {
            border-radius: 5px;
        }
    }
}

.Error<PERSON>lert > div > div:first-of-type {
   align-items: center;
}