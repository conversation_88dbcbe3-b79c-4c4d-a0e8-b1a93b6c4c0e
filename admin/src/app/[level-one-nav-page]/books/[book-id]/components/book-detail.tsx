import useBreadcrumbs from "@providers/breadcrumb-provider";
import { MouseEvent, useCallback, useEffect, useMemo, useState } from "react";
import { Divide<PERSON>, Stack, ToggleButton, ToggleButtonGroup, Tooltip, Typography } from "@mui/material";
import { TimeRangeEnum, TimeRangeEnumToReportingValue } from "@constants/state";
import { calculator, currencyFormat, numberFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  AdminBookResponse,
  AssetType,
  BookType,
  GroupedAllocationWithNestedResponse,
  PermissionEnum,
  PositionsReport,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { MISSING_DATA } from "@constants/constants";
import GenericTable from "@components/ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import { FileDownloadRounded, InfoRounded } from "@mui/icons-material";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import ProjectValidation, { AlertsDef } from "@components/alerts/project-validation/project-validation";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { getAssetSummarizedBookNumbers, SummarizedBookNumbers } from "@utils/helpers/portfolio/book-transformations";
import { BookTypeToLabel } from "@constants/book-type-to-label";
import { BOOK_DETAIL_COLUMNS } from "../constants/book-detail-column";
import { ExtendedAllocation } from "../../../../../types/book";
import { BookDetailModel } from "../models/book-detail";

import classes from "../styles/book-detail.module.scss";

const rctBookAlertsDef: AlertsDef = {
  rctStandard: true,
  suspended: false,
  buffer: true,
};

const complianceBookAlertsDef: AlertsDef = {
  complianceType: true,
};

const TimeRangeOptions: TimeRangeEnum[] = [
  TimeRangeEnum.ONE_DAY,
  TimeRangeEnum.ONE_WEEK,
  TimeRangeEnum.ONE_MONTH,
  TimeRangeEnum.THREE_MONTHS,
  TimeRangeEnum.ONE_YEAR,
  TimeRangeEnum.YEAR_TO_DATE,
];

const BookDetail = ({ books }: { books: AdminBookResponse[] }): JSX.Element => {
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRangeEnum>(TimeRangeEnum.ONE_DAY);

  const compositeId = useMemo(() => books?.map(({ id }) => id).join(","), [books]);
  const firstBook = useMemo(() => books?.at(0), [books]);
  const isRCTBook = useMemo(() => books?.length > 1, [books?.length]);
  const isComplianceBook = useMemo(() => firstBook?.type === BookType.COMPLIANCE_DEFAULT, [firstBook?.type]);

  const extendedAllocations = useMemo(
    () =>
      books?.reduce(
        (accum: ExtendedAllocation[], { id: bookId, type: bookType, name: bookName, ownerAllocations }) => [
          ...accum,
          ...((ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations || [])
            .filter(({ asset }) => asset?.type === AssetType.REGISTRY_VINTAGE)
            .map((allocation) => ({
              bookId,
              bookType,
              bookName,
              ...allocation,
            })),
        ],
        [],
      ),
    [books],
  );

  const vintage_ids = useMemo(
    () => Array.from(new Set(extendedAllocations?.map((allocation) => allocation?.detailedAsset?.id))),
    [extendedAllocations],
  );

  const summarizedBookNumbers = books?.reduce((numbers, book) => {
    const current = getAssetSummarizedBookNumbers(book, [AssetType.REGISTRY_VINTAGE]);

    return {
      ...numbers,
      value: calculator(numbers?.value)?.add(current.value)?.calculate()?.toNumber(),
      pending: calculator(numbers?.pending)?.add(current.pending)?.calculate()?.toNumber(),
      total: calculator(numbers?.total)?.add(current.total)?.calculate()?.toNumber(),
      holding: calculator(numbers?.holding)?.add(current.holding)?.calculate()?.toNumber(),
    };
  }, {} as SummarizedBookNumbers);

  const {
    data: postionsResponse,
    trigger: getPositions,
    isMutating: loadingPostitions,
  } = useTriggerRequest<PositionsReport[]>({
    url: "/reporting/positions-report",
    method: "post",
    requestBody: {
      vintage_ids,
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load additional data for book details");
        logger.error(
          `Unable to load additional data for book details with id${isRCTBook ? "s" : ""} ${compositeId}: ${error?.message}`,
          {},
        );
      },
    },
  });

  const { table } = useGenericTableUtility<BookDetailModel>({});

  const loading = useMemo(() => loadingPostitions, [loadingPostitions]);

  const positions = useMemo(() => postionsResponse || [], [postionsResponse]);

  const value = useMemo(
    () =>
      currencyFormat(summarizedBookNumbers?.value, {
        fallback: MISSING_DATA,
      }),
    [summarizedBookNumbers?.value],
  );

  const pending = useMemo(() => {
    const pending = summarizedBookNumbers?.pending;

    if (pending > 0) return numberFormat(pending, { prepend: "+$", decimalPlaces: 2, fallback: MISSING_DATA });
    else if (pending === 0) return MISSING_DATA;
    else return numberFormat(Math.abs(pending), { prepend: "-$", decimalPlaces: 2, fallback: MISSING_DATA });
  }, [summarizedBookNumbers?.pending]);

  const total = useMemo(
    () => currencyFormat(summarizedBookNumbers?.total, { fallback: MISSING_DATA }),
    [summarizedBookNumbers?.total],
  );

  const holding = useMemo(
    () => numberFormat(summarizedBookNumbers?.holding, { fallback: MISSING_DATA }),
    [summarizedBookNumbers?.holding],
  );

  const breadcrumbTitle = useMemo(
    () => (isRCTBook ? BookTypeToLabel[BookType.PORTFOLIO_DEFAULT] : firstBook?.name),
    [isRCTBook, firstBook?.name],
  );

  usePerformantEffect(() => {
    if (vintage_ids?.length) setTimeout(async () => await getPositions());
  }, [vintage_ids?.length]);

  useEffect(() => {
    if (breadcrumbTitle) updateBreadcrumbName?.("Book Detail", `${breadcrumbTitle} Book Detail`);
  }, [breadcrumbTitle, updateBreadcrumbName]);

  const toRowModel = useCallback(
    (data: ExtendedAllocation): GenericTableRowModel<BookDetailModel> => {
      const { bookId, bookName, detailedAsset } = data;
      const { project, name: vName } = (detailedAsset as TrimmedProjectVintageResponse) || {};
      const { registryProjectId, name: pName, projectType } = project || {};
      const { type } = projectType || {};

      const position = positions?.find((position) => {
        const { registry_project_id, project_name, project_type, vintage } = position || {};
        return (
          registry_project_id === registryProjectId &&
          project_name === pName &&
          project_type === type &&
          vintage?.toString() === vName
        );
      }) as Pick<
        PositionsReport,
        "registry_project_id" | "project_name" | "project_type" | "vintage" | "buffer_category_name"
      >;

      return {
        id: `${bookId}-${detailedAsset?.id}`,
        allocation: isRCTBook ? bookName : "",
        change: position?.[`change_${TimeRangeEnumToReportingValue[selectedTimeRange]}`],
        changePercentage: position?.[`perc_change_${TimeRangeEnumToReportingValue[selectedTimeRange]}`],
        ...data,
        ...position,
      };
    },
    [isRCTBook, positions, selectedTimeRange],
  );

  const handleTimeRangeChange = (event: MouseEvent<HTMLButtonElement>, range: TimeRangeEnum): void => {
    event.preventDefault();
    if (range) {
      setSelectedTimeRange(range);
    }
  };

  return (
    <Stack gap={3}>
      {/* Header Info */}
      <Stack className={classes.SubHeader} direction="row" alignItems="center" gap={1}>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography className={classes.Text} variant="body2">
            Value: {value}
          </Typography>
          <Tooltip title="Total value of credits (settled)">
            <InfoRounded fontSize="small" />
          </Tooltip>
          <Divider className={classes.Divider} orientation="vertical" />
        </Stack>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography className={classes.Text} variant="body2">
            Pending: {pending}
          </Typography>
          <Tooltip title="Net pending buy and/or sell">
            <InfoRounded fontSize="small" />
          </Tooltip>
          <Divider className={classes.Divider} orientation="vertical" />
        </Stack>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography className={classes.Text} variant="body2">
            Total: {total}
          </Typography>
          <Tooltip title="Value + Net pending buy and/or sell">
            <InfoRounded fontSize="small" />
          </Tooltip>
          <Divider className={classes.Divider} orientation="vertical" />
        </Stack>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography className={classes.Text} variant="body2">
            Holding: {holding} {holding === MISSING_DATA ? "" : "credits"}
          </Typography>
          <Tooltip title="Quantity of credits held">
            <InfoRounded fontSize="small" />
          </Tooltip>
        </Stack>
      </Stack>
      <Maybe condition={isRCTBook}>
        <ProjectValidation extendedAllocations={extendedAllocations} entity="Book" alertsDef={rctBookAlertsDef} />
      </Maybe>
      <Maybe condition={isComplianceBook}>
        <ProjectValidation
          extendedAllocations={extendedAllocations}
          entity="Book"
          alertsDef={complianceBookAlertsDef}
        />
      </Maybe>
      {/* Table */}
      <GenericTable
        id="Book Details"
        loading={loading}
        columns={BOOK_DETAIL_COLUMNS}
        globalSearch={{
          searchKeys: [
            "allocation",
            "project_name",
            "registry_project_id",
            "vintage",
            "detailedAsset.project.projectType.type",
            "detailedAsset.project.country.name",
            "amountAllocated",
            "currentPrice",
            "change",
            "changePercentage",
            "detailedAsset.averageCostBasis",
            "detailedAsset.riskBufferPercentage",
          ],
        }}
        sort={{
          sorts: {
            amountAllocated: "desc",
          },
        }}
        pageableData={{
          data: extendedAllocations,
          page: {
            offset: 0,
            limit: 25,
            size: extendedAllocations?.length || 0,
            totalCount: extendedAllocations?.length || 0,
          },
        }}
        toRowModel={toRowModel}
        export={{
          filename: `Book-Details-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.BOOKS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loading || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
        ]}
        toolbarItems={
          <ToggleButtonGroup
            className={classes.TimeRange}
            exclusive
            color="primary"
            value={selectedTimeRange}
            aria-label="book detail time range"
            onChange={handleTimeRangeChange}
            disabled={loading}
          >
            {TimeRangeOptions.map((range) => (
              <ToggleButton
                key={range}
                className={classes.ToggleButton}
                value={range}
                classes={{
                  selected: classes.Selected,
                }}
              >
                {range}
              </ToggleButton>
            ))}
          </ToggleButtonGroup>
        }
      />
    </Stack>
  );
};

export default BookDetail;
