import { CompareArrowsRounded, FileDownloadRounded } from "@mui/icons-material";
import {
  AssetType,
  GroupingParentQueryResponse,
  GroupingParentResponse,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import useNavigation from "@/hooks/use-navigation";
import { Stack } from "@mui/material";
import { calculator } from "@rubiconcarbon/frontend-shared";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import GenericTable from "@components/ui/generic-table";
import { BOOKS_COLUMNS } from "../constants/books-columns";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import BookValidation from "@components/alerts/book-validation/book-validation";
import { BookParentGrouping } from "../../../../types/book";

const Books = ({ booksByParentResponse }: { booksByParentResponse: GroupingParentQueryResponse }): JSX.Element => {
  const { pushToPath } = useNavigation();

  const { table } = useGenericTableUtility<BookParentGrouping>({});

  const toRowModel = (grouping: GroupingParentResponse): GenericTableRowModel<BookParentGrouping> => {
    // only need vintage asset type
    const vintageAllocation = grouping?.ownerAllocationsByAssetType?.find(
      ({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE,
    );

    return {
      compositeId: grouping?.books?.map(({ id }) => id)?.join(","),
      ...grouping,
      ownerAllocationsByAssetType: vintageAllocation ? [vintageAllocation] : [],
      total: calculator(vintageAllocation?.groupedPrices?.totalPriceAllocated)
        .add(vintageAllocation?.groupedPrices?.totalPricePendingBuy)
        .subtract(vintageAllocation?.groupedPrices?.totalPricePendingSell)
        .calculate()
        .toNumber(),
    };
  };

  return (
    <Stack gap={1}>
      <BookValidation books={booksByParentResponse?.data} />
      <GenericTable
        id="Books"
        columns={BOOKS_COLUMNS}
        pageableData={booksByParentResponse}
        sort={{
          sorts: {
            total: "desc",
          },
        }}
        toRowModel={toRowModel}
        export={{
          filename: `Book-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.BOOKS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: !booksByParentResponse?.data?.length,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Transfer Assets",
            startIcon: <CompareArrowsRounded />,
            requiredPermission: PermissionEnum.TRANSFERS_EXECUTE,
            onClickHandler: () => pushToPath("/transfer-assets"),
          },
        ]}
      />
    </Stack>
  );
};

export default Books;
