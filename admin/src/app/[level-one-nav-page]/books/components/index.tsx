"use client";

import { GroupingParentQueryResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import BooksComponent from "./books";

const Books = ({ booksByParentResponse }: { booksByParentResponse: GroupingParentQueryResponse }): JSX.Element => {
  return (
    <Page>
      <BooksComponent booksByParentResponse={booksByParentResponse} />
    </Page>
  );
};

export default Books;
