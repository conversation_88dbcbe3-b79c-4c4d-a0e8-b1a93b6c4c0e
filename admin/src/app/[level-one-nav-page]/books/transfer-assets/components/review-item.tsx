import { MISSING_DATA } from "@constants/constants";
import { Box, Stack } from "@mui/material";
import { ReactNode } from "react";

type ReviewItemProps = {
  label: ReactNode;
  value: ReactNode;
  fallbackValue?: ReactNode;
};

const ReviewItem = ({ label, value, fallbackValue = MISSING_DATA }: ReviewItemProps): JSX.Element => {
  return (
    <Stack direction="row">
      <Box fontWeight="bold" minWidth={150}>
        {label}
      </Box>
      <Box width="100%">{value || fallbackValue}</Box>
    </Stack>
  );
};

export default ReviewItem;
