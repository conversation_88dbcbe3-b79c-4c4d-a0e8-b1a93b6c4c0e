"use client";

import { GroupingParentResponse } from "@rubiconcarbon/shared-types";
import Page from "@components/layout/containers/page";
import TransferAssetsComponent from "./transfer-assets";

const TransferAssets = ({ booksByParents }: { booksByParents: GroupingParentResponse[] }): JSX.Element => {
  return (
    <Page>
      <TransferAssetsComponent booksByParents={booksByParents} />
    </Page>
  );
};

export default TransferAssets;
