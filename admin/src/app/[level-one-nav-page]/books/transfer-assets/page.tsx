import { AuthorizeServer } from "@app/authorize-server";
import {
  BookRelations,
  GroupingParentQueryResponse,
  GroupingParentResponse,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import TransferAssets from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Transfer Assets Page
 *
 * This is a server component that renders the Transfer Assets page
 */
export default async function TransferAssetsPage(): Promise<JSX.Element> {
  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<GroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_NESTED,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.TRANSFERS_EXECUTE]}>
      <TransferAssets
        booksByParents={(booksByParentResponse as GroupingParentQueryResponse)?.data as GroupingParentResponse[]}
      />
    </AuthorizeServer>
  );
}
