import { AuthorizeServer } from "@app/authorize-server";
import { BookRelations, GroupingParentQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import Books from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Books Page
 *
 * This is a server component that renders the Books page
 */
export default async function BooksPage(): Promise<JSX.Element> {
  const booksByParentResponse = await withErrorHandling(async () =>
    baseApiRequest<GroupingParentQueryResponse>(
      `admin/books/parents?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        isEnabled: true,
        includeTotalCount: true,
        includeRelations: [
          BookRelations.OWNER_ALLOCATIONS,
          BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
          BookRelations.PRICES,
        ],
      })}`,
    ),
  );

  // Check if the result is a server error
  if (isValidElement(booksByParentResponse)) return booksByParentResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.BOOKS_READ]}>
      <Books booksByParentResponse={booksByParentResponse as GroupingParentQueryResponse} />
    </AuthorizeServer>
  );
}
