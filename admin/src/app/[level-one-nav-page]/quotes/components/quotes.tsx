import React, { useState, useEffect, useCallback, useContext, useMemo } from "react";
import {
  Stack,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
} from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import { PermissionEnum, uuid, QuoteQueryResponse, QuoteResponse } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import SearchAppBar from "@components/ui/search/enhanced-search";
import AddIcon from "@mui/icons-material/Add";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import { NumericFormat } from "react-number-format";
import CancelIcon from "@mui/icons-material/Cancel";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { isEmpty, isFinite } from "lodash";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import currencyFormat from "@/utils/formatters/currency-format";
import { Maybe, Undefinable, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { CsvBuilder } from "filefy";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { Quote, mapQuote } from "@models/quote";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs, { Dayjs } from "dayjs";
import resetTimeInDate from "@/utils/formatters/reset-time-in-date";
import { useLogger } from "@providers/logging";
import ConfirmationModal, { ButtonDef } from "@components/ui/dialogs/confirmation-dialog";
import YearRangePicker from "@components/ui/year-range/year-range";
import DialogTheme from "@components/ui/dialogs/dialog-themes";

const buildDateRange = (dateFrom: Dayjs, dateTo: Dayjs): Undefinable<string> => {
  if (dateFrom?.isValid() && dateTo?.isValid()) {
    return `${dayjs(dateFrom).year()} - ${dayjs(dateTo).year()}`;
  } else if (dateFrom?.isValid()) {
    return `${dayjs(dateFrom).year()}`;
  } else if (dateTo?.isValid()) {
    return `${dayjs(dateTo).year()}`;
  }
};

const formatNumber = (input: string): string => {
  return (+input).toLocaleString();
};

const extractDatesFromRange = (input: string): DateRange => {
  if (isEmpty(input)) {
    return {};
  }

  const splitInput = input.split("-");
  if (!!splitInput && splitInput.length === 2 && isFinite(+splitInput[0].trim()) && isFinite(+splitInput[1].trim())) {
    return {
      from: new Date(`01/01/${splitInput[0].trim()}`),
      to: new Date(`12/31/${splitInput[1].trim()}`),
    };
  }

  if (isFinite(+input)) {
    return {
      from: new Date(`01/01/${input}`),
    };
  }

  return {};
};

const formatCellVAlue = (colDef: ColDef): string => {
  if (!colDef?.formatter) {
    return colDef?.value?.toString() ?? MISSING_DATA;
  }
  return colDef.formatter.func(colDef.value!);
};

interface DateRange {
  from?: Date;
  to?: Date;
}

interface IDisplayFormatter {
  func: (input: string | number | Date | boolean | Dayjs | Map<string, string | number | Date>) => any;
  inputFields?: (string | number | Date)[];
  overrideMissingDataDisplay?: boolean;
}

interface IExportFormatter {
  func: (input: string | number | Date | boolean | Dayjs | Map<string, string | number | Date>) => any;
}

interface ColDef {
  id: string;
  type: "date" | "string" | "number" | "date-range";
  value: Undefinable<string | number | Date | Dayjs>;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  decimalScale?: number;
  prefix?: string;
  helperText: string;
  error: boolean;
  formatter?: IDisplayFormatter;
}

interface IFormField {
  error?: boolean;
  message?: string;
}

interface StringFormField extends IFormField {
  value?: string;
}

interface DateFormField extends IFormField {
  value?: Dayjs;
}

interface QuoteRow {
  id?: uuid;
  from?: StringFormField;
  registryName?: StringFormField;
  registryProjectId?: StringFormField;
  projectName?: StringFormField;
  vintageInterval?: StringFormField;
  amount?: StringFormField;
  price?: StringFormField;
  expirationDate?: DateFormField;
}

interface ExportColDef {
  name: string;
  displayName: string;
  formatter?: IExportFormatter;
}

const exportColumns: ExportColDef[] = [
  {
    name: "from",
    displayName: "Counterparty",
  },
  {
    name: "registryName",
    displayName: "Registry",
  },
  {
    name: "registryProjectId",
    displayName: "Project ID",
  },
  {
    name: "projectName",
    displayName: "Project Name",
  },
  {
    name: "vintageInterval",
    displayName: "Vintage",
  },
  {
    name: "amount",
    displayName: "Volume",
    formatter: { func: formatNumber },
  },
  {
    name: "price",
    displayName: "Price",
    formatter: { func: currencyFormat as any },
  },
  {
    name: "expirationDate",
    displayName: "Expired On",
    formatter: { func: dateFormatterEST },
  },
  {
    name: "updatedAt",
    displayName: "Last Updated",
    formatter: { func: dateFormatterEST },
  },
  {
    name: "updatedBy",
    displayName: "Updated By",
  },
];

const columns = [
  "Counterparty",
  "Registry",
  "Project ID",
  "Project Name",
  "Vintage",
  "Volume",
  "Price",
  "Expired On",
  "Last Updated",
  "Updated By",
  "Actions",
];

const isRowContainsValue = (row: Quote, searchString: string): boolean => {
  if (
    row.projectName?.toUpperCase().includes(searchString) ||
    row.registryProjectId?.toUpperCase().includes(searchString) ||
    row.registryName?.toUpperCase().includes(searchString) ||
    row.from?.toUpperCase().includes(searchString) ||
    row.vintageInterval?.toString().includes(searchString) ||
    row.amount?.toString().includes(searchString) ||
    row.price?.toString().includes(searchString) ||
    row.updatedBy?.toUpperCase().includes(searchString)
  )
    return true;

  return false;
};

const getEmptyQuoteRow = (): QuoteRow => {
  return {
    from: { value: "" },
    registryName: { value: "" },
    registryProjectId: { value: "" },
    projectName: { value: "" },
    vintageInterval: { value: "" },
    amount: { value: "" },
    price: { value: "" },
    expirationDate: {},
  };
};

const isValidRow = (quoteRow: QuoteRow): boolean => {
  for (const [, value] of Object.entries(quoteRow)) {
    if (value.error) return false;
  }

  return true;
};

const validateStringInput = (input?: StringFormField): StringFormField => {
  let newValue: StringFormField = {};
  if (isEmpty(input?.value)) {
    newValue = {
      error: true,
      message: "value is required",
    };
  } else {
    newValue = {
      value: input!.value,
      error: false,
      message: "",
    };
  }

  return newValue;
};

const validateRow = (row: QuoteRow): QuoteRow => {
  const validatedRow = { ...row };
  validatedRow.projectName = validateStringInput(row?.projectName);
  validatedRow.registryProjectId = validateStringInput(row.registryProjectId);
  return validatedRow;
};

interface QuoteTableRow extends Quote {
  hidden?: boolean;
}

const newRowStyle = {
  verticalAlign: "top",
};

const itemStyle = {
  paddingTop: "10px",
};

export function convertStringToNumber(input: string): number {
  return +input.replaceAll(",", "").replaceAll("$", "");
}

function Row(props: {
  row: Quote;
  idx: uuid;
  editIdx?: uuid;
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  deleteHandler: (id: uuid) => void;
  submitEditRow: (updatedRow: QuoteRow) => void;
}): JSX.Element {
  const { row, idx, editIdx, startEditing, stopEditing, deleteHandler, submitEditRow } = props;

  const [quoteRow, setQuoteRow] = useState<QuoteRow>();
  const [initialValue, setInitialValue] = useState<Quote>();

  useEffect(() => {
    setQuoteRow({
      id: row.id,
      from: { value: row?.from },
      registryName: { value: row?.registryName },
      registryProjectId: { value: row.registryProjectId },
      projectName: { value: row.projectName },
      vintageInterval: { value: row?.vintageInterval },
      amount: { value: row?.amount?.toString() ?? "" },
      price: { value: row?.price?.toString() ?? "" },
      expirationDate: { value: row.expirationDate ? dayjs(row.expirationDate) : undefined },
    });

    setInitialValue(row);
  }, [startEditing, row]);

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const name = event.target.name;
      const row = { ...quoteRow };
      row[name].value = value;
      setQuoteRow(row);
    },
    [quoteRow],
  );

  const editRowDef: ColDef[] = useMemo(
    () => [
      {
        id: "from",
        type: "string",
        value: quoteRow?.from?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.from?.message ?? "",
        error: !!quoteRow?.from?.error,
      },
      {
        id: "registryName",
        type: "string",
        value: quoteRow?.registryName?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.registryName?.message ?? "",
        error: !!quoteRow?.registryName?.error,
      },
      {
        id: "registryProjectId",
        type: "string",
        value: quoteRow?.registryProjectId?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.registryProjectId?.message ?? "",
        error: !!quoteRow?.registryProjectId?.error,
      },
      {
        id: "projectName",
        type: "string",
        value: quoteRow?.projectName?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.projectName?.message ?? "",
        error: !!quoteRow?.projectName?.error,
      },
      {
        id: "vintageInterval",
        type: "date-range",
        value: quoteRow?.vintageInterval?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.vintageInterval?.message ?? "",
        error: !!quoteRow?.vintageInterval?.error,
      },
      {
        id: "amount",
        type: "number",
        decimalScale: 0,
        value: quoteRow?.amount?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.amount?.message ?? "",
        error: !!quoteRow?.amount?.error,
        formatter: { func: formatNumber },
      },
      {
        id: "price",
        type: "number",
        decimalScale: 2,
        prefix: "$",
        value: quoteRow?.price?.value ?? "",
        onChange: onChangeHandler,
        helperText: quoteRow?.price?.message ?? "",
        error: !!quoteRow?.price?.error,
        formatter: { func: currencyFormat as any },
      },
      {
        id: "expirationDate",
        type: "date",
        value: quoteRow?.expirationDate?.value ?? undefined,
        onChange: onChangeHandler,
        helperText: quoteRow?.expirationDate?.message ?? "",
        error: !!quoteRow?.expirationDate?.error,
        formatter: { func: dateFormatterEST },
      },
    ],
    [quoteRow, onChangeHandler],
  );

  const cancelEditHandler = (): void => {
    stopEditing();
    setQuoteRow({
      id: initialValue?.id,
      from: { value: initialValue?.from },
      registryName: { value: initialValue?.registryName },
      registryProjectId: { value: initialValue?.registryProjectId },
      projectName: { value: initialValue?.projectName },
      vintageInterval: { value: initialValue?.vintageInterval },
      amount: { value: initialValue?.amount?.toString() ?? "" },
      price: { value: initialValue?.price?.toString() ?? "" },
      expirationDate: { value: initialValue?.expirationDate ? dayjs(initialValue.expirationDate) : undefined },
    });
  };

  const currentlyEditing = idx === editIdx;

  const dateErrorHandler = useCallback(
    (validationError) => {
      if (validationError) {
        setQuoteRow({
          ...(quoteRow ?? {}),
          expirationDate: {
            ...(quoteRow ?? {}).expirationDate,
            error: true,
            message: "Invalid date",
          },
        });
      }
    },
    [quoteRow],
  );

  const dateRangeHandler = useCallback(
    (dateFrom: Dayjs, dateTo: Dayjs): void => {
      setQuoteRow({
        ...quoteRow,
        vintageInterval: {
          value: buildDateRange(dateFrom, dateTo),
          error: false,
          message: "",
        },
      });
    },
    [quoteRow],
  );

  const dateChangeHandler = useCallback(
    (value, fieldName: string): void => {
      const row = { ...quoteRow };
      row[fieldName] = {
        value,
        error: false,
        message: "",
      };
      setQuoteRow(row);
    },
    [quoteRow],
  );

  const submitRowHandler = (): void => {
    const validatedRow = validateRow(quoteRow ?? {});
    setQuoteRow(validatedRow);
    if (isValidRow(validatedRow)) {
      submitEditRow(quoteRow!);
    }
  };

  const showDateRange = (dateRangeInput: string): JSX.Element => {
    const dateRange = extractDatesFromRange(dateRangeInput);
    return (
      <YearRangePicker dateFrom={dayjs(dateRange.from)} dateTo={dayjs(dateRange.to)} onChange={dateRangeHandler} />
    );
  };

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        {editRowDef.map((colDef) => {
          switch (colDef.type) {
            case "string":
              return (
                <TableCell align="left" key={colDef.id}>
                  {currentlyEditing ? (
                    <TextField
                      id={colDef.id}
                      name={colDef.id}
                      size="small"
                      type="text"
                      value={colDef.value ?? ""}
                      onChange={onChangeHandler}
                      inputProps={{
                        maxLength: 256,
                        style: { fontSize: 14, minWidth: "100px" },
                      }}
                      helperText={colDef.helperText}
                      error={colDef.error}
                    />
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {colDef.value?.toString() ?? MISSING_DATA}
                    </Typography>
                  )}
                </TableCell>
              );
            case "date":
              return (
                <TableCell key={colDef.id}>
                  {currentlyEditing ? (
                    <DatePicker
                      views={["year", "month", "day"]}
                      format="DD/MM/YYYY"
                      sx={{
                        marginBottom: "5px",
                      }}
                      slotProps={{
                        textField: {
                          required: true,
                          size: "small",
                          helperText: colDef.helperText,
                          sx: { width: "155px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
                        },
                      }}
                      value={colDef.value}
                      onChange={(e) => dateChangeHandler(e, colDef.id)}
                      onError={dateErrorHandler}
                    />
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {colDef.value?.toString() ? dateFormatterEST(colDef.value.toString()) : MISSING_DATA}
                    </Typography>
                  )}
                </TableCell>
              );
            case "number":
              return (
                <TableCell align="left" key={colDef.id}>
                  {currentlyEditing ? (
                    <NumericFormat
                      size="small"
                      defaultValue={0}
                      value={convertStringToNumber(colDef?.value?.toString() ?? "")}
                      name={colDef.id}
                      decimalScale={colDef.decimalScale ?? 0}
                      prefix={colDef.prefix ?? ""}
                      inputProps={{
                        maxLength: 11,
                        style: { fontSize: 14, width: "100px" },
                      }}
                      allowNegative={false}
                      customInput={TextField}
                      type="text"
                      thousandSeparator={","}
                      helperText={colDef.helperText}
                      error={colDef.error}
                      onChange={onChangeHandler}
                    />
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {formatCellVAlue(colDef)}
                    </Typography>
                  )}
                </TableCell>
              );
            case "date-range":
              return (
                <TableCell align="left" key={colDef.id}>
                  {currentlyEditing ? (
                    showDateRange(colDef?.value?.toString() ?? "")
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {colDef.value?.toString() ?? MISSING_DATA}
                    </Typography>
                  )}
                </TableCell>
              );
            default:
              throw new Error("unsupported field type");
          }
        })}
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {dateFormatterEST(row?.updatedAt?.toString() ?? "") ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.updatedBy ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell>
          {currentlyEditing ? (
            <Stack direction="row" gap={2}>
              <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={submitRowHandler}>
                <CheckIcon />
              </IconButton>
              <IconButton
                sx={{ marginLeft: "1px", color: COLORS.rubiconGreen }}
                edge="start"
                onClick={cancelEditHandler}
              >
                <CancelIcon />
              </IconButton>
            </Stack>
          ) : (
            <Stack direction="row" gap={2}>
              <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={startEditing.bind(this, idx)}>
                <CreateIcon />
              </IconButton>
              <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={deleteHandler.bind(this, row.id)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          )}
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

export default function Quotes({
  quotesResponse: serverQuotesResponse,
}: {
  quotesResponse: QuoteQueryResponse;
}): JSX.Element {
  const { enqueueError, enqueueSuccess } = useSnackbarVariants();
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);

  const [editIdx, setEditIdx] = useState<uuid>();
  const [quotes, setQuotes] = useState<QuoteTableRow[]>([]);
  const [showNewRow, setShowNewRow] = useState<boolean>(false);
  const [quoteRow, setQuoteRow] = useState<QuoteRow>(getEmptyQuoteRow());
  const [isConfirmationOpen, setIsConfirmationOpen] = useState<boolean>(false);
  const [selectedQuote, setSelectedQuote] = useState<uuid>();

  const { data: quotesResponse, trigger: refreshQuotes } = useTriggerRequest<QuoteQueryResponse>({
    url: "/admin/quotes",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
    },
    optimisticData: serverQuotesResponse,
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load quotes");
        logger.error(`Unable to load quotes: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    if (quotesResponse) {
      setQuotes(mapQuote(quotesResponse.data));
    }
  }, [quotesResponse]);

  const startEditingHandler = (idx: uuid): void => {
    setEditIdx(idx);
  };

  const stopEditingHandler = (): void => {
    setEditIdx(undefined);
  };

  const deleteHandler = (quoteId: uuid): void => {
    setSelectedQuote(quoteId);
    setIsConfirmationOpen(true);
  };

  const cancelNewRowHandler = (): void => {
    setQuoteRow(getEmptyQuoteRow());
    setShowNewRow(false);
  };

  const submitNewQuote = useCallback(async () => {
    const validatedRow = validateRow(quoteRow);
    setQuoteRow(validatedRow);

    if (isValidRow(validatedRow)) {
      const payload = {
        amount: quoteRow.amount?.value ? convertStringToNumber(quoteRow.amount?.value) : undefined,
        expirationDate: quoteRow.expirationDate?.value
          ? resetTimeInDate(quoteRow.expirationDate.value.toDate())
          : undefined,
        from: quoteRow.from?.value ? quoteRow.from.value : undefined,
        price: quoteRow.price?.value ? convertStringToNumber(quoteRow.price.value) : undefined,
        projectName: quoteRow?.projectName?.value ?? "",
        registryProjectId: quoteRow?.registryProjectId?.value ?? "",
        vintageInterval: quoteRow?.vintageInterval?.value ?? "",
        registryName: quoteRow?.registryName?.value ?? "",
      };

      try {
        await api.post<QuoteResponse>("admin/quotes", payload);
        enqueueSuccess("Successfully added new quote");
        setShowNewRow(false);
        refreshQuotes();
        setQuoteRow(getEmptyQuoteRow());
      } catch {
        enqueueError("Unable to add new quote");
      }
    }
  }, [quoteRow, api, enqueueError, enqueueSuccess, refreshQuotes]);

  const getFilteredData = useCallback(
    (input: string) => {
      const searchString = input.toUpperCase();
      const allQuotes = quotes.map((quote) => {
        if (isRowContainsValue(quote, searchString)) {
          quote.hidden = false;
        } else {
          quote.hidden = true;
        }
        return quote;
      });

      setQuotes(allQuotes);
    },
    [quotes],
  );

  const onSearchChangeHandler = (value: string): void => {
    getFilteredData(value);
  };

  const addQuoteHandler = (): void => {
    setShowNewRow(true);
  };

  const formatRowValue = (row: QuoteTableRow, exportColDef: ExportColDef): string => {
    if (!row[exportColDef.name]) return MISSING_DATA;

    if (exportColDef?.formatter) {
      return exportColDef.formatter.func(row[exportColDef.name]);
    }

    return row[exportColDef.name];
  };

  const exportHandler = (): void => {
    const columns = exportColumns?.map((colDef) => colDef.displayName);
    const rows = quotes.map((row) => exportColumns.map((colDef) => formatRowValue(row, colDef)));
    const csvBuilder = new CsvBuilder("export_quotes.csv");
    csvBuilder.setColumns(columns).addRows(rows).exportFile();
  };

  const onConfirmDeleteQuote = useCallback(
    async (id: uuid) => {
      setIsConfirmationOpen(false);
      try {
        await api.delete<QuoteResponse>(`admin/quotes/${id}`);
        enqueueSuccess("Successfully deleted selected quote");
        refreshQuotes();
      } catch {
        enqueueError("Unable to delete selected quote");
      }
    },
    [setIsConfirmationOpen, enqueueSuccess, refreshQuotes, enqueueError, api],
  );

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmDeleteQuote(selectedQuote!),
      tooltip: "proceed with delete",
    },
  ];

  const onChangeHandler = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>): void => {
      const value = event.target.value;
      const name = event.target.name;
      const row = { ...quoteRow };
      row[name].value = value;
      setQuoteRow(row);
    },
    [quoteRow],
  );

  const dateChangeHandler = useCallback(
    (value, fieldName: string): void => {
      const row = { ...quoteRow };
      row[fieldName] = {
        value,
        error: false,
        message: "",
      };
      setQuoteRow(row);
    },
    [quoteRow],
  );

  const dateRangeHandler = useCallback(
    (dateFrom: Dayjs, dateTo: Dayjs): void => {
      setQuoteRow({
        ...quoteRow,
        vintageInterval: {
          value: buildDateRange(dateFrom, dateTo),
          error: false,
          message: "",
        },
      });
    },
    [quoteRow],
  );

  const newRowDef: ColDef[] = [
    {
      id: "from",
      type: "string",
      value: quoteRow?.from?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.from?.message ?? "",
      error: !!quoteRow?.from?.error,
    },
    {
      id: "registryName",
      type: "string",
      value: quoteRow?.registryName?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.registryName?.message ?? "",
      error: !!quoteRow?.registryName?.error,
    },
    {
      id: "registryProjectId",
      type: "string",
      value: quoteRow?.registryProjectId?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.registryProjectId?.message ?? "",
      error: !!quoteRow?.registryProjectId?.error,
    },
    {
      id: "projectName",
      type: "string",
      value: quoteRow?.projectName?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.projectName?.message ?? "",
      error: !!quoteRow?.projectName?.error,
    },
    {
      id: "vintageInterval",
      type: "date-range",
      value: quoteRow?.vintageInterval?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.vintageInterval?.message ?? "",
      error: !!quoteRow?.vintageInterval?.error,
    },
    {
      id: "amount",
      type: "number",
      decimalScale: 0,
      value: quoteRow?.amount?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.amount?.message ?? "",
      error: !!quoteRow?.amount?.error,
    },
    {
      id: "price",
      type: "number",
      decimalScale: 2,
      prefix: "$",
      value: quoteRow?.price?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.price?.message ?? "",
      error: !!quoteRow?.price?.error,
    },
    {
      id: "expirationDate",
      type: "date",
      value: quoteRow?.expirationDate?.value,
      onChange: onChangeHandler,
      helperText: quoteRow?.expirationDate?.message ?? "",
      error: !!quoteRow?.expirationDate?.error,
    },
  ];

  const dateErrorHandler = useCallback(
    (validationError) => {
      if (validationError) {
        setQuoteRow({
          ...quoteRow,
          expirationDate: {
            ...quoteRow.expirationDate,
            error: true,
            message: "Invalid date",
          },
        });
      }
    },
    [quoteRow],
  );

  const submitEditRow = useCallback(
    async (updatedRow: QuoteRow) => {
      const validatedRow = validateRow(updatedRow);
      if (isValidRow(validatedRow)) {
        const payload = {
          amount: updatedRow.amount?.value ? convertStringToNumber(updatedRow.amount?.value) : undefined,
          expirationDate: updatedRow.expirationDate?.value
            ? resetTimeInDate(updatedRow.expirationDate.value.toDate())
            : undefined,
          from: updatedRow.from?.value !== "" ? updatedRow?.from?.value : null,
          price: updatedRow.price?.value ? convertStringToNumber(updatedRow.price.value) : undefined,
          projectName: updatedRow?.projectName?.value,
          registryProjectId: updatedRow?.registryProjectId?.value,
          vintageInterval: updatedRow.vintageInterval?.value !== "" ? updatedRow?.vintageInterval?.value : null,
          registryName: updatedRow.registryName?.value !== "" ? updatedRow.registryName?.value : null,
        };

        try {
          await api.patch<QuoteResponse>(`admin/quotes/${updatedRow.id}`, payload);
          enqueueSuccess("Successfully updated quote");
          setEditIdx(undefined);
          refreshQuotes();
        } catch {
          enqueueError("Unable to update quote");
        }
      }
    },
    [api, enqueueError, enqueueSuccess, refreshQuotes],
  );

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      <SearchAppBar onChangeHandler={onSearchChangeHandler}>
        <Stack direction="row" gap={0.5}>
          <ActionButton
            onClickHandler={addQuoteHandler}
            startIcon={<AddIcon />}
            requiredPermission={PermissionEnum.QUOTES_WRITE}
          >
            Add
          </ActionButton>
          <ActionButton
            onClickHandler={exportHandler}
            startIcon={<FileDownloadIcon />}
            requiredPermission={PermissionEnum.QUOTES_READ}
            tooltip="Export table data to csv file"
            style={{
              borderColor: "rgba(0, 0, 0, 0.23)",
              color: "rgba(0, 0, 0, 0.54)",
              backgroundColor: "rgba(255, 255, 255, 1)",
              ontWeight: 600,
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 1)",
                boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
              },
            }}
          >
            Export
          </ActionButton>
        </Stack>
      </SearchAppBar>
      <TableContainer>
        <Table aria-label="quotes table" stickyHeader>
          <TableHead sx={{ backgroundColor: COLORS.tableHeader }}>
            <TableRow>
              {columns?.map((column, idx) => (
                <TableCell key={`${column}-${idx}`}>
                  <Typography variant="body2" component="h4" fontWeight="700">
                    {column}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            <Maybe condition={showNewRow}>
              <TableRow sx={newRowStyle}>
                {newRowDef.map((rowDef) => {
                  switch (rowDef.type) {
                    case "string":
                      return (
                        <TableCell align="left" key={rowDef.id}>
                          <TextField
                            id={rowDef.id}
                            name={rowDef.id}
                            size="small"
                            type="text"
                            value={rowDef.value}
                            onChange={onChangeHandler}
                            inputProps={{
                              maxLength: 256,
                              style: { fontSize: 14, minWidth: "100px" },
                            }}
                            helperText={rowDef.helperText}
                            error={rowDef.error}
                          />
                        </TableCell>
                      );
                    case "date":
                      return (
                        <TableCell key={rowDef.id}>
                          <DatePicker
                            views={["year", "month", "day"]}
                            format="DD/MM/YYYY"
                            sx={{
                              marginBottom: "5px",
                            }}
                            slotProps={{
                              textField: {
                                required: true,
                                size: "small",
                                helperText: rowDef.helperText,
                                sx: { width: "155px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
                              },
                            }}
                            value={rowDef?.value ?? null}
                            onChange={(e) => dateChangeHandler(e, rowDef.id)}
                            onError={dateErrorHandler}
                          />
                        </TableCell>
                      );
                    case "number":
                      return (
                        <TableCell align="left" key={rowDef.id}>
                          <NumericFormat
                            size="small"
                            defaultValue={0}
                            value={convertStringToNumber(rowDef?.value?.toString() ?? "")}
                            name={rowDef.id}
                            decimalScale={rowDef.decimalScale ?? 0}
                            prefix={rowDef.prefix ?? ""}
                            inputProps={{
                              maxLength: 11,
                              style: { fontSize: 14, width: "100px" },
                            }}
                            allowNegative={false}
                            customInput={TextField}
                            type="text"
                            thousandSeparator={","}
                            helperText={rowDef.helperText}
                            error={rowDef.error}
                            onChange={onChangeHandler}
                          />
                        </TableCell>
                      );
                    case "date-range":
                      return (
                        <TableCell align="left" key={rowDef.id}>
                          <YearRangePicker
                            dateFrom={quoteRow?.expirationDate?.value}
                            dateTo={quoteRow?.expirationDate?.value}
                            onChange={dateRangeHandler}
                          />
                        </TableCell>
                      );
                    default:
                      throw new Error("unsupported field type");
                  }
                })}
                <TableCell></TableCell>
                <TableCell></TableCell>
                <TableCell>
                  <Stack direction="row" gap={2}>
                    <IconButton sx={{ color: "rgba(0, 0, 0, 0.56)" }} edge="start" onClick={submitNewQuote}>
                      <CheckIcon />
                    </IconButton>
                    <IconButton sx={{ color: "rgba(0, 0, 0, 0.56)" }} edge="start" onClick={cancelNewRowHandler}>
                      <DeleteIcon />
                    </IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            </Maybe>
            {quotes.map(
              (row) =>
                !row.hidden && (
                  <Row
                    key={row.id}
                    row={row}
                    idx={row.id}
                    editIdx={editIdx}
                    startEditing={startEditingHandler}
                    stopEditing={stopEditingHandler}
                    deleteHandler={deleteHandler}
                    submitEditRow={submitEditRow}
                  />
                ),
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <ConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          You are about to delete the quote.
        </Typography>
      </ConfirmationModal>
    </Paper>
  );
}
