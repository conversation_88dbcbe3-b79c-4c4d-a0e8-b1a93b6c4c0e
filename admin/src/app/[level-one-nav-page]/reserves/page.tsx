import { AuthorizeServer } from "@app/authorize-server";
import {
  AdminBookResponse,
  BookRelations,
  OrganizationResponse,
  PermissionEnum,
  ReserveQueryResponse,
  ReserveRelation,
} from "@rubiconcarbon/shared-types";
import Reserves from "./components";
import { withErrorHandling } from "@app/data-server";
import { baseApiRequest, generateQueryParams } from "@app/libs/server";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { isValidElement } from "react";

/**
 * Reserves Page
 *
 * This is a server component that renders the Reserves page
 */
export default async function ReservesPage(): Promise<JSX.Element> {
  const reservesResponse = await withErrorHandling(async () =>
    baseApiRequest<ReserveQueryResponse>(
      `admin/reserves?${generateQueryParams({
        limit: SERVER_PAGINATION_LIMIT,
        includeTotalCount: true,
        includeRelations: [ReserveRelation.PROJECT_VINTAGE, ReserveRelation.ORGANIZATION],
      })}`,
    ),
  );

  const defaultPortofolioResponse = await withErrorHandling(async () =>
    baseApiRequest<AdminBookResponse>(
      `admin/books/${process.env.NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT}?${generateQueryParams({
        includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED],
      })}`,
    ),
  );

  const organizationsResponse = await withErrorHandling(async () =>
    baseApiRequest<OrganizationResponse[]>("admin/organizations"),
  );

  // Check if the result is a server error
  if (isValidElement(reservesResponse)) return reservesResponse;
  if (isValidElement(defaultPortofolioResponse)) return defaultPortofolioResponse;
  if (isValidElement(organizationsResponse)) return organizationsResponse;

  return (
    <AuthorizeServer permissions={[PermissionEnum.RESERVES_READ]}>
      <Reserves
        reservesResponse={reservesResponse as ReserveQueryResponse}
        defaultPortofolioResponse={defaultPortofolioResponse as AdminBookResponse}
        organizationsResponse={organizationsResponse as OrganizationResponse[]}
      />
    </AuthorizeServer>
  );
}
