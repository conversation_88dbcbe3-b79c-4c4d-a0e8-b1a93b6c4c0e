import { isNothing } from "@rubiconcarbon/frontend-shared";
import { AdminProjectVintageResponse, AllocationResponse, BookType } from "@rubiconcarbon/shared-types";

export const amountAllocatedToPortfolio = (vintage: AdminProjectVintageResponse): number => {
  // @kofi should this just be available too?
  return (
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_PUBLIC)?.totalAmountAllocated ||
      0) +
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_CUSTOM)?.totalAmountAllocated ||
      0)
  );
};

export const amountUnallocatedByVintage = (vintage: AdminProjectVintageResponse): number => {
  // @kofi should these be available only or allocated?
  // amountAllocatedPortfolioDefault +
  //   amountAllocatedOpportunisticDefault +
  //   amountAllocatedRehabilitationDefault +
  //   amountAllocatedComplianceDefault -
  //   amountPendingSell -
  //   amountPendingPurchase -
  //   amountPendingCustomerTransferOutflow; /* todo : @kofi to double check this, why is retirement not here? */

  return (
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.AGED_DEFAULT)?.totalAmountAvailable ||
      0) +
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.COMPLIANCE_DEFAULT)
      ?.totalAmountAvailable || 0) +
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.OPPORTUNISTIC_DEFAULT)
      ?.totalAmountAvailable || 0) +
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_DEFAULT)
      ?.totalAmountAvailable || 0) +
    (vintage?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.REHABILITATION_DEFAULT)
      ?.totalAmountAvailable || 0)
  );
};

export const amountUnallocatedByBook = (vintage: AllocationResponse): number => {
  const {
    amountAllocated = 0,
    amountPendingPurchase,
    amountPendingRetirement,
    amountPendingCustomerTransferOutflow,
    amountPendingSell = 0,
  } = vintage || {};

  return (
    amountAllocated -
    (amountPendingPurchase + amountPendingRetirement + amountPendingSell + amountPendingCustomerTransferOutflow)
  );
};

export const pendingSellByVintageAssetAllocation = (vintage: AdminProjectVintageResponse): number => {
  if (isNothing(vintage?.assetAllocationsByBookType, ["array"])) return 0;

  return vintage?.assetAllocationsByBookType.reduce(
    (accumulator, currentValue) =>
      accumulator +
      (!!currentValue && (currentValue?.totalAmountPendingSell ?? 0) + (currentValue?.totalAmountPendingPurchase ?? 0)),
    0,
  );
};
