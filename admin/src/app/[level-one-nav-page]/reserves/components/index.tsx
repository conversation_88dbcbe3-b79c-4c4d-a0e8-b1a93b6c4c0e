"use client";

import Page from "@components/layout/containers/page";
import ReservesComponent from "./reserves";
import { AdminBookResponse, OrganizationResponse, ReserveQueryResponse } from "@rubiconcarbon/shared-types";

export default function Reserves({
  reservesResponse,
  defaultPortofolioResponse,
  organizationsResponse,
}: {
  reservesResponse: ReserveQueryResponse;
  defaultPortofolioResponse: AdminBookResponse;
  organizationsResponse: OrganizationResponse[];
}): JSX.Element {
  return (
    <Page>
      <ReservesComponent
        reservesResponse={reservesResponse}
        defaultPortofolioResponse={defaultPortofolioResponse}
        organizationsResponse={organizationsResponse}
      />
    </Page>
  );
}
