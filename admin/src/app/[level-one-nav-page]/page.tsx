import { AuthorizeServer } from "@app/authorize-server";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import LevelOneNav from "./components";

/**
 * Level One Navigation Page
 *
 * This is a server component that renders the level one navigation page
 */
export default function LevelOneNavPage(): JSX.Element {
  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <LevelOneNav />
    </AuthorizeServer>
  );
}
