import COLORS from "@/components/ui/theme/colors";
import { uuid } from "@rubiconcarbon/shared-types";

export interface PortfolioElement {
  color: string;
}

export const PortfolioMapping = new Map<uuid, PortfolioElement>([
  [
    uuid("f4cf07b2-04ba-4ee9-94b3-d1f860fe8974"),
    {
      color: COLORS.green,
    },
  ],
  [
    uuid("3b50b813-c390-4935-b8a6-2d812509bb07"),
    {
      color: COLORS.blue,
    },
  ],
  [
    uuid("0a22034b-0ba2-4eec-a8d6-c9bbc6429ba1"),
    {
      color: COLORS.orange,
    },
  ],
]);
