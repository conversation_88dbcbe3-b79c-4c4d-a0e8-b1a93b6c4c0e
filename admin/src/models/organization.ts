import { OrganizationType } from "@constants/organization-type.enum";
import {
  CounterpartyResponse,
  CounterpartyFeeType,
  OrganizationResponse,
  TradeType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { IsEmail, IsNotEmpty, ValidateIf, ValidateNested } from "class-validator";

class AccountManager {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: string;

  name: string;

  email: string;
}

export class Email {
  constructor() {}

  @IsEmail({}, { message: "Must be in email format" })
  @IsNotEmpty({ message: "Required" })
  value: string;
}

export class DefaultFee {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  type: TradeType;

  @IsNotEmpty({ message: "Required" })
  feeType: CounterpartyFeeType;

  @IsNotEmpty({ message: "Required" })
  fee: string; // decimal

  // "fee" will be set to one of the following based on the "feeType" before sending request
  fixedFee?: string; // decimal
  perTonneFee?: string; // decimal
  percentageFee?: string; // decimal
}

export class TradeCounterpartyOnly {
  constructor() {}

  @ValidateNested()
  @Type(() => Email)
  confirmationEmails: Email[] = [];

  @ValidateNested()
  @Type(() => DefaultFee)
  defaultFees: DefaultFee[] = [];
}

export class UnifiedOrganizationModel
  implements Pick<OrganizationResponse | CounterpartyResponse, "id" | "name" | "isOnboarded" | "createdAt">
{
  constructor() {}

  id: uuid = undefined;

  @IsNotEmpty({ message: "Required" })
  type?: OrganizationType = undefined;

  @IsNotEmpty({ message: "Required" })
  name: string = "";

  @ValidateIf((o: UnifiedOrganizationModel) => o?.type === OrganizationType.Portal)
  @IsNotEmpty({ message: "Required" })
  salesforceIdentifier?: string = "";

  isEnabled: boolean = undefined;

  @IsNotEmpty({ message: "Required" })
  isOnboarded: boolean = undefined;

  @ValidateIf((o: UnifiedOrganizationModel) => o?.type === OrganizationType.Portal)
  @ValidateNested()
  @Type(() => AccountManager)
  @IsNotEmpty({ message: "Required" })
  rubiconManager?: AccountManager = undefined;

  createdAt: Date = undefined;

  @ValidateNested()
  @ValidateIf((o: UnifiedOrganizationModel) => o.type === OrganizationType.Counterparty)
  @Type(() => TradeCounterpartyOnly)
  counterparty?: TradeCounterpartyOnly = new TradeCounterpartyOnly();

  statusF?: string; // represents the status of the organization

  onboardingStatusF?: string; // represents the onboarding status of the organization

  createdAtF?: string; // represents the created date in "MM/DD/YYYY" format so it can be compared and sorted
}
