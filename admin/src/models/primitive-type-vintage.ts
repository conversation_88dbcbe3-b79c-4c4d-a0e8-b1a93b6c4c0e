import "@utils/reflect-metadata";
import { DateRange, TrimmedProjectVintageResponse, uuid } from "@rubiconcarbon/shared-types";
import { IsNotEmpty, ValidateNested } from "class-validator";
import Decimal from "decimal.js";
import { PrimitiveTypeProject } from "./primitive-type-project";
import { Type } from "class-transformer";

export class PrimitiveTypeVintage implements TrimmedProjectVintageResponse {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid = undefined;

  averageCostBasis?: Decimal;

  highBufferPercentage?: Decimal;

  interval?: string | DateRange;

  lowBufferPercentage?: Decimal;

  riskBufferPercentage?: Decimal;

  name: string;

  @ValidateNested()
  @Type(() => PrimitiveTypeProject)
  project: PrimitiveTypeProject;

  createdAt: Date;

  updatedAt?: Date;
}
