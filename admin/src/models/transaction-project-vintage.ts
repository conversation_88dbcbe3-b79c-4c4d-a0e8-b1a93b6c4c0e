import { uuid, RetirementLink, VintageAssetResponse } from "@rubiconcarbon/shared-types";
import { isNil } from "lodash";

export interface TransactionProjectVintage {
  id: uuid;
  projectName?: string;
  registryProjectId?: string;
  projectType?: string;
  location?: string;
  vintageName?: string;
  interval: string;
  amountTransacted: number;
  buffer?: number;
  costBasis?: number;
  latestPrice?: number;
  amountUnallocated?: number;
  links?: RetirementLink[];
  suspended?: boolean;
  rctStandard?: boolean;
  isScienceTeamApproved?: boolean;
}

export function mapTransactionProjectVintageData(inputData: VintageAssetResponse[] = []): TransactionProjectVintage[] {
  return inputData.map((row) => ({
    id: row?.projectVintage?.id,
    projectName: row?.projectVintage?.project?.name,
    registryProjectId: row?.projectVintage?.project?.registryProjectId,
    projectType: row?.projectVintage?.project?.projectType?.type,
    location: row?.projectVintage?.project?.country?.name,
    vintageName: row?.projectVintage?.name,
    interval: row?.projectVintage?.interval?.toString(),
    buffer: row?.projectVintage?.riskBufferPercentage ? +row?.projectVintage?.riskBufferPercentage : null,
    costBasis: !isNil(row?.projectVintage?.averageCostBasis) ? +row?.projectVintage?.averageCostBasis : null,
    amountTransacted: row.amount,
    links: row?.links,
    suspended: row?.projectVintage?.project?.suspended,
    rctStandard: row?.projectVintage?.project?.rctStandard,
    isScienceTeamApproved: row?.projectVintage?.project?.isScienceTeamApproved,
  }));
}
