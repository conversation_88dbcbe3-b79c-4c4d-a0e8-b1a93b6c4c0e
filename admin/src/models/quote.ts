import { uuid, QuoteResponse } from "@rubiconcarbon/shared-types";

export interface Quote {
  id: uuid;
  from?: string;
  registryName?: string;
  registryProjectId: string;
  projectName: string;
  vintageInterval?: string;
  amount?: number;
  price?: number;
  expirationDate?: Date;
  updatedAt?: Date;
  updatedBy: string;
}

export function mapQuote(inputData: QuoteResponse[] = []): Quote[] {
  return inputData.map((row) => ({
    id: row.id,
    from: row.from,
    registryName: row.registryName,
    registryProjectId: row.registryProjectId,
    projectName: row.projectName,
    vintageInterval: row?.vintageInterval?.toString(),
    amount: row.amount,
    price: +(row.price ?? 0),
    expirationDate: row?.expirationDate,
    updatedAt: row?.updatedAt,
    updatedBy: row.updatedBy.name,
  }));
}
