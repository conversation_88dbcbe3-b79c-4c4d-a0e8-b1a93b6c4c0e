import { uuid, OrganizationResponse, PurchaseFlowType } from "@rubiconcarbon/shared-types";

export interface OrganizationData {
  id: uuid;
  organizationName: string;
  accountManagerName: string;
  salesforceIdentifier?: string;
  rialtoCustomerIdentifier?: string;
  createdAt: Date;
  accountManagerEmail?: string;
  purchaseFlowTypes: PurchaseFlowType[];
  status: boolean;
  isOnboarded: boolean;
}

export function mapOrganizationsData(inputData: OrganizationResponse[] = []): OrganizationData[] {
  return inputData.map((row) => ({
    id: row.id,
    organizationName: row.name,
    accountManagerName: row.rubiconManager?.name,
    salesforceIdentifier: row.salesforceIdentifier,
    createdAt: row.createdAt,
    accountManagerEmail: row.rubiconManager?.email,
    purchaseFlowTypes: row.purchaseFlowTypes,
    status: row.isEnabled,
    isOnboarded: row.isOnboarded,
  }));
}
