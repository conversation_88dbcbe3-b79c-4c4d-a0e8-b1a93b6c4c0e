import Decimal from "decimal.js";
import {
  uuid,
  AdminBookResponse,
  AdminProjectResponse,
  GroupedAllocationWithNestedResponse,
  TrimmedProjectVintageResponse,
  AllocationResponse,
  AssetType,
} from "@rubiconcarbon/shared-types";
import { isEmpty, isNil } from "lodash";
import { Undefinable } from "@rubiconcarbon/frontend-shared";

export const getBufferCategory = (projectId: Undefinable<uuid>, projects?: AdminProjectResponse[]): string => {
  if (!isEmpty(projects) && !!projectId) {
    return projects?.find((p) => p.id === projectId)?.bufferCategory?.name ?? "";
  }
  return "";
};

export default interface ProjectVintage {
  id: uuid;
  projectName: string;
  projectId: uuid;
  projectRegistryId?: string;
  vintageName: string;
  quantity: number;
  basketChartDisplayGroup: string;
  amountAvailable?: number;
  amountUnallocated?: number;
  projectType?: string;
  projectLocation?: string;
  buffer?: number;
  bufferCategory?: string;
  averageCostBasis?: Decimal;
  latestPrice?: Decimal;
  latestTraderPrice?: Decimal;
  portfolioAllocation?: string;
  suspended?: boolean;
  rctStandard?: boolean;
  isScienceTeamApproved?: boolean;
}
export const mapBasketComponentToVintages = (
  // todo (portfolios) book issue fix
  // input: BasketResponseWithRelations,
  input: any,
  // todo (portfolios) book issue fix

  latestPrices: Record<string, Decimal> = {},
  latestTraderPrices: Record<string, Decimal> = {},
  totalQuantity: number = 0,
): ProjectVintage[] => {
  const result: ProjectVintage[] = input.basketComponents?.map((component) => ({
    id: component.projectVintageId,
    projectName: component.projectVintage.project.name,
    projectId: component.projectVintage.project.id,
    projectRegistryId: component.projectVintage.project.registryProjectId,
    vintageName: component.projectVintage.name,
    originalQuantity: component.amountAllocated,
    quantity: component.amountAllocated,

    // todo: books issue fix
    // amountUnallocated: component.projectVintage.amountUnallocated,
    // amountAvailable: component.projectVintage.amountAllocated + component.projectVintage.amountAvailable,
    amountUnallocated: 0,
    amountAvailable: 0,
    // todo: books issue fix

    basketChartDisplayGroup: component.projectVintage.project.basketChartDisplayGroup,
    projectLocation: component?.projectVintage?.project?.country?.name,
    buffer: !isNil(component?.projectVintage?.riskBufferPercentage)
      ? +component?.projectVintage?.riskBufferPercentage * 100
      : null,
    projectType: component?.projectVintage?.project?.projectType?.type,
    averageCostBasis: component?.projectVintage?.averageCostBasis,
    latestPrice: latestPrices?.[component.projectVintageId],
    latestTraderPrice: latestTraderPrices?.[component.projectVintageId],
    portfolioAllocation: totalQuantity
      ? `${Number(((component.amountAllocated / totalQuantity) * 100).toFixed(2))}%`
      : null,
    suspended: component?.projectVintage?.project?.suspended,
    rctStandard: component?.projectVintage?.project?.rctStandard,
  }));
  return result;
};

// todo : @kofi are we sure this is only called iwth vintages? if not we should maybe filter for only vintages or throw if there are RCTs?
export const mapBookVintagesToProjectVintages = (
  input: AdminBookResponse,
  latestTraderPrices: Record<string, Decimal> = {},
  totalQuantity: number = 0,
  projects: AdminProjectResponse[] = [],
): ProjectVintage[] => {
  if (!(input.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations) {
    console.error("must have ownerAllocations.allocations to get projectVintage details");
  }

  const allocations = (input.ownerAllocations as GroupedAllocationWithNestedResponse).allocations?.filter(
    (allocation) => allocation?.asset?.type === AssetType.REGISTRY_VINTAGE,
  );
  const result: ProjectVintage[] = allocations.map((allocation: AllocationResponse) => {
    const projectVintage = allocation.detailedAsset as TrimmedProjectVintageResponse;
    return {
      id: projectVintage.id,
      projectName: projectVintage?.project?.name ?? "",
      projectId: projectVintage?.project?.id as uuid,
      projectRegistryId: projectVintage?.project?.registryProjectId,
      vintageName: projectVintage.name,
      originalQuantity: allocation.amountAllocated,
      quantity: allocation.amountAllocated,
      amountUnallocated: 0,
      amountAvailable: allocation.amountAvailable,
      basketChartDisplayGroup: "",
      projectLocation: projectVintage?.project?.country?.name,
      buffer: !isNil(projectVintage.riskBufferPercentage) ? +projectVintage.riskBufferPercentage : undefined,
      bufferCategory: getBufferCategory(projectVintage?.project?.id, projects),
      projectType: projectVintage?.project?.projectType?.type,
      averageCostBasis: allocation.averageCostBasis,
      latestTraderPrice: latestTraderPrices?.[projectVintage.id],
      portfolioAllocation: totalQuantity
        ? `${Number(((allocation.amountAllocated / totalQuantity) * 100).toFixed(2))}%`
        : undefined,
      suspended: projectVintage.project?.suspended,
      rctStandard: projectVintage.project?.rctStandard,
      isScienceTeamApproved: projectVintage.project?.isScienceTeamApproved,
    };
  });
  return result;
};
