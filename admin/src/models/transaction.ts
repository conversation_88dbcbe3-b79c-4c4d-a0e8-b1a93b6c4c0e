import "@utils/reflect-metadata";
import {
  PurchaseFlowType,
  RetirementStatus,
  RetirementType,
  TradeType,
  TransactionType,
  VintageAssetResponse,
  type AssetType,
  type TradeUpdatableStatus,
  type TransactionResponse,
  type TrimmedBookResponse,
  uuid,
  BookType,
  TrimmedProjectVintageResponse,
  CounterpartyRole,
} from "@rubiconcarbon/shared-types";
import { Transform, Type } from "class-transformer";
import { ArrayNotEmpty, IsDate, IsNotEmpty, Min, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { isNothing, MaxOfField, toNumber } from "@rubiconcarbon/frontend-shared";
import { TransactionStatus, TransactionUpdateStatus } from "@constants/transaction-status";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";
import { TIFEnum } from "@constants/tif.enum";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

export enum AllTransactionType {
  PURCHASE = TransactionType.PURCHASE, // sales
  BUY = TradeType.BUY, // trade
  SELL = TradeType.SELL, // trade
  RETIREMENT = RetirementType.RETIREMENT, // retirement
  TRANSFER_OUTFLOW = RetirementType.TRANSFER_OUTFLOW, // retirement
}

export type TrimmedTransactionModel = {
  typeF: string;
  productF: string;
  updatedAtF: string;
} & TransactionResponse;

export class Counterparty {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid = undefined;

  name: string = undefined;

  isOnboarded: boolean = undefined;

  tradeConfirmEmails: string[] = [];

  isEnabled: boolean = undefined;

  createdAt?: Date = undefined;
}

export class TradeCounterparty {
  constructor() {}

  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => Counterparty)
  counterparty: Counterparty = undefined;

  @IsNotEmpty({ message: "Required" })
  role: CounterpartyRole = undefined;

  isPrimary: boolean = false;

  comments?: string = undefined;
}

export class NonTradeAssetDetails {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid = undefined; // id is either from portfolio or project vintage

  sourceId?: uuid = undefined; // id of source book

  type: "portfolio" | "vintage";

  sourceType?: BookType = undefined; // type or source book

  name: string = undefined; // name is either from portfolio or project vintage

  sourceName?: string; // name of source book

  available: number;

  projectName?: string;

  registryProjectId?: string;

  wholeAsset?: TrimmedBookResponse | TrimmedProjectVintageResponse; // added for suspended and rct eligibilty tags
}

export class AssetOrder {
  constructor() {}

  _id: uuid = uuid(); // for list rendering performance only

  isASale?: boolean = false; // either trade or customer sales

  isAPurchase?: boolean = false; // customer sales

  @MaxOfField<AssetOrder>("amountAvailable", { message: "Quantity cannot exceed credits available" })
  @Min(1, { message: "Quantity must be greater than 0." })
  @IsNotEmpty({ message: "Required." })
  @Transform(({ value }) => (!isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: [","] }) : undefined))
  @ValidateIf(
    (o: AssetOrder) =>
      // it is a trade           // OR // [ it is a purchase or a non sale ] AND [it has non trade asset stuff] //
      (o.isASale && !o.isAPurchase) || ((o.isAPurchase || !o.isASale) && !!o?.supplementaryAssetDetails),
  )
  amount: string = undefined; // number

  @ValidateIf((o: AssetOrder) => o.isASale && !o.rawPrice)
  @IsNotEmpty({ message: "Required if Sub Total is empty." })
  @Transform(({ value }) =>
    !isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: ["$", ","] }) : undefined,
  )
  unitPrice?: string = undefined; // decimal

  @ValidateIf((o: AssetOrder) => o.isASale && !o.unitPrice)
  @IsNotEmpty({ message: "Required if Price is empty." })
  @Transform(({ value }) =>
    !isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: ["$", ","] }) : undefined,
  )
  rawPrice?: string = undefined; // decimal

  serviceFee?: string = ""; // decimal

  otherFee?: string = ""; // decimal

  // should be derived
  feeTotal?: string = undefined; // decimal

  // should be derived
  grandTotal?: string = undefined; // decimal

  amountAvailable?: number = undefined;

  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => NonTradeAssetDetails)
  @ValidateIf((o: AssetOrder) => !o?.projectVintage)
  supplementaryAssetDetails?: NonTradeAssetDetails = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeBook)
  @ValidateIf((o: AssetOrder) => o?.isASale)
  source: PrimitiveTypeBook = undefined;

  rct?: TrimmedBookResponse = undefined;

  @ValidateNested()
  @Type(() => PrimitiveTypeVintage)
  @ValidateIf((o: AssetOrder) => !o?.isAPurchase)
  projectVintage?: PrimitiveTypeVintage = undefined;

  associatedVintages?: VintageAssetResponse[];
}

export class TradeOnly {
  constructor() {}

  @ValidateNested()
  @ArrayNotEmpty()
  @Type(() => TradeCounterparty)
  counterparties: TradeCounterparty[] = undefined;

  totalValue: number = undefined;

  mtm?: string = undefined; // decimal

  @IsNotEmpty({ message: "Required." })
  tif?: TIFEnum = undefined;

  poid?: string = undefined;

  /**
   * tif = EOD (today's date at 23:59:99 PM)
   * tif = GTC (empty date)
   */
  goodUntilDate?: string = undefined; // date
}

export class SalesOnly {
  constructor(isTraderPreFill?: boolean) {
    this.isTraderPreFill = isTraderPreFill;
    if (isTraderPreFill) {
      this.flowType = PurchaseFlowType.PURCHASE_TO_RETIRE;
    }
  }

  isTraderPreFill?: boolean = false;

  totalValue: number = undefined;

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse = undefined;

  @IsNotEmpty({ message: "Required" })
  flowType: PurchaseFlowType = undefined;

  needsRiskAdjustment: boolean = undefined;

  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o: SalesOnly) => !!o?.customerPortfolio)
  productType?: AssetType = undefined;

  @ValidateIf((o: SalesOnly) => !!o?.paymentDueDate)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @Type(() => Date)
  paymentDueDate?: string = undefined; // date;
}

export class RetirementOnly {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse = undefined;

  @IsNotEmpty({ message: "Required" })
  @Type(() => Boolean)
  isPublic: boolean = undefined;

  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o: RetirementOnly) => !!o?.customerPortfolio)
  productType?: AssetType = undefined;

  @IsNotEmpty({ message: "Required" })
  beneficiary: string = undefined;
}

export class TransferOutflowOnly {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse = undefined;

  registryAccount: string = undefined;
}

export class TransactionModel {
  constructor(isTraderPreFill?: boolean) {
    if (isTraderPreFill) this.sale = new SalesOnly(isTraderPreFill);
  }

  id?: uuid = undefined;

  uiKey: string = undefined;

  amount: number = undefined;

  @IsNotEmpty({ message: "Required." })
  type?: AllTransactionType = undefined;

  product?: string;

  @ValidateNested()
  @ArrayNotEmpty()
  @Type(() => AssetOrder)
  assets?: AssetOrder[] = undefined;

  @ValidateIf((o: TransactionModel) => [TradeType.BUY, TradeType.SELL, TransactionType.TRADE].includes(o.type as any))
  @ValidateNested()
  @Type(() => TradeOnly)
  trade?: TradeOnly = undefined;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.PURCHASE)
  @ValidateNested()
  @Type(() => SalesOnly)
  sale?: SalesOnly = undefined;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.RETIREMENT)
  @ValidateNested()
  @Type(() => RetirementOnly)
  retirement?: RetirementOnly = undefined;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.TRANSFER_OUTFLOW)
  @ValidateNested()
  @Type(() => TransferOutflowOnly)
  transfer?: TransferOutflowOnly = undefined;

  @ValidateIf((o: TransactionModel) => !!o?.id)
  @IsNotEmpty({ message: "Required." })
  status: RetirementStatus | TransactionStatus | TransactionUpdateStatus | "paid" | "delivered" =
    TransactionUpdateStatus.FIRM;

  currentStatus: TransactionStatus = undefined;

  isDelivered?: boolean;

  isPaid?: boolean;

  updatableStatusOrder?: TradeUpdatableStatus[] = undefined;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.RETIREMENT)
  @IsNotEmpty({ message: "Required." })
  memo: string = "";

  updatedAt?: string = undefined; // date

  dateStarted?: string = undefined; // date

  dateFinished?: string = undefined; // date

  inStatusAmend?: boolean = undefined;

  docsCount: number;
}

export class TransactionTableFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => TransactionModel)
  amends: TransactionModel[];
}
