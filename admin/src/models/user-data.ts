import { uuid, AdminUserResponse } from "@rubiconcarbon/shared-types";

export interface UserData {
  id: uuid;
  companyName?: string;
  email: string;
  name: string;
  phone?: string;
  status: string;
  organizationId?: uuid;
  organizationName?: string;
  createdAt: Date;
  updatedAt: Date;
  organizationRole: string;
  roles: string;
  lastLogin?: Date;
}

export function mapUsersData(inputData: AdminUserResponse[] = []): UserData[] {
  return inputData.map((row) => ({
    id: row.id,
    name: row.name,
    organizationId: row?.organization?.id,
    organizationName: row?.organization?.name,
    companyName: row.companyName,
    organizationRole: row.organization?.userRoles[0],
    roles: row.roles?.join(", "),
    email: row.email,
    phone: row.phone,
    status: row.status?.toString(),
    createdAt: row.createdAt,
    updatedAt: row.updatedAt,
    lastLogin: row?.lastLogin,
  }));
}
