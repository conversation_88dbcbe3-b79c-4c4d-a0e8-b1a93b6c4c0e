import { BookType } from "@rubiconcarbon/shared-types";

export const MISSING_DATA: string = "-";
export const MISSING_CURRENCY_VALUE: string = "$0.00";
export const MISSING_COST_BASIS_VALUE: string = "$0.0000";
export const EST_TIME_ZONE = "America/New_York";
export const UTC_TIME_ZONE = "UTC";
export const DATE_FORMAT = "M/d/yyyy";
export const TIME_FORMAT = "hh:mm aaaa";
export const DATE_TIME_FORMAT = `${DATE_FORMAT} ${TIME_FORMAT} zzz`;
export const DATE_REANGE_FORMAT = "dd/MM/yyyy";
export const TEN_MINUTES = 600_000;
export const FIFTEEN_MB = 15728640;
export const SUSPENDED = "SUSPENDED";
export const RCT_STANDARD = "RCT STANDARD";
export const MISSING_PERMISSIONS = (
  <div>
    Insufficient permissions.
    <br />
    <NAME_EMAIL>.
  </div>
);
export const DEFAULT_BASKET_ID = "f4cf07b2-04ba-4ee9-94b3-d1f860fe8974";
export const SERVER_PAGINATION_LIMIT = Number(process.env.NEXT_PUBLIC_ADMIN_SERVER_PAGINATION_LIMIT);
export const NON_RCT_TYPES = [
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.REHABILITATION_DEFAULT,
  BookType.AGED_DEFAULT,
];
export const COMPLIANCE_PERMIT = "Compliance Permit";
