import { CounterpartyRole } from "@rubiconcarbon/shared-types";

export const CounterpartyRoleToLabel = {
  [CounterpartyRole.CUSTOMER]: "Customer",
  [CounterpartyRole.DEVELOPER]: "Developer",
  [CounterpartyRole.INTRODUCING_BROKER]: "Introducing Broker",
  [CounterpartyRole.BROKER]: "Broker",
  [CounterpartyRole.CONSULTANT]: "Consultant",
  [CounterpartyRole.CLEARING_HOUSE]: "Clearing House",
  [CounterpartyRole.INTERNAL]: "Internal",
  [CounterpartyRole.OTHER]: "Other",
};

export const CounterpartyRoleOptions = [
  {
    value: CounterpartyRole.CUSTOMER,
    label: CounterpartyRoleToLabel[CounterpartyRole.CUSTOMER],
  },
  {
    value: CounterpartyRole.DEVELOPER,
    label: CounterpartyRoleToLabel[CounterpartyRole.DEVELOPER],
  },
  {
    value: CounterpartyRole.INTRODUCING_BROKER,
    label: CounterpartyRoleToLabel[CounterpartyRole.INTRODUCING_BROKER],
  },
  {
    value: CounterpartyRole.BROKER,
    label: CounterpartyRoleToLabel[CounterpartyRole.BROKER],
  },
  {
    value: CounterpartyRole.CONSULTANT,
    label: CounterpartyRoleToLabel[CounterpartyRole.CONSULTANT],
  },
  {
    value: CounterpartyRole.CLEARING_HOUSE,
    label: CounterpartyRoleToLabel[CounterpartyRole.CLEARING_HOUSE],
  },
  {
    value: CounterpartyRole.INTERNAL,
    label: CounterpartyRoleToLabel[CounterpartyRole.INTERNAL],
  },
  {
    value: CounterpartyRole.OTHER,
    label: CounterpartyRoleToLabel[CounterpartyRole.OTHER],
  },
];
