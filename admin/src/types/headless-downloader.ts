import { Nullable } from "@rubiconcarbon/frontend-shared";
import { MouseEvent } from "react";

export type DownloadResult = {
  status: "success" | "error";
  data?: Blob;
  reason?: any;
};

export type HeadlessDownloaderStateEntry = {
  progresses: Record<string, boolean>;
  results: Record<string, DownloadResult>;
  apiError: any;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export type FileUploadHandlers = Record<string, Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>>;
