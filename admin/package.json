{"name": "rubiconcarbon-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:webpack": "next dev", "build": "NODE_OPTIONS='--max_old_space_size=8192' next build", "build:turbo": "NODE_OPTIONS='--max_old_space_size=8192' next build --turbo", "build:local": "env-cmd -f .env.deploy.local next build --turbo", "build:dev": "env-cmd -f .env.deploy.dev next build", "build:testing": "env-cmd -f .env.deploy.testing next build", "build:staging": "env-cmd -f .env.deploy.staging next build", "build:sandbox": "env-cmd -f .env.deploy.sandbox next build", "build:prod": "env-cmd -f .env.deploy.prod next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "type-check:single": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck --isolatedModules", "type-check:tiny": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.minimal.json", "type-check:basic": "NODE_OPTIONS='--max_old_space_size=2048' tsc --noEmit --skipLibCheck -p tsconfig.basic.json", "type-check:constants": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.constants.json", "type-check:utils": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.utils.json", "type-check:models": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.models.json", "type-check:hooks": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.hooks.json", "type-check:providers": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.providers.json", "type-check:components": "NODE_OPTIONS='--max_old_space_size=2048' tsc --noEmit --skipLibCheck -p tsconfig.components.json", "type-check:mappers": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.mappers.json", "type-check:services": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.services.json", "type-check:types": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.types.json", "type-check:app": "NODE_OPTIONS='--max_old_space_size=3072' tsc --noEmit --skipLibCheck -p tsconfig.app.json", "type-check:components-base": "find src/components -maxdepth 1 -name '*.ts' -o -name '*.tsx' | head -10 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:components-ui": "find src/components/ui -name '*.ts' -o -name '*.tsx' | head -15 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:components-forms": "find src/components/forms -name '*.ts' -o -name '*.tsx' | head -15 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:components-charts": "find src/components/charts -name '*.ts' -o -name '*.tsx' | head -15 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:app-root": "find src/app -maxdepth 1 -name '*.ts' -o -name '*.tsx' | head -10 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:app-api": "find src/app/api -name '*.ts' -o -name '*.tsx' | head -15 | xargs -r NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck", "type-check:level1": "NODE_OPTIONS='--max_old_space_size=1024' tsc --noEmit --skipLibCheck -p tsconfig.level1.json", "type-check:level2": "NODE_OPTIONS='--max_old_space_size=2048' tsc --noEmit --skipLibCheck -p tsconfig.level2.json", "type-check:level3": "NODE_OPTIONS='--max_old_space_size=3072' tsc --noEmit --skipLibCheck -p tsconfig.level3.json", "find-problem-files": "node scripts/find-problem-files.js", "check-file-size": "find src -name '*.ts' -o -name '*.tsx' | xargs wc -l | sort -n", "type-check": "NODE_OPTIONS='--max_old_space_size=6144' tsc --noEmit -p tsconfig.typecheck.json", "type-check:watch": "NODE_OPTIONS='--max_old_space_size=4096' tsc --noEmit --watch -p tsconfig.typecheck.json", "type-check:fast": "NODE_OPTIONS='--max_old_space_size=4096' tsc --noEmit --skipLibCheck -p tsconfig.typecheck.json", "clean": "rm -rf .next", "clean:tsc": "rm -rf .tsbuildinfo* .tsbuildinfo-typecheck", "clean:all": "yarn clean && yarn clean:tsc", "dev:clean": "yarn clean && yarn dev", "fix-workflow": "echo 'Run: yarn find-problem-files && yarn type-check:tiny'"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@hcorta/react-echarts": "^2.0.0", "@hookform/resolvers": "^2.9.11", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.137", "@mui/material": "^5.11.8", "@mui/x-date-pickers": "^6.5.0", "@next/font": "13.3.2", "@opentelemetry/api": "^1.7.0", "@opentelemetry/api-logs": "^0.52.1", "@opentelemetry/auto-instrumentations-web": "^0.40.0", "@opentelemetry/context-zone": "^1.20.0", "@opentelemetry/exporter-logs-otlp-http": "^0.52.1", "@opentelemetry/exporter-trace-otlp-http": "^0.52.1", "@opentelemetry/instrumentation-document-load": "^0.39.0", "@opentelemetry/sdk-logs": "^0.52.1", "@opentelemetry/sdk-trace-web": "^1.20.0", "@react-oauth/google": "^0.7.0", "@rubiconcarbon/frontend-shared": "0.0.142", "@rubiconcarbon/shared-types": "^1.3.4", "@superset-ui/embedded-sdk": "^0.1.2", "@types/papaparse": "^5.3.14", "axios": "^1.3.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookies-next": "^5.1.0", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.7", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "fast-equals": "^5.0.1", "filefy": "^0.1.11", "flagged": "^2.0.9", "framer-motion": "^11.11.7", "jotai": "^2.8.4", "jotai-scope": "^0.6.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "material-react-table": "^1.14.0", "notistack": "3.0.1", "papaparse": "^5.4.1", "react-echarts-core": "^1.0.2", "react-error-boundary": "^3.1.4", "react-fast-marquee": "^1.6.4", "react-hook-form": "^7.56.4", "react-idle-timer": "^5.7.2", "react-number-format": "^5.1.4", "react-remark": "^2.1.0", "react-use": "^17.4.0", "reflect-metadata": "^0.2.1", "server-only": "^0.0.1", "sql-formatter": "^15.3.2", "swr": "^2.0.3", "victory": "36.6.2", "yup": "^1.0.2"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.2", "@types/lodash": "^4.14.191", "@types/node": "18.13.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "env-cmd": "^10.1.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react-hooks": "4.6.0", "next": "^15.3.1", "prettier": "^3.2.4", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.58.0", "typescript": "^5.8.3", "uuid": "^9.0.0"}}