{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "emitDecoratorMetadata": true,
    "strictPropertyInitialization": false,
    "experimentalDecorators": true,
    "downlevelIteration": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@app/*": ["./src/app/*"],
      "@components/*": ["./src/components/*"],
      "@constants/*": ["./src/constants/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@models/*": ["./src/models/*"],
      "@providers/*": ["./src/providers/*"],
      "@utils/*": ["./src/utils/*"],
      "@assets/*": ["./public/*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ],
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "next-env.d.ts",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    ".next"
  ]
}
