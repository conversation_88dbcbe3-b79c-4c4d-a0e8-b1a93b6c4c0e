#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Finding problematic TypeScript files...\n');

// Helper function to get file size and complexity
function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').length;
    const size = Buffer.byteLength(content, 'utf8');
    
    // Count potential complexity indicators
    const imports = (content.match(/^import /gm) || []).length;
    const exports = (content.match(/^export /gm) || []).length;
    const interfaces = (content.match(/interface /gm) || []).length;
    const types = (content.match(/type /gm) || []).length;
    const generics = (content.match(/</g) || []).length;
    
    return {
      path: filePath,
      lines,
      size,
      imports,
      exports,
      interfaces,
      types,
      generics,
      complexity: imports + exports + interfaces + types + (generics * 0.5)
    };
  } catch (error) {
    return null;
  }
}

// Find all TypeScript files
function findTSFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findTSFiles(fullPath, files);
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Main analysis
try {
  const srcDir = path.join(process.cwd(), 'src');
  if (!fs.existsSync(srcDir)) {
    console.log('❌ src directory not found');
    process.exit(1);
  }
  
  console.log('📁 Analyzing files in src/...');
  const tsFiles = findTSFiles(srcDir);
  console.log(`Found ${tsFiles.length} TypeScript files\n`);
  
  // Analyze all files
  const analyses = tsFiles
    .map(analyzeFile)
    .filter(Boolean)
    .sort((a, b) => b.complexity - a.complexity);
  
  // Report findings
  console.log('📊 ANALYSIS RESULTS\n');
  
  // Largest files by lines
  console.log('🔴 LARGEST FILES (by lines):');
  const largestFiles = [...analyses].sort((a, b) => b.lines - a.lines).slice(0, 10);
  largestFiles.forEach((file, i) => {
    console.log(`${i + 1}. ${file.path.replace(process.cwd(), '.')} (${file.lines} lines)`);
  });
  
  console.log('\n🔴 MOST COMPLEX FILES (likely to cause type issues):');
  const complexFiles = analyses.slice(0, 10);
  complexFiles.forEach((file, i) => {
    console.log(`${i + 1}. ${file.path.replace(process.cwd(), '.')} (complexity: ${file.complexity.toFixed(1)})`);
    console.log(`   Lines: ${file.lines}, Imports: ${file.imports}, Types: ${file.types + file.interfaces}`);
  });
  
  // Files with lots of generics (type complexity)
  console.log('\n🔴 FILES WITH COMPLEX TYPES (many generics):');
  const genericFiles = [...analyses].sort((a, b) => b.generics - a.generics).slice(0, 5);
  genericFiles.forEach((file, i) => {
    console.log(`${i + 1}. ${file.path.replace(process.cwd(), '.')} (${file.generics} generic uses)`);
  });
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. Start with smallest files first');
  console.log('2. Fix files in src/constants and src/utils first');
  console.log('3. Consider temporarily simplifying the most complex files');
  console.log('4. Check for circular dependencies in the largest files');
  
  // Generate a focused check list
  const simpleFiles = analyses
    .filter(f => f.lines < 100 && f.complexity < 20)
    .slice(0, 20);
    
  if (simpleFiles.length > 0) {
    console.log('\n✅ SIMPLE FILES (good starting points):');
    simpleFiles.forEach((file, i) => {
      if (i < 5) {
        console.log(`${i + 1}. ${file.path.replace(process.cwd(), '.')}`);
      }
    });
  }
  
  console.log('\n🚀 NEXT STEPS:');
  console.log('1. yarn type-check:constants');
  console.log('2. yarn type-check:utils');
  console.log('3. yarn type-check:models');
  console.log('4. Fix any issues found, then move to next level');
  
} catch (error) {
  console.error('❌ Error during analysis:', error.message);
  process.exit(1);
}