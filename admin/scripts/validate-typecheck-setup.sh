#!/bin/bash

# TypeScript Configuration Validation Script
# Run this to validate your new type checking setup

echo "🔍 Validating TypeScript Configuration Setup..."
echo ""

# Check if files exist
echo "📁 Checking required files..."
if [ -f "tsconfig.typecheck.json" ]; then
    echo "✅ tsconfig.typecheck.json exists"
else
    echo "❌ tsconfig.typecheck.json missing - create this file first"
    exit 1
fi

if [ -f "next-env.d.ts" ]; then
    echo "✅ next-env.d.ts exists"
else
    echo "❌ next-env.d.ts missing - this should be auto-generated by Next.js"
fi

if [ -f "tsconfig.json" ]; then
    echo "✅ tsconfig.json exists"
else
    echo "❌ tsconfig.json missing"
    exit 1
fi

echo ""

# Test TypeScript compilation
echo "🧪 Testing TypeScript configuration..."

# Test 1: Basic compilation check
echo "  → Testing basic compilation..."
if npx tsc --noEmit -p tsconfig.typecheck.json --skipLibCheck > /dev/null 2>&1; then
    echo "✅ Basic compilation test passed"
else
    echo "❌ Basic compilation test failed"
    echo "Run: yarn type-check:fast to see detailed errors"
fi

# Test 2: Check if path aliases resolve
echo "  → Testing path alias resolution..."
if npx tsc --showConfig -p tsconfig.typecheck.json | grep -q "baseUrl"; then
    echo "✅ Path aliases configured"
else
    echo "❌ Path aliases not configured properly"
fi

# Test 3: Check module resolution
echo "  → Testing module resolution..."
MODULE_RESOLUTION=$(npx tsc --showConfig -p tsconfig.typecheck.json | grep -o '"moduleResolution": "[^"]*"' | cut -d'"' -f4)
if [ "$MODULE_RESOLUTION" = "bundler" ]; then
    echo "✅ Module resolution set to 'bundler' (Next.js compatible)"
else
    echo "⚠️  Module resolution is '$MODULE_RESOLUTION' - should be 'bundler' for best Next.js compatibility"
fi

echo ""

# Performance test
echo "⚡ Performance test..."
echo "  → Running fast type check on a sample file..."
START_TIME=$(date +%s)
if find src -name "*.tsx" -o -name "*.ts" | head -1 | xargs -I {} npx tsc --noEmit -p tsconfig.typecheck.json --skipLibCheck {} > /dev/null 2>&1; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    echo "✅ Sample file check completed in ${DURATION}s"
else
    echo "❌ Sample file check failed"
fi

echo ""

# Memory check
echo "💾 Memory configuration check..."
NODE_VERSION=$(node --version)
echo "  → Node.js version: $NODE_VERSION"

# Check available memory
if command -v free > /dev/null 2>&1; then
    TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    echo "  → System memory: ${TOTAL_MEM}GB"
    if [ "$TOTAL_MEM" -lt 8 ]; then
        echo "⚠️  Consider reducing memory limits in package.json scripts"
    else
        echo "✅ Sufficient memory for configured limits"
    fi
elif command -v vm_stat > /dev/null 2>&1; then
    # macOS
    echo "  → macOS system detected"
    echo "✅ Memory configuration should work on macOS"
fi

echo ""

# Final recommendations
echo "🎯 Next Steps:"
echo "1. Run 'yarn type-check:fast' to do a quick scan"
echo "2. Run 'yarn type-check:watch' for real-time type checking"
echo "3. Use domain-specific scripts like 'yarn type-check:components'"
echo "4. Fix issues incrementally, then run 'yarn build'"

echo ""
echo "🚀 Setup validation complete!"