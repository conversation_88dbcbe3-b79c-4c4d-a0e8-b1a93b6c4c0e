# Rubicon Admin Upgrade Plan: Next.js 13 (Pages Router) to Next.js 15 (App Router)



This document outlines a comprehensive, incremental approach to upgrading the rubicon-admin module from Next.js 13 with Pages Router to Next.js 15 with App Router. The plan is designed to allow for continuous validation and testing throughout the migration process.



## Table of Contents



1. [Prerequisites and Environment Setup](#1-prerequisites-and-environment-setup)

2. [Initial Setup for App Router](#2-initial-setup-for-app-router)

3. [Core Infrastructure Migration](#3-core-infrastructure-migration)

4. [Authentication and Provider Migration](#4-authentication-and-provider-migration)

5. [Layout and Navigation Migration](#5-layout-and-navigation-migration)

6. [Page Migration Strategy](#6-page-migration-strategy)

7. [Data Fetching Migration](#7-data-fetching-migration)

8. [API Routes Migration](#8-api-routes-migration)

9. [Testing and Validation](#9-testing-and-validation)

10. [Code Quality Improvements](#10-code-quality-improvements)

11. [Cleanup and Finalization](#11-cleanup-and-finalization)

12. [Rollback Plan](#12-rollback-plan)



## 1. Prerequisites and Environment Setup ✅



### 1.1 Update Node.js Version

- Ensure Node.js version is at least v18.17 (Next.js 15 requirement)

- Update CI/CD pipelines to use the correct Node.js version



### 1.2 Create a Backup Branch

```bash

git  checkout  -b  backup/next-13-pages-router

git  push  origin  backup/next-13-pages-router

```



### 1.3 Create a Development Branch

```bash

git  checkout  -b  feature/next-15-app-router-migration

```



### 1.4 Update Dependencies

```bash

cd  admin

npm  install  next@latest  react@latest  react-dom@latest

npm  install  -D  eslint-config-next@latest

```



### 1.5 Update ESLint Configuration

- Update ESLint configuration to support the new Next.js version

- Restart ESLint server in your IDE if needed



### 1.6 Validation Step

- Run the application to ensure it still works with the updated dependencies

- Fix any immediate issues that arise from the dependency updates



```bash

cd  admin

npm  run  dev

```



## 2. Initial Setup for App Router ✅



### 2.1 Create App Directory Structure

```bash

mkdir  -p  admin/src/app

```



### 2.2 Configure Next.js for App Router

Update `next.config.js` to enable the App Router:



```javascript

/** @type  {import('next').NextConfig} */

const  nextConfig = {

// existing config...



// Enable App Router

experimental: {

appDir:  true,

// Keep existing experimental options

esmExternals:  "loose",

},



// other config...

};



module.exports = nextConfig;

```



### 2.3 Create a Basic Root Layout

Create a minimal root layout file to test the App Router setup:



```tsx

// admin/src/app/layout.tsx

import { Metadata } from  'next'

import  '@/styles/globals.scss'



export  const  metadata: Metadata = {

title:  'Rubicon Admin',

description:  'Rubicon Carbon Admin Portal',

}



export  default  function  RootLayout({

children,

}: {

children: React.ReactNode

}) {

return (

<html  lang="en">

<body>{children}</body>

</html>

)

}

```



### 2.4 Create a Simple Test Page

Create a simple page to verify the App Router is working:



```tsx

// admin/src/app/test/page.tsx

export  default  function  TestPage() {

return (

<div>

<h1>App Router Test Page</h1>

<p>This is a test page for the App Router migration.</p>

</div>

)

}

```



### 2.5 Validation Step

- Run the application and navigate to `/test` to verify the App Router is working

- The Pages Router should still handle all other routes

- Fix any issues before proceeding



## 3. Core Infrastructure Migration ✅



### 3.1 Create Client and Server Utilities

Create utility files to help with the migration:



```tsx

// admin/src/utils/app-router/client-utils.tsx

'use client'



import { usePathname, useRouter, useSearchParams } from  'next/navigation'

import { useCallback } from  'react'



// Legacy router compatibility layer

export  function  useLegacyCompatRouter() {

const  router = useRouter()

const  pathname = usePathname()

const  searchParams = useSearchParams()



// Create a compatibility layer for the old router

return {

...router,

pathname,

query:  Object.fromEntries(searchParams.entries()),

asPath:  pathname + (searchParams.toString() ? `?${searchParams.toString()}` : ''),

route:  pathname,

isReady:  true,

}

}

```



### 3.2 Create Provider Wrapper Components

Create client components for each provider from the current `_app.tsx` file:



```tsx

// admin/src/app/providers.tsx

'use client'



import { GoogleOAuthProvider } from  "@react-oauth/google"

import { TelemetryProvider } from  "@/providers/telemetry"

import { LoggingProvider } from  "@/providers/logging"

import { AuthProvider } from  "@/providers/auth-provider"

import { StoreProvider } from  "@/providers/store-provider"

import { StyledEngineProvider } from  "@mui/material/styles"

import  ThemeProvider  from  "@/providers/theme-provider"

import  SnackbarProvider  from  "@/providers/themed-snackbar-provider"

import { ErrorBoundary } from  "react-error-boundary"

import { AxiosProvider } from  "@/providers/axios-provider"

import { LocalizationProvider } from  "@mui/x-date-pickers"

import { AdapterDayjs } from  "@mui/x-date-pickers/AdapterDayjs"

import  FeaturesProvider  from  "@/providers/features-provider"

import { NavigationMenuProvider } from  "@/providers/navigation-menu-provider"

import  InactivityChecker  from  "@/components/inactivity-checker/inactivity-checker"

import  ErrorBoundaryPage  from  "@/components/error/error-boundary-page"



export  function  Providers({ children }: { children: React.ReactNode }) {

const  errorFallback = (props: any) =>  <ErrorBoundaryPage  {...props}  />



return (

<GoogleOAuthProvider  clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ''}>

<TelemetryProvider  />

<LoggingProvider  />

<AuthProvider>

<InactivityChecker>

<StoreProvider>

<StyledEngineProvider  injectFirst>

<ThemeProvider>

<SnackbarProvider>

<ErrorBoundary  FallbackComponent={errorFallback}>

<AxiosProvider>

<LocalizationProvider  dateAdapter={AdapterDayjs}>

<FeaturesProvider  extraFlags={[]}>

<NavigationMenuProvider>

{children}

</NavigationMenuProvider>

</FeaturesProvider>

</LocalizationProvider>

</AxiosProvider>

</ErrorBoundary>

</SnackbarProvider>

</ThemeProvider>

</StyledEngineProvider>

</StoreProvider>

</InactivityChecker>

</AuthProvider>

</GoogleOAuthProvider>

)

}

```



### 3.3 Validation Step

- Ensure the provider components compile without errors

- Test the compatibility utilities with a simple component



## 4. Authentication and Provider Migration ✅



### 4.1 Update Auth Provider for App Router

Create an updated version of the Auth Provider that works with App Router:



```tsx

// admin/src/providers/auth-provider-app.tsx

'use client'



import { createContext, ReactNode, useContext, useEffect, useMemo, useState } from  "react"

import { GoogleLogin, CredentialResponse } from  "@react-oauth/google"

import  jwtDecode  from  "jwt-decode"

import  Image  from  "next/image"

import { useRouter } from  "next/navigation"

import  logo  from  "@assets/images/RC-Admin-Logo.svg"

import { Permission, PermissionEnum } from  "@rubiconcarbon/shared-types"



// Keep the same User class and interfaces as the original auth-provider.tsx



export  function  AuthProviderApp({ children }: { children: ReactNode }): JSX.Element {

// Copy the implementation from the original auth-provider.tsx

// Update router references to use next/navigation



// Rest of the implementation remains the same

}



export  default  function  useAuthApp(): UserContextType {

return  useContext(AuthContext)

}

```



### 4.2 Update Other Providers as Needed

Review and update other providers that might need adjustments for App Router:



-  `navigation-menu-provider.tsx` - Update to use App Router hooks

-  `axios-provider.tsx` - Ensure it works with App Router

-  `store-provider.tsx` - Ensure it works with App Router



### 4.3 Validation Step

- Create a test component that uses the updated providers

- Verify authentication flow works correctly

- Test navigation and state management



## 5. Layout and Navigation Migration ✅



### 5.1 Create App Router Layout Components

Convert the existing layout components to work with App Router:



```tsx

// admin/src/app/layout.tsx

import { Metadata } from  'next'

import { Providers } from  './providers'

import  '@/styles/globals.scss'



export  const  metadata: Metadata = {

title:  'Rubicon Admin',

description:  'Rubicon Carbon Admin Portal',

}



export  default  function  RootLayout({

children,

}: {

children: React.ReactNode

}) {

return (

<html  lang="en">

<body>

<Providers>

{children}

</Providers>

</body>

</html>

)

}

```



### 5.2 Create Main Layout Component

Create a client component for the main layout:



```tsx

// admin/src/app/main-layout.tsx

'use client'



import  Layout  from  "@/components/layout/layout"

import  BreadcrumbsLayout  from  "@/components/layout/breadcrumbs-layout"

import  ChatbotIcon  from  "@/components/chatbot/chatbot-icon"



export  default  function  MainLayout({

children,

}: {

children: React.ReactNode

}) {

return (

<>

<Layout>

<BreadcrumbsLayout>

{children}

</BreadcrumbsLayout>

</Layout>

<ChatbotIcon  />

</>

)

}

```



### 5.3 Update Navigation Components

Update the navigation components to work with App Router:



- Update `SideBar.tsx` to use App Router navigation

- Update `NavMenuList.tsx` to use App Router navigation

- Update `BreadcrumbsLayout.tsx` to use App Router navigation



### 5.4 Create Route Group Layouts

Create layouts for the main route groups based on the existing routing structure:



```tsx

// admin/src/app/(main)/layout.tsx

import  MainLayout  from  "../main-layout"



export  default  function  MainRouteGroupLayout({

children,

}: {

children: React.ReactNode

}) {

return  <MainLayout>{children}</MainLayout>

}

```



This layout will be applied to all routes within the (main) route group, which will include most of the application's routes. The route group pattern with parentheses allows us to group routes without affecting the URL structure.



### 5.5 Validation Step

- Create a test page within the dashboard layout

- Verify navigation works correctly

- Test breadcrumbs and sidebar functionality



## 6. Page Migration Strategy ✅ (Resumed)



### 6.1 Define Migration Approach

For each page in the Pages Router, follow these steps:



1. Create a client component from the existing page component

2. Create a server component page in the App Router

3. Move data fetching to the server component

4. Import the client component into the server component



### 6.2 Simple Page Migration Example

For a simple page like the home page:



```tsx

// Step 1: Create a client component from the existing page

// admin/src/app/(main)/home-client.tsx

'use client'



import  NestedMenuSection  from  "@/components/nested-menu-section/nested-menu-section"

import  useNavigationMenu  from  "@/providers/navigation-menu-provider"



export  default  function  HomeClient() {

const { permissibleMenus } = useNavigationMenu()



return (

<NestedMenuSection  menus={permissibleMenus.slice(1)}  />

)

}



// Step 2: Create a server component page

// admin/src/app/(main)/page.tsx

import  HomeClient  from  "./home-client"



export  default  function  HomePage() {

return  <HomeClient  />

}

```



### 6.3 Dynamic Route Migration Example

For pages with dynamic routes:



```tsx

// Step 1: Create a client component

// admin/src/app/(main)/[level-one-nav-page]/[level-two-nav-page]/page-client.tsx

'use client'



import  NestedMenuSection  from  "@/components/nested-menu-section/nested-menu-section"

import  useNavigationMenu  from  "@/providers/navigation-menu-provider"

import { useLegacyCompatRouter } from  "@/utils/app-router/client-utils"



export  default  function  LevelTwoNavPageClient() {

const { asPath } = useLegacyCompatRouter()

const { permissibleMenus, getPermissableMenu } = useNavigationMenu()

const  menu = getPermissableMenu(asPath, permissibleMenus, "equals")



return (

<NestedMenuSection  menus={[menu]}  />

)

}



// Step 2: Create a server component page

// admin/src/app/(main)/[level-one-nav-page]/[level-two-nav-page]/page.tsx

import  LevelTwoNavPageClient  from  "./page-client"

import  Page  from  "@/components/layout/containers/page"



export  default  function  LevelTwoNavPage({

params

}: {

params: { 'level-one-nav-page': string, 'level-two-nav-page': string }

}) {

return (

<Page>

<LevelTwoNavPageClient  />

</Page>

)

}

```



### 6.4 Authorization Wrapper for Server Components ✅

Create an authorization wrapper for server components:



```tsx
// admin/src/app/authorize-server.tsx
'use client'

/**
 * AuthorizeServer
 *
 * This component is specifically designed to be used in Server Components that need authorization checks.
 * It wraps the client-side Authorize component to make it usable in server components.
 */

import { PropsWithChildren } from "react";
import { Authorize } from "@/services/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

export function AuthorizeServer({
  children,
  permissions,
  partiallyAuthorize = false,
  unauthorizedPage,
}: PropsWithChildren<{
  permissions: PermissionEnum[];
  partiallyAuthorize?: boolean;
  unauthorizedPage?: JSX.Element;
}>) {
  return (
    <Authorize
      permissions={permissions}
      partiallyAuthorize={partiallyAuthorize}
      unauthorizedPage={unauthorizedPage}
    >
      {children}
    </Authorize>
  );
}

export default AuthorizeServer;
```



### 6.5 Replace Maybe Component with Frontend-Shared Implementation



1. Identify all usages of the custom Maybe component:



```tsx

// Before

import  Maybe  from  "@/components/maybe/maybe"



// After

import { Maybe } from  "@rubiconcarbon/frontend-shared"

```



2. Update any component that uses the Maybe component



3. Remove the custom Maybe component implementation once all usages have been migrated



### 6.6 Validation Step ✅

- Migrate a few simple pages first

- Test each migrated page thoroughly

- Ensure authorization works correctly

- Verify navigation between migrated and non-migrated pages

- Verify that the frontend-shared components work correctly

### 6.7 Migrated Pages ✅

The following pages have been successfully migrated to the App Router:

1. Sandbox Page
2. Market Intelligence Page (including Add and Edit pages)
3. Level One Navigation Page
4. Level Two Navigation Page
5. Market Data Page
6. My Positions Page
7. Bid Ask Page
8. Projects Pages:
   - Projects Page
   - Create Project Page
   - Create Vintage Page
   - Project Item Page
   - Project Image Management Page
9. Books Pages:
   - Books Page
   - Book Detail Page
   - Transfer Assets Page
10. Portfolios Pages:
   - Portfolios Page
   - Portfolio Detail Page
   - New Custom Portfolio Page
   - Portfolio Composition Page
11. Portfolio Sandbox Pages:
   - Portfolio Sandbox Page
   - Edit Portfolio Sandbox Page
12. Quotes Page:
   - Quotes Page with server-side data fetching
13. Organizations Pages:
   - Organizations Page
   - Organization Detail Page
   - Create Portal Organization Page
   - Create Trade Counterparty Page
   - Edit Portal Organization Page
   - Edit Trade Counterparty Page
   - Upload Document Page
14. Transactions Pages:
   - Transactions Page
   - Transaction Detail Page
   - New Transaction Page
   - Edit Transaction Page
15. Retirements Pages:
   - Retirements Page
   - Retirement Detail Page
   - Retirement Composition Page
   - New Retirement Page
   - New Transfer Page
   - Retirement Calculator Page
   - Upload Retirement Certification Page
16. Data Management Page
17. Notification Settings Page
18. Forward Delivery Page
19. Marketing Agreements Page
20. Reconciliation Page
21. Reserves Page
22. User Activity Page
23. Users Pages:
   - Users Page
   - Create User Page
   - User Permissions Page



## 7. Data Fetching Migration ✅

### 7.0 Remove Legacy Compatibility Layer ✅

Before proceeding with data fetching migration, we've removed the legacy compatibility layer:

1. Removed `useLegacyCompatRouter` and replaced it with direct App Router hooks:
   - `useAppPathname` instead of `router.pathname`
   - `useAppRouter` instead of the legacy router object
   - `WithSearchParams` component for safely using search parameters

2. Created proper router hooks in `src/hooks/router-hooks.tsx`:
   - `useAppPathname()` - A wrapper for `usePathname()`
   - `useAppRouter()` - A wrapper for `useRouter()`
   - `WithSearchParams` - A component that safely uses `useSearchParams()` with a Suspense boundary

3. Updated all components to use the new hooks directly:
   - Updated breadcrumbs-layout.tsx
   - Updated breadcrumb-provider.tsx
   - Updated navigation-menu-provider.tsx
   - Updated auth-provider.tsx
   - Updated back-button.tsx
   - Updated useNavigation.ts
   - Updated useNavigationInterrupter.ts
   - Updated home-client.tsx

### 7.1 Create Server-Side Data Fetching Utilities ✅

Created comprehensive utilities for server-side data fetching:

```tsx
// admin/src/app/lib/server.ts
import "server-only";

import { cookies } from "next/headers";
import { getCookie } from "cookies-next/server";
import { GenericRecord, Undefinable } from "@rubiconcarbon/frontend-shared";
import { HttpStatusMapper } from "@/constants/http";
import { environment } from "@/environment";
import { AUTH_COOKIE_NAME } from "@/constants/auth-constants";

export async function getAuthToken(): Promise<string | null> {
  const cookie = await getCookie(AUTH_COOKIE_NAME, { cookies });

  try {
    const decoded = decodeURIComponent(cookie ?? "");
    const data: { token?: string } = JSON.parse(decoded);

    return data?.token ?? null;
  } catch (err) {
    return null;
  }
}

export async function serverFetch<T>(
  url: string,
  options: RequestInit = {},
  cache: RequestCache = "no-cache",
): Promise<Undefinable<T>> {
  const token = await getAuthToken();

  if (!token) return undefined;

  const res = await fetch(url, {
    ...options,
    cache,
    headers: {
      ...options.headers,
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!res.ok) {
    throw new Error(HttpStatusMapper[res.status] || `Failed to fetch: ${res.status}`);
  }

  return await res.json();
}

export async function baseApiRequest<T>(
  path: string,
  options: RequestInit = {},
  cache: RequestCache = "no-cache",
): Promise<Undefinable<T>> {
  return serverFetch<T>(`${environment.server.basApi.ssr}/${path}`, options, cache);
}

export async function reportingApiRequest<T>(
  path: string,
  options: RequestInit = {},
  cache: RequestCache = "no-cache",
): Promise<Undefinable<T>> {
  return serverFetch<T>(`${environment.server.reportingApi.ssr}/${path}`, options, cache);
}
```

### 7.2 Implement Cookie-Based Authentication ✅

Created a robust cookie-based authentication system that works for both client and server contexts:

```tsx
// admin/src/app/auth/cookie/route.ts
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { AUTH_COOKIE_NAME } from "@/constants/auth-constants";

export async function POST(req: NextRequest) {
  const data = await req.json();

  const res = NextResponse.json({ success: true });

  res.cookies.set({
    name: AUTH_COOKIE_NAME,
    value: encodeURIComponent(JSON.stringify(data)),
    httpOnly: true,
    secure: true,
    path: "/",
    sameSite: "strict",
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });

  return res;
}

export async function GET() {
  const cookie = (await cookies()).get(AUTH_COOKIE_NAME)?.value;

  try {
    const decoded = decodeURIComponent(cookie ?? "");
    const data = JSON.parse(decoded);

    if (!data) {
      return NextResponse.json({ status: 401 });
    }

    return NextResponse.json({ ...data });
  } catch {
    return NextResponse.json({ status: 401 });
  }
}

export async function DELETE() {
  const res = NextResponse.json({ success: true });

  res.cookies.set({
    name: AUTH_COOKIE_NAME,
    value: "",
    httpOnly: true,
    secure: true,
    path: "/",
    sameSite: "strict",
    maxAge: 0,
  });

  return res;
}
```



### 7.3 Create Error Handling Wrapper for Server Components ✅

Created a robust error handling wrapper for server components that can return either data or an error component:

```tsx
// admin/src/app/data-server.tsx
import "server-only";

import { GenericRecord, Undefinable } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import ErrorComponent from "@/app/error";
import { generateQueryParams, serverFetch } from "./lib/server";

/**
 * Wrapper for API calls that handles errors by returning an error component
 *
 * @param fetchFn The fetch function to execute
 * @returns The data or an error component
 */
export const withErrorHandling = async <T,>(
  fetchFn: () => Promise<Undefinable<T>>,
): Promise<Undefinable<T> | JSX.Element> => {
  try {
    return await fetchFn();
  } catch (error: any) {
    // Return the error component directly
    return <ErrorComponent error={error} />;
  }
};

/**
 * Generic function to fetch data with pagination and filtering
 */
export const fetchPaginatedData = async <T, Q extends GenericRecord>(
  endpoint: string,
  queryParams: Q = {} as Q,
): Promise<Undefinable<T>> => {
  return serverFetch<T>(
    `${endpoint}?${generateQueryParams({
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      ...queryParams,
    })}`,
  );
};

/**
 * Generic function to fetch a single item by ID
 */
export const fetchItemById = async <T, Q extends GenericRecord>(
  endpoint: string,
  id: string,
  queryParams: Q = {} as Q,
): Promise<Undefinable<T>> => {
  return serverFetch<T>(`${endpoint}/${id}?${generateQueryParams(queryParams)}`);
};
```

### 7.4 Example Page with Server-Side Data Fetching ✅

Implemented server-side data fetching in server components:

```tsx
// admin/src/app/notification-settings/page.tsx
import { AuthorizeServer } from "@/app/authorize-server";
import { NotificationSubscriptionsBulkResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { withErrorHandling } from "@/app/data-server";
import NotificationSettings from "./components";
import { NOTIFICATION_SUBSCRIPTIONS_API_URL } from "./constants/notification";
import { isValidElement } from "react";
import { baseApiRequest } from "../lib/server";

/**
 * Notification Settings Page
 *
 * This is a server component that fetches initial notification subscription data
 * and passes it to the client component
 */
export default async function NotificationSettingsPage() {
  // Fetch notification subscriptions data on the server
  const subscriptionsData = await withErrorHandling(async () =>
    baseApiRequest<NotificationSubscriptionsBulkResponse>(NOTIFICATION_SUBSCRIPTIONS_API_URL),
  );

  // Check if the result is a server error
  if (isValidElement(subscriptionsData)) return subscriptionsData;

  return (
    <AuthorizeServer permissions={[PermissionEnum.LOGIN]}>
      <NotificationSettings initialData={subscriptionsData as NotificationSubscriptionsBulkResponse} />
    </AuthorizeServer>
  );
}
```

### 7.5 Replace Custom Data Fetching Hooks with Frontend-Shared Library

Replace the custom data fetching hooks with the implementations from the frontend-shared library:

1. Identify all usages of custom hooks:
   - `useRequest`
   - `useTriggerRequest`
   - `useBatchRequest`
   - `useDataPolling`

2. Replace them with the equivalent hooks from the frontend-shared library:

```tsx
// Before
import useRequest from "@/utils/hooks/useRequest"

// After
import { useRequest } from "@rubiconcarbon/frontend-shared"
```

3. Update any component that uses these hooks to work with the frontend-shared implementations

4. Remove the custom hook implementations once all usages have been migrated

### 7.6 Validation Step

- Test pages with data fetching

- Verify data is correctly fetched and displayed

- Test error handling and loading states



## 8. API Routes Migration 🔄 (Current Focus)



### 8.1 Create Route Handlers

Migrate API routes to Route Handlers:



```tsx

// admin/src/app/api/hello/route.ts

import { NextResponse } from  'next/server'



export  async  function  GET() {

return  NextResponse.json({ name:  'John Doe' })

}

```



### 8.2 Migrate Authentication API

Migrate the authentication API:



```tsx

// admin/src/app/api/auth/route.ts

import { NextRequest, NextResponse } from  'next/server'



export  async  function  POST(request: NextRequest) {

const  body = await  request.json()



const  response = await  fetch(process.env.NEXT_PUBLIC_AUTH_URL || '/api/auth', {

method:  'POST',

headers: { 'Content-Type':  'application/json' },

body:  JSON.stringify(body),

})



if (!response.ok) {

return  NextResponse.json(

{ error:  'Authentication failed' },

{ status:  response.status }

)

}



const  data = await  response.json()

return  NextResponse.json(data)

}

```



### 8.3 Validation Step

- Test API routes

- Verify authentication works correctly

- Test error handling



## 9. Testing and Validation



### 9.1 Create Test Plan

Create a comprehensive test plan covering:

- Authentication

- Navigation

- Data fetching

- Authorization

- UI components

- Error handling



### 9.2 Automated Testing

Update and run automated tests:

```bash

cd  admin

npm  run  test

```



### 9.3 Manual Testing

Perform manual testing of critical paths:

- Login flow

- Navigation between sections

- Data display and interaction

- Form submissions

- Error scenarios



### 9.4 Performance Testing

Compare performance between Pages Router and App Router:

- Page load times

- Time to interactive

- Memory usage

- Network requests



### 9.5 Cross-Browser Testing

Test in multiple browsers:

- Chrome

- Firefox

- Safari

- Edge



## 10. Code Quality Improvements



### 10.1 Re-enable ESLint and TypeScript Type Checking

Update Next.js configuration to re-enable linting and type checking:



```javascript

/** @type  {import('next').NextConfig} */

const  nextConfig = {

reactStrictMode:  true,

basePath:  process.env.RUBICON_ADMIN_BASE_URL || "",

output:  "standalone",



// Re-enable ESLint and TypeScript type checking

// eslint: {

// ignoreDuringBuilds: true,

// },

//

// typescript: {

// ignoreBuildErrors: true,

// },



// Keep other configuration options

};



module.exports = nextConfig;

```



### 10.2 Restore ESLint Configuration

Restore the ESLint configuration to enforce code quality standards:



```javascript

module.exports = {

root:  true,

env: {

node:  true,

browser:  true,

es6:  true,

},

ignorePatterns: [".eslintrc.js", ".next", "node_modules", "out"],

plugins: ["@typescript-eslint/eslint-plugin", "import"],

extends: [

"next/core-web-vitals",

"plugin:@typescript-eslint/recommended",

"plugin:prettier/recommended",

],

settings: {

next: {

rootDir:  ".",

},

"import/resolver": {

typescript: {},

node: {

extensions: [".js", ".jsx", ".ts", ".tsx"],

},

},

},

rules: {

"@typescript-eslint/interface-name-prefix":  "off",

"@typescript-eslint/explicit-function-return-type":  "error",

"@typescript-eslint/explicit-module-boundary-types":  "off",

"@typescript-eslint/no-explicit-any":  "off",

"import/no-unresolved":  2,

"import/no-unused-modules":  2,

"import/no-commonjs":  2,

"import/extensions": [2, { js:  "always", ts:  "never" }],

"import/no-namespace":  2,

"import/no-useless-path-segments":  2,

"comma-dangle": [2, "always-multiline"],

"no-console": ["error", { allow: ["warn", "error"] }],

},

};

```



### 10.3 Fix ESLint Errors

Address the over 200 ESLint errors identified during the migration:



1. Fix double negation issues (`!!` operator)

2. Fix unsafe optional chaining usage

3. Fix React hooks dependency arrays

4. Fix case declarations in switch statements

5. Fix prettier formatting issues



### 10.4 Fix TypeScript Type Errors

Address all TypeScript type errors:



1. Fix null/undefined handling

2. Add proper type annotations

3. Fix incompatible type assignments

4. Update component return types



### 10.5 Update SASS Files

Fix SASS deprecation warnings:



1. Update nested declarations in layout.module.scss

2. Replace deprecated @import rules with @use and @forward



## 11. Cleanup and Finalization



### 11.1 Update Configuration

Finalize Next.js configuration:



```javascript

/** @type  {import('next').NextConfig} */

const  nextConfig = {

reactStrictMode:  true,

basePath:  process.env.RUBICON_ADMIN_BASE_URL || "",

output:  "standalone",



// Remove appDir once migration is complete

// experimental: {

// appDir: true,

// },



// Keep other configuration options

};



module.exports = nextConfig;

```



### 11.2 Remove Pages Directory

Once all pages have been migrated and tested:

```bash

# Backup pages directory first

mkdir  -p  admin/src/_pages_backup

cp  -r  admin/src/pages/*  admin/src/_pages_backup/



# Remove pages directory

rm  -rf  admin/src/pages

```



### 11.3 Remove Compatibility Layers

Once all components have been migrated to use the App Router natively:



1. Remove the `useLegacyCompatRouter` utility and update all components to use the native App Router hooks directly:



```tsx

// Before (with compatibility layer)

const { asPath } = useLegacyCompatRouter()



// After (native App Router)

const  pathname = usePathname()

const  searchParams = useSearchParams()

```



2. Remove any other temporary compatibility utilities created during the migration



### 11.4 Update Documentation

Update internal documentation:

- Update README.md with new architecture information

- Document new patterns and best practices

- Update development guides



### 11.5 Final Validation

Perform a final round of testing:

- Verify all features work correctly

- Check for any regressions

- Validate performance metrics



## 12. Rollback Plan



### 12.1 Rollback Strategy

In case of critical issues:



1. Revert to the backup branch:

```bash

git  checkout  backup/next-13-pages-router

```



2. Redeploy the application with the previous version



### 12.2 Partial Rollback

For issues with specific features:



1. Keep the App Router for working features

2. Temporarily restore specific pages from the Pages Router

3. Update routing to direct to the correct implementation



## Revised Incremental Migration Approach



This plan is designed to be implemented incrementally, allowing for continuous validation and testing. The revised approach is:



1. Set up the App Router alongside the Pages Router ✅

2. Migrate core infrastructure and providers ✅

3. Create the layout structure ✅

4. **Temporarily skip page migration to focus on other aspects** ⏩

5. Implement data fetching utilities and replace custom implementations ✅

6. Migrate API routes ✅

7. Improve code quality (ESLint, TypeScript, SASS) 🔄

8. Return to page migration, migrating one page at a time 🔄
   - Created detailed page migration analysis (PAGE_MIGRATION_ANALYSIS.md)
   - Migrated Sandbox page to App Router ✅
   - Migrated Market Intelligence pages to App Router ✅ (including Add and Edit pages)
   - Moving original Pages Router files to backup directories

9. Perform thorough testing

10. Remove the Pages Router once everything is working correctly



By following this incremental approach, you can:

- Maintain a functioning application throughout the migration

- Test each component as it's migrated

- Roll back specific changes if issues arise

- Learn and adapt your approach as you progress