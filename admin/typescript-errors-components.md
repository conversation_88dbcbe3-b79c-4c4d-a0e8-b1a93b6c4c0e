# TypeScript Errors in Components Folder

## Summary
Total: 8 errors in 3 files

### Error Categories:

1. **Type Property Access Issues (6 errors)** - Most errors
   - Missing properties on union types
   - Destructuring from potentially undefined objects

2. **Generic Type Mismatch (1 error)**
   - Missing `smartSetValue` property in form hook

3. **Hook Interface Mismatch (1 error)**
   - Navigation interceptor hook interface issues

## Detailed Errors by File:

### src/components/ui/generic-table/hooks/use-generic-table-row-actions.ts (4 errors)
**Lines 54 & 160:** Property access on union type
```typescript
// Error: Property 'action' does not exist on type 'void | GenericTableOnSubmitReturn<M> | {}'
// Error: Property 'row' does not exist on type 'void | GenericTableOnSubmitReturn<M> | {}'
const { action, row: submittedRow } = (row ? await onRowSubmit?.(row) : {}) ?? {};
```

**Issue:** Destructuring from a union type that includes `void` and `{}`
**Solution:** Add type guards or proper type narrowing

### src/components/ui/generic-table/index.tsx (1 error)
**Line 240:** Generic type mismatch
```typescript
// Error: Property 'smartSetValue' is missing in type 'UseFormReturn<Values<M>>'
useForm,
```

**Issue:** Expected enhanced form hook but got standard form hook
**Solution:** Use the correct enhanced form hook or add missing property

### src/components/ui/uploader/components/UploaderWidget.tsx (3 errors)
**Lines 121-124:** Hook interface mismatch
```typescript
// Error: Property 'show' does not exist on type 'NavigationInterceptorResult'
// Error: Property 'setShow' does not exist on type 'NavigationInterceptorResult'  
// Error: Type 'boolean' has no properties in common with type 'NavigationInterceptorProps'
show: showNavigationModal,
setShow: setShowNavigationModal,
} = useNavigationInterrupter(uploading);
```

**Issue:** Hook interface doesn't match expected properties
**Solution:** Check hook implementation and fix interface mismatch

## Priority Fix Order:
1. Fix navigation interceptor hook interface (3 errors)
2. Fix generic table type guards (4 errors)
3. Fix form hook type mismatch (1 error)

## Next Steps:
1. Investigate hook implementations and interfaces
2. Add proper type guards for union types
3. Ensure correct hook usage patterns
4. Re-run type check to verify fixes
