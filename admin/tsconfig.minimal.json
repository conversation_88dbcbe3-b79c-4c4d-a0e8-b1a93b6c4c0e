{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "lib": ["dom", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@app/*": ["./src/app/*"], "@components/*": ["./src/components/*"], "@constants/*": ["./src/constants/*"], "@hooks/*": ["./src/hooks/*"], "@models/*": ["./src/models/*"], "@providers/*": ["./src/providers/*"], "@utils/*": ["./src/utils/*"], "@assets/*": ["./public/*"]}}, "include": ["next-env.d.ts", "src/constants/**/*.ts"], "exclude": ["node_modules", ".next", "**/*.test.ts", "**/*.test.tsx"]}